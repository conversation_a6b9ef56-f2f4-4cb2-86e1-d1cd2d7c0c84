import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/pages/auth/LoginPage.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/pages/dashboard/DashboardPage.vue'),
    meta: {
      title: '仪表板',
      requiresAuth: true
    }
  },
  {
    path: '/categories',
    name: 'Categories',
    component: () => import('@/pages/categories/CategoriesPage.vue'),
    meta: {
      title: '分类管理',
      requiresAuth: true
    }
  },
  {
    path: '/novels',
    name: 'Novels',
    component: () => import('@/pages/novels/NovelsPage.vue'),
    meta: {
      title: '小说管理',
      requiresAuth: true
    }
  },
  {
    path: '/novels/:id',
    name: 'NovelDetail',
    component: () => import('@/pages/novels/NovelDetailPage.vue'),
    meta: {
      title: '小说详情',
      requiresAuth: true
    }
  },
  {
    path: '/novels/:novelId/records',
    name: 'Records',
    component: () => import('@/pages/records/RecordsPage.vue'),
    meta: {
      title: '分析记录',
      requiresAuth: true
    }
  },
  {
    path: '/novels/:novelId/records/new',
    name: 'NewRecord',
    component: () => import('@/pages/records/RecordEditPage.vue'),
    meta: {
      title: '新建记录',
      requiresAuth: true
    }
  },
  {
    path: '/records/:id',
    name: 'RecordDetail',
    component: () => import('@/pages/records/RecordDetailPage.vue'),
    meta: {
      title: '记录详情',
      requiresAuth: true
    }
  },
  {
    path: '/records/:id/edit',
    name: 'EditRecord',
    component: () => import('@/pages/records/RecordEditPage.vue'),
    meta: {
      title: '编辑记录',
      requiresAuth: true
    }
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/pages/search/SearchPage.vue'),
    meta: {
      title: '搜索',
      requiresAuth: true
    }
  },
  {
    path: '/test/markdown-editor',
    name: 'MarkdownEditorTest',
    component: () => import('@/pages/test/MarkdownEditorTest.vue'),
    meta: {
      title: 'Markdown编辑器测试',
      requiresAuth: false
    }
  },
  {
    path: '/test/records-management',
    name: 'RecordsManagementTest',
    component: () => import('@/pages/test/RecordsManagementTest.vue'),
    meta: {
      title: '记录管理功能测试',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/pages/common/NotFoundPage.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - HaoWriter`
  }

  // 检查认证状态
  const token = localStorage.getItem('haowriter_token')
  const requiresAuth = to.meta?.requiresAuth !== false

  if (requiresAuth && !token) {
    // 需要认证但未登录，跳转到登录页
    next('/login')
  } else if (to.path === '/login' && token) {
    // 已登录用户访问登录页，跳转到仪表板
    next('/dashboard')
  } else {
    next()
  }
})

export default router
