<template>
  <div class="record-statistics">
    <el-card>
      <template #header>
        <div class="statistics-header">
          <span>记录统计</span>
          <el-button :icon="Refresh" size="small" @click="refreshStatistics">
            刷新
          </el-button>
        </div>
      </template>

      <div v-loading="loading" class="statistics-content">
        <div v-if="statistics" class="stats-grid">
          <!-- 总体统计 -->
          <div class="stat-card total-card">
            <div class="stat-icon">
              <el-icon size="24" color="#409EFF"><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalRecords }}</div>
              <div class="stat-label">总记录数</div>
            </div>
          </div>

          <!-- 分析类型分布 -->
          <div class="stat-section">
            <h4 class="section-title">分析类型分布</h4>
            <div class="type-stats">
              <div
                v-for="item in statistics.typeStats"
                :key="item.type"
                class="type-item"
              >
                <div class="type-info">
                  <el-tag 
                    :type="getAnalysisTypeColor(item.type)" 
                    size="small"
                  >
                    {{ getAnalysisTypeLabel(item.type) }}
                  </el-tag>
                  <span class="type-count">{{ item.count }}</span>
                </div>
                <div class="type-progress">
                  <el-progress
                    :percentage="getPercentage(item.count, statistics.totalRecords)"
                    :show-text="false"
                    :stroke-width="6"
                    :color="getProgressColor(item.type)"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 重要程度分布 -->
          <div class="stat-section">
            <h4 class="section-title">重要程度分布</h4>
            <div class="importance-stats">
              <div
                v-for="item in statistics.importanceStats"
                :key="item.level"
                class="importance-item"
              >
                <div class="importance-info">
                  <el-rate
                    :model-value="item.level"
                    :max="4"
                    disabled
                    size="small"
                  />
                  <span class="importance-count">{{ item.count }}</span>
                </div>
                <div class="importance-progress">
                  <el-progress
                    :percentage="getPercentage(item.count, statistics.totalRecords)"
                    :show-text="false"
                    :stroke-width="6"
                    :color="getImportanceColor(item.level)"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 最近趋势 -->
          <div class="stat-section">
            <h4 class="section-title">最近7天趋势</h4>
            <div class="trend-stats">
              <div v-if="statistics.recentStats.length === 0" class="no-data">
                暂无数据
              </div>
              <div v-else class="trend-chart">
                <div
                  v-for="item in statistics.recentStats"
                  :key="item.date"
                  class="trend-item"
                >
                  <div class="trend-date">{{ formatTrendDate(item.date) }}</div>
                  <div class="trend-bar">
                    <div
                      class="trend-fill"
                      :style="{ 
                        height: `${getTrendHeight(item.count)}px`,
                        backgroundColor: '#409EFF'
                      }"
                    />
                  </div>
                  <div class="trend-count">{{ item.count }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="!loading" class="no-statistics">
          <el-empty description="暂无统计数据" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Refresh } from '@element-plus/icons-vue'
import { useRecordsStore } from '@/stores/records'
import type { RecordStatistics } from '@/stores/records'
import dayjs from 'dayjs'

interface Props {
  novelId?: number
}

const props = defineProps<Props>()

const recordsStore = useRecordsStore()

// 状态
const loading = ref(false)
const statistics = ref<RecordStatistics | null>(null)

// 计算属性
const maxTrendCount = computed(() => {
  if (!statistics.value?.recentStats.length) return 1
  return Math.max(...statistics.value.recentStats.map(item => item.count))
})

// 生命周期
onMounted(() => {
  loadStatistics()
})

// 方法实现
const loadStatistics = async () => {
  loading.value = true
  try {
    statistics.value = await recordsStore.fetchStatistics(props.novelId)
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

const refreshStatistics = () => {
  loadStatistics()
}

// 工具函数
const getAnalysisTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    character: '人物分析',
    plot: '情节分析',
    theme: '主题分析',
    writing: '写作技巧',
    worldview: '世界观设定',
    other: '其他'
  }
  return typeMap[type] || '未分类'
}

const getAnalysisTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    character: 'success',
    plot: 'primary',
    theme: 'warning',
    writing: 'info',
    worldview: 'danger',
    other: ''
  }
  return colorMap[type] || ''
}

const getProgressColor = (type: string) => {
  const colorMap: Record<string, string> = {
    character: '#67C23A',
    plot: '#409EFF',
    theme: '#E6A23C',
    writing: '#909399',
    worldview: '#F56C6C',
    other: '#C0C4CC'
  }
  return colorMap[type] || '#C0C4CC'
}

const getImportanceColor = (level: number) => {
  const colorMap: Record<number, string> = {
    1: '#C0C4CC',
    2: '#E6A23C',
    3: '#409EFF',
    4: '#F56C6C'
  }
  return colorMap[level] || '#C0C4CC'
}

const getPercentage = (count: number, total: number) => {
  return total > 0 ? Math.round((count / total) * 100) : 0
}

const getTrendHeight = (count: number) => {
  const maxHeight = 60
  const minHeight = 4
  if (maxTrendCount.value === 0) return minHeight
  return Math.max(minHeight, (count / maxTrendCount.value) * maxHeight)
}

const formatTrendDate = (date: string) => {
  return dayjs(date).format('MM/DD')
}
</script>

<style scoped>
.record-statistics {
  width: 100%;
}

.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistics-content {
  min-height: 200px;
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  border-radius: 8px;
  color: white;
}

.stat-icon {
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.stat-section {
  padding: 16px 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.type-stats,
.importance-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-item,
.importance-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.type-info,
.importance-info {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.type-count,
.importance-count {
  font-weight: 500;
  color: #606266;
}

.type-progress,
.importance-progress {
  flex: 1;
}

.trend-stats {
  padding: 16px 0;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.trend-chart {
  display: flex;
  align-items: end;
  gap: 8px;
  height: 100px;
  padding: 0 8px;
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.trend-date {
  font-size: 12px;
  color: #909399;
}

.trend-bar {
  height: 60px;
  width: 20px;
  background-color: #F5F7FA;
  border-radius: 2px;
  display: flex;
  align-items: end;
  overflow: hidden;
}

.trend-fill {
  width: 100%;
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
}

.trend-count {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
}

.no-statistics {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stat-card {
    padding: 16px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .type-item,
  .importance-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .type-info,
  .importance-info {
    justify-content: space-between;
  }
  
  .trend-chart {
    gap: 4px;
  }
  
  .trend-bar {
    width: 16px;
  }
}
</style>
