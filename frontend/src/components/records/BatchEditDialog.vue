<template>
  <el-dialog
    v-model="visible"
    title="批量编辑记录"
    width="600px"
    :before-close="handleClose"
  >
    <div class="batch-edit-content">
      <el-alert
        :title="`将对 ${selectedCount} 条记录进行批量编辑`"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      />

      <el-form
        ref="formRef"
        :model="form"
        label-width="100px"
      >
        <el-form-item label="分析类型">
          <el-select
            v-model="form.analysisType"
            placeholder="选择分析类型（不选择则不修改）"
            clearable
            style="width: 100%"
          >
            <el-option label="人物分析" value="character" />
            <el-option label="情节分析" value="plot" />
            <el-option label="主题分析" value="theme" />
            <el-option label="写作技巧" value="writing" />
            <el-option label="世界观设定" value="worldview" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="重要程度">
          <el-radio-group v-model="form.importanceLevel">
            <el-radio :label="undefined">不修改</el-radio>
            <el-radio :label="1">低</el-radio>
            <el-radio :label="2">中</el-radio>
            <el-radio :label="3">高</el-radio>
            <el-radio :label="4">关键</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="标签操作">
          <el-radio-group v-model="tagOperation">
            <el-radio label="none">不修改标签</el-radio>
            <el-radio label="add">添加标签</el-radio>
            <el-radio label="remove">移除标签</el-radio>
            <el-radio label="replace">替换标签</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="tagOperation !== 'none'" label="标签">
          <div class="tags-input">
            <el-tag
              v-for="tag in form.tags"
              :key="tag"
              closable
              @close="removeTag(tag)"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="tagInputVisible"
              ref="tagInputRef"
              v-model="tagInputValue"
              size="small"
              style="width: 120px;"
              @keyup.enter="addTag"
              @blur="addTag"
            />
            <el-button
              v-else
              size="small"
              @click="showTagInput"
            >
              + 添加标签
            </el-button>
          </div>
          <div class="tag-operation-hint">
            <span v-if="tagOperation === 'add'">将添加这些标签到选中的记录</span>
            <span v-else-if="tagOperation === 'remove'">将从选中的记录中移除这些标签</span>
            <span v-else-if="tagOperation === 'replace'">将用这些标签替换选中记录的所有标签</span>
          </div>
        </el-form-item>
      </el-form>

      <div class="preview-section">
        <h4>修改预览</h4>
        <div class="preview-content">
          <div v-if="!hasChanges" class="no-changes">
            未选择任何修改项
          </div>
          <div v-else class="changes-list">
            <div v-if="form.analysisType" class="change-item">
              <strong>分析类型：</strong>修改为 {{ getAnalysisTypeLabel(form.analysisType) }}
            </div>
            <div v-if="form.importanceLevel" class="change-item">
              <strong>重要程度：</strong>修改为 {{ getImportanceLevelLabel(form.importanceLevel) }}
            </div>
            <div v-if="tagOperation !== 'none' && form.tags.length > 0" class="change-item">
              <strong>标签操作：</strong>{{ getTagOperationLabel() }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="!hasChanges"
          @click="handleConfirm"
        >
          确认修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

interface Props {
  modelValue: boolean
  selectedCount: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', updates: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()
const tagInputRef = ref()

// 状态
const loading = ref(false)
const tagInputVisible = ref(false)
const tagInputValue = ref('')
const tagOperation = ref<'none' | 'add' | 'remove' | 'replace'>('none')

// 表单数据
const form = ref({
  analysisType: '',
  importanceLevel: undefined as number | undefined,
  tags: [] as string[]
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const hasChanges = computed(() => {
  return !!(
    form.value.analysisType ||
    form.value.importanceLevel ||
    (tagOperation.value !== 'none' && form.value.tags.length > 0)
  )
})

// 监听对话框关闭，重置表单
watch(visible, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})

// 方法实现
const resetForm = () => {
  form.value = {
    analysisType: '',
    importanceLevel: undefined,
    tags: []
  }
  tagOperation.value = 'none'
  tagInputVisible.value = false
  tagInputValue.value = ''
}

const handleClose = () => {
  visible.value = false
}

const handleConfirm = async () => {
  if (!hasChanges.value) {
    ElMessage.warning('请至少选择一项要修改的内容')
    return
  }

  loading.value = true
  try {
    const updates: any = {}
    
    if (form.value.analysisType) {
      updates.analysisType = form.value.analysisType
    }
    
    if (form.value.importanceLevel) {
      updates.importanceLevel = form.value.importanceLevel
    }
    
    if (tagOperation.value !== 'none' && form.value.tags.length > 0) {
      updates.tagOperation = tagOperation.value
      updates.tags = form.value.tags
    }
    
    emit('confirm', updates)
    visible.value = false
  } catch (error) {
    console.error('批量编辑失败:', error)
  } finally {
    loading.value = false
  }
}

// 标签管理
const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

const addTag = () => {
  const tag = tagInputValue.value.trim()
  if (tag && !form.value.tags.includes(tag)) {
    form.value.tags.push(tag)
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

const removeTag = (tag: string) => {
  const index = form.value.tags.indexOf(tag)
  if (index > -1) {
    form.value.tags.splice(index, 1)
  }
}

// 工具函数
const getAnalysisTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    character: '人物分析',
    plot: '情节分析',
    theme: '主题分析',
    writing: '写作技巧',
    worldview: '世界观设定',
    other: '其他'
  }
  return typeMap[type] || type
}

const getImportanceLevelLabel = (level: number) => {
  const levelMap: Record<number, string> = {
    1: '低',
    2: '中',
    3: '高',
    4: '关键'
  }
  return levelMap[level] || level.toString()
}

const getTagOperationLabel = () => {
  const operationMap: Record<string, string> = {
    add: `添加标签：${form.value.tags.join(', ')}`,
    remove: `移除标签：${form.value.tags.join(', ')}`,
    replace: `替换为标签：${form.value.tags.join(', ')}`
  }
  return operationMap[tagOperation.value] || ''
}
</script>

<style scoped>
.batch-edit-content {
  max-height: 600px;
  overflow-y: auto;
}

.tags-input {
  width: 100%;
}

.tag-operation-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.preview-section {
  margin-top: 24px;
  padding: 16px;
  background-color: var(--el-bg-color-page);
  border-radius: 6px;
}

.preview-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #303133;
}

.no-changes {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.changes-list {
  font-size: 14px;
}

.change-item {
  margin-bottom: 8px;
  color: #606266;
}

.change-item:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
