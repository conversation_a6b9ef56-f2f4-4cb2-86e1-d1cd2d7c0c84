import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { recordsApi } from '@/api/records'
import type { AnalysisRecord } from '@/types/models'
import type { PaginationData, RecordQuery } from '@/types/api'

export interface RecordFilters {
  search?: string
  novelId?: number
  analysisType?: string
  importanceLevel?: number
  tags?: string[]
  startDate?: string
  endDate?: string
}

export interface RecordStatistics {
  totalRecords: number
  typeStats: Array<{ type: string; count: number }>
  importanceStats: Array<{ level: number; count: number }>
  recentStats: Array<{ date: string; count: number }>
}

export const useRecordsStore = defineStore('records', () => {
  // 状态
  const records = ref<AnalysisRecord[]>([])
  const currentRecord = ref<AnalysisRecord | null>(null)
  const loading = ref(false)
  const statistics = ref<RecordStatistics | null>(null)
  
  // 分页状态
  const pagination = ref({
    current: 1,
    size: 20,
    total: 0
  })
  
  // 筛选状态
  const filters = ref<RecordFilters>({})
  
  // 排序状态
  const sort = ref({
    field: 'createdAt',
    order: 'desc' as 'asc' | 'desc'
  })
  
  // 选中的记录
  const selectedRecords = ref<number[]>([])
  
  // 计算属性
  const hasRecords = computed(() => records.value.length > 0)
  const selectedCount = computed(() => selectedRecords.value.length)
  const isAllSelected = computed(() => 
    records.value.length > 0 && selectedRecords.value.length === records.value.length
  )
  
  // 获取记录列表
  const fetchRecords = async (params?: Partial<RecordQuery>) => {
    loading.value = true
    try {
      const queryParams = {
        page: pagination.value.current,
        size: pagination.value.size,
        sort: sort.value.field,
        order: sort.value.order,
        ...filters.value,
        ...params
      }
      
      const response = await recordsApi.getList(queryParams)
      const data = response.data as PaginationData<AnalysisRecord>
      
      records.value = data.items
      pagination.value = {
        current: data.page,
        size: data.size,
        total: data.total
      }
    } catch (error) {
      console.error('获取记录列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 获取记录详情
  const fetchRecord = async (id: number) => {
    loading.value = true
    try {
      const response = await recordsApi.getById(id)
      currentRecord.value = response.data
      return response.data
    } catch (error) {
      console.error('获取记录详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 创建记录
  const createRecord = async (novelId: number, data: any) => {
    try {
      const response = await recordsApi.create(novelId, data)
      // 刷新列表
      await fetchRecords()
      return response.data
    } catch (error) {
      console.error('创建记录失败:', error)
      throw error
    }
  }
  
  // 更新记录
  const updateRecord = async (id: number, data: any) => {
    try {
      const response = await recordsApi.update(id, data)
      
      // 更新本地记录
      const index = records.value.findIndex(r => r.id === id)
      if (index !== -1) {
        records.value[index] = response.data
      }
      
      // 如果是当前记录，也更新
      if (currentRecord.value?.id === id) {
        currentRecord.value = response.data
      }
      
      return response.data
    } catch (error) {
      console.error('更新记录失败:', error)
      throw error
    }
  }
  
  // 删除记录
  const deleteRecord = async (id: number) => {
    try {
      await recordsApi.delete(id)
      
      // 从本地列表中移除
      const index = records.value.findIndex(r => r.id === id)
      if (index !== -1) {
        records.value.splice(index, 1)
        pagination.value.total--
      }
      
      // 从选中列表中移除
      const selectedIndex = selectedRecords.value.indexOf(id)
      if (selectedIndex !== -1) {
        selectedRecords.value.splice(selectedIndex, 1)
      }
    } catch (error) {
      console.error('删除记录失败:', error)
      throw error
    }
  }
  
  // 批量删除记录
  const batchDeleteRecords = async (ids: number[]) => {
    try {
      await recordsApi.batchDelete({ recordIds: ids })
      
      // 从本地列表中移除
      records.value = records.value.filter(r => !ids.includes(r.id))
      pagination.value.total -= ids.length
      
      // 清空选中列表
      selectedRecords.value = []
    } catch (error) {
      console.error('批量删除记录失败:', error)
      throw error
    }
  }
  
  // 批量更新记录
  const batchUpdateRecords = async (ids: number[], updates: any) => {
    try {
      await recordsApi.batchUpdate({ recordIds: ids, updates })
      
      // 刷新列表以获取最新数据
      await fetchRecords()
      
      // 清空选中列表
      selectedRecords.value = []
    } catch (error) {
      console.error('批量更新记录失败:', error)
      throw error
    }
  }
  
  // 获取统计信息
  const fetchStatistics = async (novelId?: number) => {
    try {
      const response = await recordsApi.getStatistics(novelId)
      statistics.value = response.data
      return response.data
    } catch (error) {
      console.error('获取统计信息失败:', error)
      throw error
    }
  }
  
  // 设置筛选条件
  const setFilters = (newFilters: Partial<RecordFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
    pagination.value.current = 1 // 重置到第一页
  }
  
  // 清空筛选条件
  const clearFilters = () => {
    filters.value = {}
    pagination.value.current = 1
  }
  
  // 设置排序
  const setSort = (field: string, order: 'asc' | 'desc') => {
    sort.value = { field, order }
    pagination.value.current = 1
  }
  
  // 设置分页
  const setPagination = (page: number, size?: number) => {
    pagination.value.current = page
    if (size) {
      pagination.value.size = size
    }
  }
  
  // 选择记录
  const toggleSelectRecord = (id: number) => {
    const index = selectedRecords.value.indexOf(id)
    if (index === -1) {
      selectedRecords.value.push(id)
    } else {
      selectedRecords.value.splice(index, 1)
    }
  }
  
  // 全选/取消全选
  const toggleSelectAll = () => {
    if (isAllSelected.value) {
      selectedRecords.value = []
    } else {
      selectedRecords.value = records.value.map(r => r.id)
    }
  }
  
  // 清空选中
  const clearSelection = () => {
    selectedRecords.value = []
  }
  
  // 重置状态
  const reset = () => {
    records.value = []
    currentRecord.value = null
    loading.value = false
    statistics.value = null
    pagination.value = { current: 1, size: 20, total: 0 }
    filters.value = {}
    sort.value = { field: 'createdAt', order: 'desc' }
    selectedRecords.value = []
  }
  
  return {
    // 状态
    records,
    currentRecord,
    loading,
    statistics,
    pagination,
    filters,
    sort,
    selectedRecords,
    
    // 计算属性
    hasRecords,
    selectedCount,
    isAllSelected,
    
    // 方法
    fetchRecords,
    fetchRecord,
    createRecord,
    updateRecord,
    deleteRecord,
    batchDeleteRecords,
    batchUpdateRecords,
    fetchStatistics,
    setFilters,
    clearFilters,
    setSort,
    setPagination,
    toggleSelectRecord,
    toggleSelectAll,
    clearSelection,
    reset
  }
}, {
  persist: {
    key: 'haowriter-records',
    paths: ['filters', 'sort', 'pagination.size']
  }
})
