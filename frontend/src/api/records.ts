import { http } from '@/utils/request'
import type { 
  RecordRequest, 
  RecordQuery,
  BatchDeleteRequest,
  PaginationData,
  ApiResponse 
} from '@/types/api'
import type { AnalysisRecord } from '@/types/models'

export const recordsApi = {
  // 获取所有分析记录列表（支持跨小说查询）
  getList: (params?: RecordQuery): Promise<ApiResponse<PaginationData<AnalysisRecord>>> => {
    return http.get('/records', params)
  },

  // 获取指定小说的分析记录列表
  getListByNovel: (novelId: number, params?: RecordQuery): Promise<ApiResponse<PaginationData<AnalysisRecord>>> => {
    return http.get(`/records/novels/${novelId}`, params)
  },

  // 获取分析记录详情
  getById: (id: number): Promise<ApiResponse<AnalysisRecord>> => {
    return http.get(`/records/${id}`)
  },

  // 创建分析记录
  create: (novelId: number, data: RecordRequest): Promise<ApiResponse<AnalysisRecord>> => {
    return http.post(`/records/novels/${novelId}`, data)
  },

  // 更新分析记录
  update: (id: number, data: Partial<RecordRequest>): Promise<ApiResponse<AnalysisRecord>> => {
    return http.put(`/records/${id}`, data)
  },

  // 删除分析记录
  delete: (id: number): Promise<ApiResponse<null>> => {
    return http.delete(`/records/${id}`)
  },

  // 批量删除分析记录
  batchDelete: (data: BatchDeleteRequest): Promise<ApiResponse<null>> => {
    return http.delete('/records/batch', data)
  },

  // 批量更新分析记录
  batchUpdate: (data: { recordIds: number[]; updates: any }): Promise<ApiResponse<null>> => {
    return http.put('/records/batch', data)
  },

  // 获取记录统计信息
  getStatistics: (novelId?: number): Promise<ApiResponse<any>> => {
    return http.get('/records/statistics', novelId ? { novelId } : {})
  }
}
