<template>
  <div class="markdown-editor-test">
    <div class="page-header">
      <h1 class="page-title">Markdown编辑器测试</h1>
      <p class="page-subtitle">测试Markdown编辑器的各项功能</p>
    </div>

    <el-card>
      <template #header>
        <span>编辑器测试</span>
      </template>
      
      <div class="test-content">
        <MarkdownEditor
          v-model="content"
          :height="500"
          placeholder="请输入Markdown内容进行测试..."
          @save="handleSave"
          @change="handleChange"
          @upload="handleUpload"
        />
      </div>
      
      <div class="test-info">
        <el-divider>测试信息</el-divider>
        <p><strong>内容长度:</strong> {{ content.length }} 字符</p>
        <p><strong>最后更改:</strong> {{ lastChangeTime || '无' }}</p>
        <p><strong>保存次数:</strong> {{ saveCount }}</p>
      </div>
      
      <div class="test-actions">
        <el-button @click="loadSampleContent">加载示例内容</el-button>
        <el-button @click="clearContent">清空内容</el-button>
        <el-button type="primary" @click="handleSave">手动保存</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import MarkdownEditor from '@/components/markdown/MarkdownEditor.vue'

// 状态管理
const content = ref('')
const lastChangeTime = ref('')
const saveCount = ref(0)

// 示例内容
const sampleContent = `# Markdown编辑器测试

## 基础语法测试

### 文本格式
- **粗体文本**
- *斜体文本*
- ~~删除线文本~~
- \`行内代码\`

### 列表
1. 有序列表项1
2. 有序列表项2
   - 嵌套无序列表
   - 另一个嵌套项

### 引用
> 这是一个引用块
> 可以包含多行内容

### 代码块
\`\`\`javascript
function hello() {
  console.log('Hello, World!');
}
\`\`\`

### 表格
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

### 链接和图片
[链接示例](https://example.com)

![图片示例](https://via.placeholder.com/300x200)

---

这是一个分割线上方的内容。

这是一个分割线下方的内容。
`

// 事件处理
const handleSave = () => {
  saveCount.value++
  ElMessage.success(`保存成功！第 ${saveCount.value} 次保存`)
  console.log('保存的内容:', content.value)
}

const handleChange = (value: string) => {
  lastChangeTime.value = new Date().toLocaleTimeString()
  console.log('内容变更:', value.length, '字符')
}

const handleUpload = (files: File[]) => {
  ElMessage.info(`上传 ${files.length} 个文件`)
  console.log('上传文件:', files)
}

const loadSampleContent = () => {
  content.value = sampleContent
  ElMessage.success('示例内容已加载')
}

const clearContent = () => {
  content.value = ''
  ElMessage.success('内容已清空')
}
</script>

<style scoped>
.markdown-editor-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.test-content {
  margin-bottom: 24px;
}

.test-info {
  margin: 24px 0;
}

.test-info p {
  margin: 8px 0;
  color: #606266;
}

.test-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-editor-test {
    padding: 0 8px;
  }
  
  .test-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .page-title {
    font-size: 20px;
  }
}
</style>
