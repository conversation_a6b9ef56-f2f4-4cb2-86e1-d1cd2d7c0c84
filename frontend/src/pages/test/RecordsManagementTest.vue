<template>
  <div class="records-test">
    <div class="page-header">
      <h1 class="page-title">分析记录管理功能测试</h1>
      <p class="page-subtitle">测试记录的创建、查看、编辑、删除和批量操作功能</p>
    </div>

    <el-row :gutter="24">
      <el-col :span="16">
        <el-card>
          <template #header>
            <span>功能测试</span>
          </template>
          
          <div class="test-actions">
            <el-button type="primary" @click="testCreateRecord">
              测试创建记录
            </el-button>
            <el-button @click="testLoadRecords">
              测试加载记录列表
            </el-button>
            <el-button @click="testSearch">
              测试搜索功能
            </el-button>
            <el-button @click="testBatchOperations">
              测试批量操作
            </el-button>
            <el-button @click="testStatistics">
              测试统计功能
            </el-button>
          </div>

          <div class="test-results">
            <h3>测试结果</h3>
            <div v-if="testResults.length === 0" class="no-results">
              暂无测试结果
            </div>
            <div v-else class="results-list">
              <div
                v-for="(result, index) in testResults"
                :key="index"
                class="result-item"
                :class="result.success ? 'success' : 'error'"
              >
                <div class="result-header">
                  <span class="result-title">{{ result.title }}</span>
                  <el-tag :type="result.success ? 'success' : 'danger'" size="small">
                    {{ result.success ? '成功' : '失败' }}
                  </el-tag>
                </div>
                <div class="result-content">
                  <p>{{ result.message }}</p>
                  <div v-if="result.data" class="result-data">
                    <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <RecordStatistics />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRecordsStore } from '@/stores/records'
import RecordStatistics from '@/components/records/RecordStatistics.vue'

const recordsStore = useRecordsStore()

// 测试结果
interface TestResult {
  title: string
  success: boolean
  message: string
  data?: any
}

const testResults = ref<TestResult[]>([])

// 添加测试结果
const addTestResult = (result: TestResult) => {
  testResults.value.unshift(result)
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

// 测试创建记录
const testCreateRecord = async () => {
  try {
    const testData = {
      title: `测试记录 ${Date.now()}`,
      chapterInfo: '第一章',
      originalText: '这是一段测试的原文内容，用于验证Markdown编辑器的功能。\n\n**粗体文本** 和 *斜体文本*',
      commentText: '这是测试的分析评论内容。\n\n## 分析要点\n\n1. 人物性格分析\n2. 情节发展\n3. 写作技巧',
      analysisType: 'character',
      importanceLevel: 3,
      tags: ['测试', '自动生成'],
      pageLocation: 'P123'
    }

    const result = await recordsStore.createRecord(1, testData)
    
    addTestResult({
      title: '创建记录测试',
      success: true,
      message: '成功创建测试记录',
      data: { id: result.id, title: result.title }
    })
    
    ElMessage.success('创建记录测试成功')
  } catch (error) {
    addTestResult({
      title: '创建记录测试',
      success: false,
      message: `创建记录失败: ${error}`,
    })
    
    ElMessage.error('创建记录测试失败')
  }
}

// 测试加载记录列表
const testLoadRecords = async () => {
  try {
    await recordsStore.fetchRecords()
    
    addTestResult({
      title: '加载记录列表测试',
      success: true,
      message: `成功加载 ${recordsStore.records.length} 条记录`,
      data: {
        total: recordsStore.pagination.total,
        current: recordsStore.pagination.current,
        size: recordsStore.pagination.size
      }
    })
    
    ElMessage.success('加载记录列表测试成功')
  } catch (error) {
    addTestResult({
      title: '加载记录列表测试',
      success: false,
      message: `加载记录列表失败: ${error}`,
    })
    
    ElMessage.error('加载记录列表测试失败')
  }
}

// 测试搜索功能
const testSearch = async () => {
  try {
    // 测试关键词搜索
    recordsStore.setFilters({ search: '测试' })
    await recordsStore.fetchRecords()
    
    const searchResults = recordsStore.records.length
    
    // 重置筛选
    recordsStore.clearFilters()
    await recordsStore.fetchRecords()
    
    addTestResult({
      title: '搜索功能测试',
      success: true,
      message: `搜索"测试"关键词找到 ${searchResults} 条记录`,
      data: { searchResults, totalRecords: recordsStore.records.length }
    })
    
    ElMessage.success('搜索功能测试成功')
  } catch (error) {
    addTestResult({
      title: '搜索功能测试',
      success: false,
      message: `搜索功能测试失败: ${error}`,
    })
    
    ElMessage.error('搜索功能测试失败')
  }
}

// 测试批量操作
const testBatchOperations = async () => {
  try {
    // 先加载记录列表
    await recordsStore.fetchRecords()
    
    if (recordsStore.records.length === 0) {
      throw new Error('没有记录可供测试批量操作')
    }
    
    // 选择前两条记录（如果有的话）
    const recordsToSelect = recordsStore.records.slice(0, 2).map(r => r.id)
    recordsToSelect.forEach(id => recordsStore.toggleSelectRecord(id))
    
    // 测试批量更新
    await recordsStore.batchUpdateRecords(recordsToSelect, {
      importanceLevel: 4
    })
    
    addTestResult({
      title: '批量操作测试',
      success: true,
      message: `成功批量更新 ${recordsToSelect.length} 条记录的重要程度`,
      data: { updatedRecords: recordsToSelect.length }
    })
    
    // 清空选择
    recordsStore.clearSelection()
    
    ElMessage.success('批量操作测试成功')
  } catch (error) {
    addTestResult({
      title: '批量操作测试',
      success: false,
      message: `批量操作测试失败: ${error}`,
    })
    
    ElMessage.error('批量操作测试失败')
  }
}

// 测试统计功能
const testStatistics = async () => {
  try {
    const statistics = await recordsStore.fetchStatistics()
    
    addTestResult({
      title: '统计功能测试',
      success: true,
      message: `成功获取统计数据，总记录数: ${statistics.totalRecords}`,
      data: {
        totalRecords: statistics.totalRecords,
        typeStatsCount: statistics.typeStats.length,
        importanceStatsCount: statistics.importanceStats.length,
        recentStatsCount: statistics.recentStats.length
      }
    })
    
    ElMessage.success('统计功能测试成功')
  } catch (error) {
    addTestResult({
      title: '统计功能测试',
      success: false,
      message: `统计功能测试失败: ${error}`,
    })
    
    ElMessage.error('统计功能测试失败')
  }
}
</script>

<style scoped>
.records-test {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.test-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 24px;
}

.test-results h3 {
  margin: 0 0 16px 0;
  color: #303133;
}

.no-results {
  text-align: center;
  color: #909399;
  padding: 40px 20px;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  padding: 16px;
  background-color: var(--el-bg-color);
}

.result-item.success {
  border-color: #67C23A;
  background-color: #F0F9FF;
}

.result-item.error {
  border-color: #F56C6C;
  background-color: #FEF0F0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-title {
  font-weight: 500;
  color: #303133;
}

.result-content p {
  margin: 0 0 8px 0;
  color: #606266;
}

.result-data {
  background-color: #F5F7FA;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
}

.result-data pre {
  margin: 0;
  font-size: 12px;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .records-test {
    padding: 0 8px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .test-actions {
    flex-direction: column;
  }
}
</style>
