<template>
  <div class="records-page">
    <div class="page-header">
      <div class="header-content">
        <div>
          <h1 class="page-title">分析记录</h1>
          <p class="page-subtitle">管理和查看小说分析记录</p>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            :icon="Plus"
            @click="createRecord"
          >
            新建记录
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <el-card class="filter-card">
      <div class="filter-content">
        <el-form :model="filters" inline>
          <el-form-item label="搜索">
            <el-input
              v-model="filters.search"
              placeholder="搜索标题或内容"
              :prefix-icon="Search"
              clearable
              style="width: 240px"
              @keyup.enter="handleSearch"
              @clear="handleSearch"
            />
          </el-form-item>

          <el-form-item label="小说">
            <el-select
              v-model="filters.novelId"
              placeholder="选择小说"
              clearable
              style="width: 200px"
              @change="handleSearch"
            >
              <el-option
                v-for="novel in novelsOptions"
                :key="novel.id"
                :label="novel.title"
                :value="novel.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="分析类型">
            <el-select
              v-model="filters.analysisType"
              placeholder="选择类型"
              clearable
              style="width: 140px"
              @change="handleSearch"
            >
              <el-option label="人物分析" value="character" />
              <el-option label="情节分析" value="plot" />
              <el-option label="主题分析" value="theme" />
              <el-option label="写作技巧" value="writing" />
              <el-option label="世界观设定" value="worldview" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>

          <el-form-item label="重要程度">
            <el-select
              v-model="filters.importanceLevel"
              placeholder="选择程度"
              clearable
              style="width: 120px"
              @change="handleSearch"
            >
              <el-option label="低" :value="1" />
              <el-option label="中" :value="2" />
              <el-option label="高" :value="3" />
              <el-option label="关键" :value="4" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button :icon="Search" @click="handleSearch">搜索</el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 记录列表 -->
    <el-card>
      <template #header>
        <div class="list-header">
          <div class="header-left">
            <el-checkbox
              v-model="selectAll"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            >
              全选
            </el-checkbox>
            <span class="record-count">
              共 {{ pagination.total }} 条记录
              <span v-if="selectedCount > 0">，已选择 {{ selectedCount }} 条</span>
            </span>
          </div>

          <div class="header-right">
            <el-button-group v-if="selectedCount > 0">
              <el-button
                type="warning"
                :icon="Edit"
                @click="showBatchEditDialog"
              >
                批量编辑
              </el-button>
              <el-button
                type="danger"
                :icon="Delete"
                @click="batchDelete"
              >
                批量删除
              </el-button>
            </el-button-group>

            <el-dropdown @command="handleSortCommand">
              <el-button :icon="Sort">
                排序 <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="createdAt-desc">创建时间 ↓</el-dropdown-item>
                  <el-dropdown-item command="createdAt-asc">创建时间 ↑</el-dropdown-item>
                  <el-dropdown-item command="updatedAt-desc">更新时间 ↓</el-dropdown-item>
                  <el-dropdown-item command="updatedAt-asc">更新时间 ↑</el-dropdown-item>
                  <el-dropdown-item command="importanceLevel-desc">重要程度 ↓</el-dropdown-item>
                  <el-dropdown-item command="importanceLevel-asc">重要程度 ↑</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>

      <div v-loading="loading" class="records-list">
        <div v-if="!hasRecords && !loading" class="empty-state">
          <el-empty description="暂无分析记录">
            <el-button type="primary" :icon="Plus" @click="createRecord">
              创建第一条记录
            </el-button>
          </el-empty>
        </div>

        <div v-else class="records-grid">
          <div
            v-for="record in records"
            :key="record.id"
            class="record-card"
            :class="{ selected: selectedRecords.includes(record.id) }"
          >
            <div class="record-header">
              <el-checkbox
                :model-value="selectedRecords.includes(record.id)"
                @change="toggleSelectRecord(record.id)"
              />

              <div class="record-meta">
                <el-tag
                  :type="getAnalysisTypeColor(record.analysisType)"
                  size="small"
                >
                  {{ getAnalysisTypeLabel(record.analysisType) }}
                </el-tag>
                <el-rate
                  :model-value="record.importanceLevel"
                  :max="4"
                  disabled
                  size="small"
                />
              </div>
            </div>

            <div class="record-content" @click="viewRecord(record)">
              <h3 class="record-title">{{ record.title || '无标题' }}</h3>
              <p class="record-novel">{{ record.novel?.title }}</p>
              <p class="record-excerpt">{{ getExcerpt(record.originalText || record.commentText) }}</p>
              <div class="record-footer">
                <span class="record-date">{{ formatDate(record.createdAt) }}</span>
                <span class="record-words">{{ getWordCount(record) }} 字</span>
                <span v-if="record.viewCount > 0" class="record-views">
                  {{ record.viewCount }} 次查看
                </span>
              </div>
            </div>

            <div class="record-actions">
              <el-button size="small" :icon="View" @click="viewRecord(record)">
                查看
              </el-button>
              <el-button size="small" :icon="Edit" @click="editRecord(record)">
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                :icon="Delete"
                @click="deleteRecord(record)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="hasRecords" class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 批量编辑对话框 -->
    <BatchEditDialog
      v-model="batchEditVisible"
      :selected-count="selectedCount"
      @confirm="handleBatchEdit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Delete,
  View,
  Edit,
  Sort,
  ArrowDown
} from '@element-plus/icons-vue'
import { useRecordsStore } from '@/stores/records'
import { useNovelsStore } from '@/stores/novels'
import type { AnalysisRecord } from '@/types/models'
import BatchEditDialog from '@/components/records/BatchEditDialog.vue'
import dayjs from 'dayjs'

const router = useRouter()
const recordsStore = useRecordsStore()
const novelsStore = useNovelsStore()

// 响应式数据
const loading = computed(() => recordsStore.loading)
const records = computed(() => recordsStore.records)
const pagination = computed(() => recordsStore.pagination)
const selectedRecords = computed(() => recordsStore.selectedRecords)
const hasRecords = computed(() => recordsStore.hasRecords)
const selectedCount = computed(() => recordsStore.selectedCount)
const isAllSelected = computed(() => recordsStore.isAllSelected)

// 筛选条件
const filters = reactive({
  search: '',
  novelId: undefined as number | undefined,
  analysisType: '',
  importanceLevel: undefined as number | undefined
})

// 对话框状态
const batchEditVisible = ref(false)

// 小说选项
const novelsOptions = computed(() => novelsStore.novels || [])

// 全选状态
const selectAll = computed({
  get: () => isAllSelected.value,
  set: (value: boolean) => recordsStore.toggleSelectAll()
})

const isIndeterminate = computed(() =>
  selectedCount.value > 0 && !isAllSelected.value
)

// 生命周期
onMounted(async () => {
  // 加载小说列表用于筛选
  if (!novelsStore.novels.length) {
    await novelsStore.fetchNovels()
  }

  // 加载记录列表
  await loadRecords()
})

// 方法实现
const loadRecords = async () => {
  try {
    await recordsStore.fetchRecords()
  } catch (error) {
    ElMessage.error('加载记录列表失败')
  }
}

const handleSearch = async () => {
  recordsStore.setFilters(filters)
  await loadRecords()
}

const resetFilters = async () => {
  Object.assign(filters, {
    search: '',
    novelId: undefined,
    analysisType: '',
    importanceLevel: undefined
  })
  recordsStore.clearFilters()
  await loadRecords()
}

const handleSelectAll = () => {
  recordsStore.toggleSelectAll()
}

const toggleSelectRecord = (id: number) => {
  recordsStore.toggleSelectRecord(id)
}

const handleSortCommand = async (command: string) => {
  const [field, order] = command.split('-')
  recordsStore.setSort(field, order as 'asc' | 'desc')
  await loadRecords()
}

const handleSizeChange = async (size: number) => {
  recordsStore.setPagination(1, size)
  await loadRecords()
}

const handleCurrentChange = async (page: number) => {
  recordsStore.setPagination(page)
  await loadRecords()
}

const createRecord = () => {
  router.push('/novels/new/records/new')
}

const viewRecord = (record: AnalysisRecord) => {
  router.push(`/records/${record.id}`)
}

const editRecord = (record: AnalysisRecord) => {
  router.push(`/records/${record.id}/edit`)
}

const deleteRecord = async (record: AnalysisRecord) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除记录"${record.title || '无标题'}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await recordsStore.deleteRecord(record.id)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const batchDelete = async () => {
  if (selectedCount.value === 0) {
    ElMessage.warning('请先选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCount.value} 条记录吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await recordsStore.batchDeleteRecords([...selectedRecords.value])
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const showBatchEditDialog = () => {
  if (selectedCount.value === 0) {
    ElMessage.warning('请先选择要编辑的记录')
    return
  }
  batchEditVisible.value = true
}

const handleBatchEdit = async (updates: any) => {
  try {
    await recordsStore.batchUpdateRecords([...selectedRecords.value], updates)
    ElMessage.success('批量编辑成功')
  } catch (error) {
    ElMessage.error('批量编辑失败')
  }
}

// 工具函数
const getAnalysisTypeLabel = (type?: string) => {
  const typeMap: Record<string, string> = {
    character: '人物分析',
    plot: '情节分析',
    theme: '主题分析',
    writing: '写作技巧',
    worldview: '世界观设定',
    other: '其他'
  }
  return typeMap[type || ''] || '未分类'
}

const getAnalysisTypeColor = (type?: string) => {
  const colorMap: Record<string, string> = {
    character: 'success',
    plot: 'primary',
    theme: 'warning',
    writing: 'info',
    worldview: 'danger',
    other: ''
  }
  return colorMap[type || ''] || ''
}

const getExcerpt = (text?: string) => {
  if (!text) return '暂无内容'
  return text.length > 100 ? text.substring(0, 100) + '...' : text
}

const getWordCount = (record: AnalysisRecord) => {
  const originalLength = record.originalText?.length || 0
  const commentLength = record.commentText?.length || 0
  return originalLength + commentLength
}

const formatDate = (date: string | Date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}
</script>

<style scoped>
.records-page {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 24px;
}

.filter-content {
  padding: 8px 0;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.record-count {
  color: #606266;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.records-list {
  min-height: 400px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.records-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.record-card {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 16px;
  background-color: var(--el-bg-color);
  transition: all 0.2s;
  cursor: pointer;
}

.record-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-card.selected {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.record-content {
  margin-bottom: 12px;
}

.record-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.record-novel {
  color: #409EFF;
  font-size: 14px;
  margin: 0 0 8px 0;
}

.record-excerpt {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.record-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding-top: 24px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .records-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .records-page {
    padding: 0 8px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .page-title {
    font-size: 20px;
  }

  .filter-content :deep(.el-form) {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-content :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .list-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-left {
    justify-content: space-between;
  }

  .header-right {
    justify-content: center;
  }

  .records-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .record-card {
    padding: 12px;
  }

  .record-actions {
    flex-wrap: wrap;
  }
}

/* 卡片动画 */
.record-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

<style scoped>
.records-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #909399;
  margin: 0;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
}

.coming-soon h2 {
  margin: 20px 0 12px 0;
  color: #303133;
}

.coming-soon p {
  color: #909399;
  margin: 0;
}
</style>
