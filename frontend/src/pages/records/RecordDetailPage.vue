<template>
  <div class="record-detail-page">
    <div v-loading="loading" class="detail-content">
      <div v-if="record" class="record-detail">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-content">
            <div class="header-left">
              <el-button :icon="ArrowLeft" @click="goBack">返回</el-button>
              <div class="breadcrumb">
                <span class="breadcrumb-item">分析记录</span>
                <el-icon><ArrowRight /></el-icon>
                <span class="breadcrumb-current">{{ record.title || '无标题' }}</span>
              </div>
            </div>
            
            <div class="header-actions">
              <el-button :icon="Edit" @click="editRecord">编辑</el-button>
              <el-button type="danger" :icon="Delete" @click="deleteRecord">删除</el-button>
            </div>
          </div>
        </div>

        <!-- 记录信息 -->
        <el-row :gutter="24">
          <el-col :span="16">
            <!-- 基本信息 -->
            <el-card class="info-card">
              <template #header>
                <div class="card-header">
                  <span>记录信息</span>
                  <div class="record-meta">
                    <el-tag 
                      :type="getAnalysisTypeColor(record.analysisType)" 
                      size="large"
                    >
                      {{ getAnalysisTypeLabel(record.analysisType) }}
                    </el-tag>
                    <el-rate
                      :model-value="record.importanceLevel"
                      :max="4"
                      disabled
                      show-text
                      :texts="['低', '中', '高', '关键']"
                    />
                  </div>
                </div>
              </template>
              
              <div class="record-info">
                <h1 class="record-title">{{ record.title || '无标题' }}</h1>
                
                <div class="info-grid">
                  <div class="info-item">
                    <label>所属小说：</label>
                    <el-link 
                      type="primary" 
                      @click="viewNovel(record.novel)"
                    >
                      {{ record.novel?.title }}
                    </el-link>
                  </div>
                  
                  <div v-if="record.chapterInfo" class="info-item">
                    <label>章节信息：</label>
                    <span>{{ record.chapterInfo }}</span>
                  </div>
                  
                  <div v-if="record.pageLocation" class="info-item">
                    <label>页面位置：</label>
                    <span>{{ record.pageLocation }}</span>
                  </div>
                  
                  <div class="info-item">
                    <label>创建时间：</label>
                    <span>{{ formatDate(record.createdAt) }}</span>
                  </div>
                  
                  <div class="info-item">
                    <label>更新时间：</label>
                    <span>{{ formatDate(record.updatedAt) }}</span>
                  </div>
                  
                  <div class="info-item">
                    <label>字数统计：</label>
                    <span>{{ record.wordCount }} 字</span>
                  </div>
                  
                  <div class="info-item">
                    <label>查看次数：</label>
                    <span>{{ record.viewCount }} 次</span>
                  </div>
                </div>
                
                <div v-if="record.tags && record.tags.length > 0" class="tags-section">
                  <label>标签：</label>
                  <div class="tags-list">
                    <el-tag
                      v-for="tag in record.tags"
                      :key="tag"
                      class="tag-item"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 原文内容 -->
            <el-card v-if="record.originalText" class="content-card">
              <template #header>
                <span>原文内容</span>
              </template>
              <div class="content-section">
                <div class="markdown-content" v-html="renderMarkdown(record.originalText)" />
              </div>
            </el-card>

            <!-- 分析评论 -->
            <el-card v-if="record.commentText" class="content-card">
              <template #header>
                <span>分析评论</span>
              </template>
              <div class="content-section">
                <div class="markdown-content" v-html="renderMarkdown(record.commentText)" />
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <!-- 相关记录 -->
            <el-card class="related-card">
              <template #header>
                <span>相关记录</span>
              </template>
              <div class="related-records">
                <div v-if="relatedRecords.length === 0" class="no-related">
                  暂无相关记录
                </div>
                <div
                  v-for="relatedRecord in relatedRecords"
                  :key="relatedRecord.id"
                  class="related-item"
                  @click="viewRecord(relatedRecord)"
                >
                  <h4 class="related-title">{{ relatedRecord.title || '无标题' }}</h4>
                  <p class="related-excerpt">{{ getExcerpt(relatedRecord.originalText || relatedRecord.commentText) }}</p>
                  <div class="related-meta">
                    <el-tag size="small" :type="getAnalysisTypeColor(relatedRecord.analysisType)">
                      {{ getAnalysisTypeLabel(relatedRecord.analysisType) }}
                    </el-tag>
                    <span class="related-date">{{ formatDate(relatedRecord.createdAt) }}</span>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 操作历史 -->
            <el-card class="history-card">
              <template #header>
                <span>操作历史</span>
              </template>
              <div class="history-timeline">
                <el-timeline>
                  <el-timeline-item
                    timestamp="刚刚"
                    type="primary"
                  >
                    查看记录详情
                  </el-timeline-item>
                  <el-timeline-item
                    :timestamp="formatDate(record.updatedAt)"
                    type="success"
                  >
                    更新记录内容
                  </el-timeline-item>
                  <el-timeline-item
                    :timestamp="formatDate(record.createdAt)"
                    type="info"
                  >
                    创建记录
                  </el-timeline-item>
                </el-timeline>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <div v-else-if="!loading" class="not-found">
        <el-result
          icon="warning"
          title="记录不存在"
          sub-title="您访问的记录可能已被删除或不存在"
        >
          <template #extra>
            <el-button type="primary" @click="goBack">返回列表</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  ArrowRight,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import { useRecordsStore } from '@/stores/records'
import type { AnalysisRecord } from '@/types/models'
import { marked } from 'marked'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const recordsStore = useRecordsStore()

// 状态
const loading = ref(false)
const record = ref<AnalysisRecord | null>(null)
const relatedRecords = ref<AnalysisRecord[]>([])

// 计算属性
const recordId = computed(() => Number(route.params.id))

// 生命周期
onMounted(async () => {
  await loadRecord()
  await loadRelatedRecords()
})

// 方法实现
const loadRecord = async () => {
  if (!recordId.value) return
  
  loading.value = true
  try {
    record.value = await recordsStore.fetchRecord(recordId.value)
  } catch (error) {
    console.error('加载记录详情失败:', error)
    ElMessage.error('加载记录详情失败')
  } finally {
    loading.value = false
  }
}

const loadRelatedRecords = async () => {
  if (!record.value) return
  
  try {
    // 获取同一小说的其他记录
    const response = await recordsStore.fetchRecords({
      novelId: record.value.novelId,
      size: 5
    })
    
    // 排除当前记录
    relatedRecords.value = recordsStore.records.filter(r => r.id !== recordId.value)
  } catch (error) {
    console.error('加载相关记录失败:', error)
  }
}

const goBack = () => {
  router.back()
}

const editRecord = () => {
  router.push(`/records/${recordId.value}/edit`)
}

const deleteRecord = async () => {
  if (!record.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除记录"${record.value.title || '无标题'}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await recordsStore.deleteRecord(recordId.value)
    ElMessage.success('删除成功')
    router.push('/records')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const viewRecord = (targetRecord: AnalysisRecord) => {
  router.push(`/records/${targetRecord.id}`)
}

const viewNovel = (novel: any) => {
  if (novel?.id) {
    router.push(`/novels/${novel.id}`)
  }
}

// 工具函数
const renderMarkdown = (content: string) => {
  try {
    return marked(content)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return content
  }
}

const getAnalysisTypeLabel = (type?: string) => {
  const typeMap: Record<string, string> = {
    character: '人物分析',
    plot: '情节分析',
    theme: '主题分析',
    writing: '写作技巧',
    worldview: '世界观设定',
    other: '其他'
  }
  return typeMap[type || ''] || '未分类'
}

const getAnalysisTypeColor = (type?: string) => {
  const colorMap: Record<string, string> = {
    character: 'success',
    plot: 'primary',
    theme: 'warning',
    writing: 'info',
    worldview: 'danger',
    other: ''
  }
  return colorMap[type || ''] || ''
}

const getExcerpt = (text?: string) => {
  if (!text) return '暂无内容'
  return text.length > 80 ? text.substring(0, 80) + '...' : text
}

const formatDate = (date: string | Date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}
</script>

<style scoped>
.record-detail-page {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 16px;
}

.detail-content {
  min-height: 600px;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
}

.breadcrumb-current {
  color: #303133;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.info-card,
.content-card,
.related-card,
.history-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.tags-section {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.tags-section label {
  font-weight: 500;
  color: #606266;
  margin-top: 4px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  margin: 0;
}

.content-section {
  padding: 8px 0;
}

.markdown-content {
  line-height: 1.8;
  color: #303133;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin: 20px 0 12px 0;
  font-weight: 600;
  color: #303133;
}

.markdown-content :deep(p) {
  margin: 12px 0;
}

.markdown-content :deep(blockquote) {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid var(--el-color-primary);
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-regular);
}

.markdown-content :deep(code) {
  padding: 2px 6px;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.markdown-content :deep(pre) {
  margin: 16px 0;
  padding: 16px;
  background-color: var(--el-bg-color-page);
  border-radius: 6px;
  overflow-x: auto;
}

.markdown-content :deep(pre code) {
  padding: 0;
  background: none;
}

.related-records {
  max-height: 400px;
  overflow-y: auto;
}

.no-related {
  text-align: center;
  color: #909399;
  padding: 40px 20px;
}

.related-item {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.related-item:hover {
  background-color: var(--el-bg-color-page);
}

.related-item:last-child {
  margin-bottom: 0;
}

.related-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 6px 0;
  line-height: 1.4;
}

.related-excerpt {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin: 0 0 8px 0;
}

.related-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.related-date {
  font-size: 12px;
  color: #909399;
}

.history-timeline {
  padding: 8px 0;
}

.not-found {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .info-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .record-detail-page {
    padding: 0 8px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left {
    justify-content: space-between;
  }

  .header-actions {
    justify-content: center;
  }

  .record-title {
    font-size: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .tags-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .record-meta {
    width: 100%;
    justify-content: space-between;
  }
}

/* 动画效果 */
.record-detail {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
