{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/d.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar blockKeywordsStr = \"body catch class do else enum for foreach foreach_reverse if in interface mixin \" +\n    \"out scope struct switch try union unittest version while with\";\n\nconst parserConfig = {\n  keywords: words(\"abstract alias align asm assert auto break case cast cdouble cent cfloat const continue \" +\n                  \"debug default delegate delete deprecated export extern final finally function goto immutable \" +\n                  \"import inout invariant is lazy macro module new nothrow override package pragma private \" +\n                  \"protected public pure ref return shared short static super synchronized template this \" +\n                  \"throw typedef typeid typeof volatile __FILE__ __LINE__ __gshared __traits __vector __parameters \" +\n                  blockKeywordsStr),\n  blockKeywords: words(blockKeywordsStr),\n  builtin: words(\"bool byte char creal dchar double float idouble ifloat int ireal long real short ubyte \" +\n                 \"ucent uint ulong ushort wchar wstring void size_t sizediff_t\"),\n  atoms: words(\"exit failure success true false null\"),\n  hooks: {\n    \"@\": function(stream, _state) {\n      stream.eatWhile(/[\\w\\$_]/);\n      return \"meta\";\n    }\n  }\n}\n\nvar statementIndentUnit = parserConfig.statementIndentUnit,\n    keywords = parserConfig.keywords,\n    builtin = parserConfig.builtin,\n    blockKeywords = parserConfig.blockKeywords,\n    atoms = parserConfig.atoms,\n    hooks = parserConfig.hooks,\n    multiLineStrings = parserConfig.multiLineStrings;\nvar isOperatorChar = /[+\\-*&%=<>!?|\\/]/;\n\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (hooks[ch]) {\n    var result = hooks[ch](stream, state);\n    if (result !== false) return result;\n  }\n  if (ch == '\"' || ch == \"'\" || ch == \"`\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"+\")) {\n      state.tokenize = tokenNestedComment;\n      return tokenNestedComment(stream, state);\n    }\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n  var cur = stream.current();\n  if (keywords.propertyIsEnumerable(cur)) {\n    if (blockKeywords.propertyIsEnumerable(cur)) curPunc = \"newstatement\";\n    return \"keyword\";\n  }\n  if (builtin.propertyIsEnumerable(cur)) {\n    if (blockKeywords.propertyIsEnumerable(cur)) curPunc = \"newstatement\";\n    return \"builtin\";\n  }\n  if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {end = true; break;}\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !(escaped || multiLineStrings))\n      state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenNestedComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"+\");\n  }\n  return \"comment\";\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  var indent = state.indented;\n  if (state.context && state.context.type == \"statement\")\n    indent = state.context.indented;\n  return state.context = new Context(indent, col, type, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n// Interface\n\nexport const d = {\n  name: \"d\",\n  startState: function(indentUnit) {\n    return {\n      tokenize: null,\n      context: new Context(-indentUnit, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\" || style == \"meta\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\") && ctx.type == \"statement\") popContext(state);\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (((ctx.type == \"}\" || ctx.type == \"top\") && curPunc != ';') || (ctx.type == \"statement\" && curPunc == \"newstatement\"))\n      pushContext(state, stream.column(), \"statement\");\n    state.startOfLine = false;\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != tokenBase && state.tokenize != null) return null;\n    var ctx = state.context, firstChar = textAfter && textAfter.charAt(0);\n    if (ctx.type == \"statement\" && firstChar == \"}\") ctx = ctx.prev;\n    var closing = firstChar == ctx.type;\n    if (ctx.type == \"statement\") return ctx.indented + (firstChar == \"{\" ? 0 : statementIndentUnit || cx.unit);\n    else if (ctx.align) return ctx.column + (closing ? 0 : 1);\n    else return ctx.indented + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,MAAM,KAAK;AAClB,MAAI,MAAM,CAAC,GAAGA,SAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE,EAAG,KAAIA,OAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AAEA,IAAI,mBAAmB;AAGvB,IAAM,eAAe;AAAA,EACnB,UAAU,MAAM,wcAKA,gBAAgB;AAAA,EAChC,eAAe,MAAM,gBAAgB;AAAA,EACrC,SAAS,MAAM,qJAC8D;AAAA,EAC7E,OAAO,MAAM,sCAAsC;AAAA,EACnD,OAAO;AAAA,IACL,KAAK,SAAS,QAAQ,QAAQ;AAC5B,aAAO,SAAS,SAAS;AACzB,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAI,sBAAsB,aAAa;AAAvC,IACI,WAAW,aAAa;AAD5B,IAEI,UAAU,aAAa;AAF3B,IAGI,gBAAgB,aAAa;AAHjC,IAII,QAAQ,aAAa;AAJzB,IAKI,QAAQ,aAAa;AALzB,IAMI,mBAAmB,aAAa;AACpC,IAAI,iBAAiB;AAErB,IAAI;AAEJ,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,EAAE,GAAG;AACb,QAAI,SAAS,MAAM,EAAE,EAAE,QAAQ,KAAK;AACpC,QAAI,WAAW,MAAO,QAAO;AAAA,EAC/B;AACA,MAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACvC,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AACA,MAAI,qBAAqB,KAAK,EAAE,GAAG;AACjC,cAAU;AACV,WAAO;AAAA,EACT;AACA,MAAI,KAAK,KAAK,EAAE,GAAG;AACjB,WAAO,SAAS,QAAQ;AACxB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAM,WAAW;AACjB,aAAO,mBAAmB,QAAQ,KAAK;AAAA,IACzC;AACA,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAM,WAAW;AACjB,aAAO,aAAa,QAAQ,KAAK;AAAA,IACnC;AACA,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,eAAe,KAAK,EAAE,GAAG;AAC3B,WAAO,SAAS,cAAc;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,oBAAoB;AACpC,MAAI,MAAM,OAAO,QAAQ;AACzB,MAAI,SAAS,qBAAqB,GAAG,GAAG;AACtC,QAAI,cAAc,qBAAqB,GAAG,EAAG,WAAU;AACvD,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,qBAAqB,GAAG,GAAG;AACrC,QAAI,cAAc,qBAAqB,GAAG,EAAG,WAAU;AACvD,WAAO;AAAA,EACT;AACA,MAAI,MAAM,qBAAqB,GAAG,EAAG,QAAO;AAC5C,SAAO;AACT;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO,MAAM,MAAM;AACjC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,QAAQ,SAAS,CAAC,SAAS;AAAC,cAAM;AAAM;AAAA,MAAM;AAClD,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,QAAI,OAAO,EAAE,WAAW;AACtB,YAAM,WAAW;AACnB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,UAAU,QAAQ,MAAM,OAAO,MAAM;AACpD,OAAK,WAAW;AAChB,OAAK,SAAS;AACd,OAAK,OAAO;AACZ,OAAK,QAAQ;AACb,OAAK,OAAO;AACd;AACA,SAAS,YAAY,OAAO,KAAK,MAAM;AACrC,MAAI,SAAS,MAAM;AACnB,MAAI,MAAM,WAAW,MAAM,QAAQ,QAAQ;AACzC,aAAS,MAAM,QAAQ;AACzB,SAAO,MAAM,UAAU,IAAI,QAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO;AAC3E;AACA,SAAS,WAAW,OAAO;AACzB,MAAI,IAAI,MAAM,QAAQ;AACtB,MAAI,KAAK,OAAO,KAAK,OAAO,KAAK;AAC/B,UAAM,WAAW,MAAM,QAAQ;AACjC,SAAO,MAAM,UAAU,MAAM,QAAQ;AACvC;AAIO,IAAM,IAAI;AAAA,EACf,MAAM;AAAA,EACN,YAAY,SAAS,YAAY;AAC/B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,SAAS,IAAI,QAAQ,CAAC,YAAY,GAAG,OAAO,KAAK;AAAA,MACjD,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,MAAM,MAAM;AAChB,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,IAAI,SAAS,KAAM,KAAI,QAAQ;AACnC,YAAM,WAAW,OAAO,YAAY;AACpC,YAAM,cAAc;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,cAAU;AACV,QAAI,SAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACvD,QAAI,SAAS,aAAa,SAAS,OAAQ,QAAO;AAClD,QAAI,IAAI,SAAS,KAAM,KAAI,QAAQ;AAEnC,SAAK,WAAW,OAAO,WAAW,OAAO,WAAW,QAAQ,IAAI,QAAQ,YAAa,YAAW,KAAK;AAAA,aAC5F,WAAW,IAAK,aAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW,IAAK,aAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW,IAAK,aAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW,KAAK;AACvB,aAAO,IAAI,QAAQ,YAAa,OAAM,WAAW,KAAK;AACtD,UAAI,IAAI,QAAQ,IAAK,OAAM,WAAW,KAAK;AAC3C,aAAO,IAAI,QAAQ,YAAa,OAAM,WAAW,KAAK;AAAA,IACxD,WACS,WAAW,IAAI,KAAM,YAAW,KAAK;AAAA,cACnC,IAAI,QAAQ,OAAO,IAAI,QAAQ,UAAU,WAAW,OAAS,IAAI,QAAQ,eAAe,WAAW;AAC5G,kBAAY,OAAO,OAAO,OAAO,GAAG,WAAW;AACjD,UAAM,cAAc;AACpB,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,MAAM,YAAY,aAAa,MAAM,YAAY,KAAM,QAAO;AAClE,QAAI,MAAM,MAAM,SAAS,YAAY,aAAa,UAAU,OAAO,CAAC;AACpE,QAAI,IAAI,QAAQ,eAAe,aAAa,IAAK,OAAM,IAAI;AAC3D,QAAI,UAAU,aAAa,IAAI;AAC/B,QAAI,IAAI,QAAQ,YAAa,QAAO,IAAI,YAAY,aAAa,MAAM,IAAI,uBAAuB,GAAG;AAAA,aAC5F,IAAI,MAAO,QAAO,IAAI,UAAU,UAAU,IAAI;AAAA,QAClD,QAAO,IAAI,YAAY,UAAU,IAAI,GAAG;AAAA,EAC/C;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAC9D;AACF;", "names": ["words"]}