{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/modelica.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i=0; i<words.length; ++i)\n    obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = words(\"algorithm and annotation assert block break class connect connector constant constrainedby der discrete each else elseif elsewhen encapsulated end enumeration equation expandable extends external false final flow for function if import impure in initial inner input loop model not operator or outer output package parameter partial protected public pure record redeclare replaceable return stream then true type when while within\")\nvar builtin = words(\"abs acos actualStream asin atan atan2 cardinality ceil cos cosh delay div edge exp floor getInstanceName homotopy inStream integer log log10 mod pre reinit rem semiLinear sign sin sinh spatialDistribution sqrt tan tanh\")\nvar atoms = words(\"Real Boolean Integer String\")\n\nvar completions = [].concat(Object.keys(keywords), Object.keys(builtin), Object.keys(atoms))\n\nvar isSingleOperatorChar = /[;=\\(:\\),{}.*<>+\\-\\/^\\[\\]]/;\nvar isDoubleOperatorChar = /(:=|<=|>=|==|<>|\\.\\+|\\.\\-|\\.\\*|\\.\\/|\\.\\^)/;\nvar isDigit = /[0-9]/;\nvar isNonDigit = /[_a-zA-Z]/;\n\nfunction tokenLineComment(stream, state) {\n  stream.skipToEnd();\n  state.tokenize = null;\n  return \"comment\";\n}\n\nfunction tokenBlockComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(stream, state) {\n  var escaped = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == '\"' && !escaped) {\n      state.tokenize = null;\n      state.sol = false;\n      break;\n    }\n    escaped = !escaped && ch == \"\\\\\";\n  }\n\n  return \"string\";\n}\n\nfunction tokenIdent(stream, state) {\n  stream.eatWhile(isDigit);\n  while (stream.eat(isDigit) || stream.eat(isNonDigit)) { }\n\n\n  var cur = stream.current();\n\n  if(state.sol && (cur == \"package\" || cur == \"model\" || cur == \"when\" || cur == \"connector\")) state.level++;\n  else if(state.sol && cur == \"end\" && state.level > 0) state.level--;\n\n  state.tokenize = null;\n  state.sol = false;\n\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  else if (builtin.propertyIsEnumerable(cur)) return \"builtin\";\n  else if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  else return \"variable\";\n}\n\nfunction tokenQIdent(stream, state) {\n  while (stream.eat(/[^']/)) { }\n\n  state.tokenize = null;\n  state.sol = false;\n\n  if(stream.eat(\"'\"))\n    return \"variable\";\n  else\n    return \"error\";\n}\n\nfunction tokenUnsignedNumber(stream, state) {\n  stream.eatWhile(isDigit);\n  if (stream.eat('.')) {\n    stream.eatWhile(isDigit);\n  }\n  if (stream.eat('e') || stream.eat('E')) {\n    if (!stream.eat('-'))\n      stream.eat('+');\n    stream.eatWhile(isDigit);\n  }\n\n  state.tokenize = null;\n  state.sol = false;\n  return \"number\";\n}\n\n// Interface\nexport const modelica = {\n  name: \"modelica\",\n  startState: function() {\n    return {\n      tokenize: null,\n      level: 0,\n      sol: true\n    };\n  },\n\n  token: function(stream, state) {\n    if(state.tokenize != null) {\n      return state.tokenize(stream, state);\n    }\n\n    if(stream.sol()) {\n      state.sol = true;\n    }\n\n    // WHITESPACE\n    if(stream.eatSpace()) {\n      state.tokenize = null;\n      return null;\n    }\n\n    var ch = stream.next();\n\n    // LINECOMMENT\n    if(ch == '/' && stream.eat('/')) {\n      state.tokenize = tokenLineComment;\n    }\n    // BLOCKCOMMENT\n    else if(ch == '/' && stream.eat('*')) {\n      state.tokenize = tokenBlockComment;\n    }\n    // TWO SYMBOL TOKENS\n    else if(isDoubleOperatorChar.test(ch+stream.peek())) {\n      stream.next();\n      state.tokenize = null;\n      return \"operator\";\n    }\n    // SINGLE SYMBOL TOKENS\n    else if(isSingleOperatorChar.test(ch)) {\n      state.tokenize = null;\n      return \"operator\";\n    }\n    // IDENT\n    else if(isNonDigit.test(ch)) {\n      state.tokenize = tokenIdent;\n    }\n    // Q-IDENT\n    else if(ch == \"'\" && stream.peek() && stream.peek() != \"'\") {\n      state.tokenize = tokenQIdent;\n    }\n    // STRING\n    else if(ch == '\"') {\n      state.tokenize = tokenString;\n    }\n    // UNSIGNED_NUMBER\n    else if(isDigit.test(ch)) {\n      state.tokenize = tokenUnsignedNumber;\n    }\n    // ERROR\n    else {\n      state.tokenize = null;\n      return \"error\";\n    }\n\n    return state.tokenize(stream, state);\n  },\n\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize != null) return null;\n\n    var level = state.level;\n    if(/(algorithm)/.test(textAfter)) level--;\n    if(/(equation)/.test(textAfter)) level--;\n    if(/(initial algorithm)/.test(textAfter)) level--;\n    if(/(initial equation)/.test(textAfter)) level--;\n    if(/(end)/.test(textAfter)) level--;\n\n    if(level > 0)\n      return cx.unit*level;\n    else\n      return 0;\n  },\n\n  languageData: {\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    autocomplete: completions\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,MAAM,KAAK;AAClB,MAAI,MAAM,CAAC,GAAGA,SAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAE,GAAG,IAAEA,OAAM,QAAQ,EAAE;AAC9B,QAAIA,OAAM,CAAC,CAAC,IAAI;AAClB,SAAO;AACT;AAEA,IAAI,WAAW,MAAM,+aAA+a;AACpc,IAAI,UAAU,MAAM,4NAA4N;AAChP,IAAI,QAAQ,MAAM,6BAA6B;AAE/C,IAAI,cAAc,CAAC,EAAE,OAAO,OAAO,KAAK,QAAQ,GAAG,OAAO,KAAK,OAAO,GAAG,OAAO,KAAK,KAAK,CAAC;AAE3F,IAAI,uBAAuB;AAC3B,IAAI,uBAAuB;AAC3B,IAAI,UAAU;AACd,IAAI,aAAa;AAEjB,SAAS,iBAAiB,QAAQ,OAAO;AACvC,SAAO,UAAU;AACjB,QAAM,WAAW;AACjB,SAAO;AACT;AAEA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,YAAY,MAAM,KAAK;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,YAAY,QAAQ,OAAO;AAClC,MAAI,UAAU,OAAO;AACrB,UAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,QAAI,MAAM,OAAO,CAAC,SAAS;AACzB,YAAM,WAAW;AACjB,YAAM,MAAM;AACZ;AAAA,IACF;AACA,cAAU,CAAC,WAAW,MAAM;AAAA,EAC9B;AAEA,SAAO;AACT;AAEA,SAAS,WAAW,QAAQ,OAAO;AACjC,SAAO,SAAS,OAAO;AACvB,SAAO,OAAO,IAAI,OAAO,KAAK,OAAO,IAAI,UAAU,GAAG;AAAA,EAAE;AAGxD,MAAI,MAAM,OAAO,QAAQ;AAEzB,MAAG,MAAM,QAAQ,OAAO,aAAa,OAAO,WAAW,OAAO,UAAU,OAAO,aAAc,OAAM;AAAA,WAC3F,MAAM,OAAO,OAAO,SAAS,MAAM,QAAQ,EAAG,OAAM;AAE5D,QAAM,WAAW;AACjB,QAAM,MAAM;AAEZ,MAAI,SAAS,qBAAqB,GAAG,EAAG,QAAO;AAAA,WACtC,QAAQ,qBAAqB,GAAG,EAAG,QAAO;AAAA,WAC1C,MAAM,qBAAqB,GAAG,EAAG,QAAO;AAAA,MAC5C,QAAO;AACd;AAEA,SAAS,YAAY,QAAQ,OAAO;AAClC,SAAO,OAAO,IAAI,MAAM,GAAG;AAAA,EAAE;AAE7B,QAAM,WAAW;AACjB,QAAM,MAAM;AAEZ,MAAG,OAAO,IAAI,GAAG;AACf,WAAO;AAAA;AAEP,WAAO;AACX;AAEA,SAAS,oBAAoB,QAAQ,OAAO;AAC1C,SAAO,SAAS,OAAO;AACvB,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,MAAI,OAAO,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,GAAG;AACtC,QAAI,CAAC,OAAO,IAAI,GAAG;AACjB,aAAO,IAAI,GAAG;AAChB,WAAO,SAAS,OAAO;AAAA,EACzB;AAEA,QAAM,WAAW;AACjB,QAAM,MAAM;AACZ,SAAO;AACT;AAGO,IAAM,WAAW;AAAA,EACtB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAG,MAAM,YAAY,MAAM;AACzB,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC;AAEA,QAAG,OAAO,IAAI,GAAG;AACf,YAAM,MAAM;AAAA,IACd;AAGA,QAAG,OAAO,SAAS,GAAG;AACpB,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,OAAO,KAAK;AAGrB,QAAG,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAC/B,YAAM,WAAW;AAAA,IACnB,WAEQ,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AACpC,YAAM,WAAW;AAAA,IACnB,WAEQ,qBAAqB,KAAK,KAAG,OAAO,KAAK,CAAC,GAAG;AACnD,aAAO,KAAK;AACZ,YAAM,WAAW;AACjB,aAAO;AAAA,IACT,WAEQ,qBAAqB,KAAK,EAAE,GAAG;AACrC,YAAM,WAAW;AACjB,aAAO;AAAA,IACT,WAEQ,WAAW,KAAK,EAAE,GAAG;AAC3B,YAAM,WAAW;AAAA,IACnB,WAEQ,MAAM,OAAO,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AAC1D,YAAM,WAAW;AAAA,IACnB,WAEQ,MAAM,KAAK;AACjB,YAAM,WAAW;AAAA,IACnB,WAEQ,QAAQ,KAAK,EAAE,GAAG;AACxB,YAAM,WAAW;AAAA,IACnB,OAEK;AACH,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,MAAM,YAAY,KAAM,QAAO;AAEnC,QAAI,QAAQ,MAAM;AAClB,QAAG,cAAc,KAAK,SAAS,EAAG;AAClC,QAAG,aAAa,KAAK,SAAS,EAAG;AACjC,QAAG,sBAAsB,KAAK,SAAS,EAAG;AAC1C,QAAG,qBAAqB,KAAK,SAAS,EAAG;AACzC,QAAG,QAAQ,KAAK,SAAS,EAAG;AAE5B,QAAG,QAAQ;AACT,aAAO,GAAG,OAAK;AAAA;AAEf,aAAO;AAAA,EACX;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,IAC5D,cAAc;AAAA,EAChB;AACF;", "names": ["words"]}