{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/troff.js"], "sourcesContent": ["var words = {};\n\nfunction tokenBase(stream) {\n  if (stream.eatSpace()) return null;\n\n  var sol = stream.sol();\n  var ch = stream.next();\n\n  if (ch === '\\\\') {\n    if (stream.match('fB') || stream.match('fR') || stream.match('fI') ||\n        stream.match('u')  || stream.match('d')  ||\n        stream.match('%')  || stream.match('&')) {\n      return 'string';\n    }\n    if (stream.match('m[')) {\n      stream.skipTo(']');\n      stream.next();\n      return 'string';\n    }\n    if (stream.match('s+') || stream.match('s-')) {\n      stream.eatWhile(/[\\d-]/);\n      return 'string';\n    }\n    if (stream.match('\\(') || stream.match('*\\(')) {\n      stream.eatWhile(/[\\w-]/);\n      return 'string';\n    }\n    return 'string';\n  }\n  if (sol && (ch === '.' || ch === '\\'')) {\n    if (stream.eat('\\\\') && stream.eat('\\\"')) {\n      stream.skipToEnd();\n      return 'comment';\n    }\n  }\n  if (sol && ch === '.') {\n    if (stream.match('B ') || stream.match('I ') || stream.match('R ')) {\n      return 'attribute';\n    }\n    if (stream.match('TH ') || stream.match('SH ') || stream.match('SS ') || stream.match('HP ')) {\n      stream.skipToEnd();\n      return 'quote';\n    }\n    if ((stream.match(/[A-Z]/) && stream.match(/[A-Z]/)) || (stream.match(/[a-z]/) && stream.match(/[a-z]/))) {\n      return 'attribute';\n    }\n  }\n  stream.eatWhile(/[\\w-]/);\n  var cur = stream.current();\n  return words.hasOwnProperty(cur) ? words[cur] : null;\n}\n\nfunction tokenize(stream, state) {\n  return (state.tokens[0] || tokenBase) (stream, state);\n};\n\nexport const troff = {\n  name: \"troff\",\n  startState: function() {return {tokens:[]};},\n  token: function(stream, state) {\n    return tokenize(stream, state);\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,QAAQ,CAAC;AAEb,SAAS,UAAU,QAAQ;AACzB,MAAI,OAAO,SAAS,EAAG,QAAO;AAE9B,MAAI,MAAM,OAAO,IAAI;AACrB,MAAI,KAAK,OAAO,KAAK;AAErB,MAAI,OAAO,MAAM;AACf,QAAI,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAC7D,OAAO,MAAM,GAAG,KAAM,OAAO,MAAM,GAAG,KACtC,OAAO,MAAM,GAAG,KAAM,OAAO,MAAM,GAAG,GAAG;AAC3C,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,IAAI,GAAG;AACtB,aAAO,OAAO,GAAG;AACjB,aAAO,KAAK;AACZ,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,GAAG;AAC5C,aAAO,SAAS,OAAO;AACvB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,GAAI,KAAK,OAAO,MAAM,IAAK,GAAG;AAC7C,aAAO,SAAS,OAAO;AACvB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,OAAO,OAAO,OAAO,MAAO;AACtC,QAAI,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI,GAAI,GAAG;AACxC,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,OAAO,OAAO,KAAK;AACrB,QAAI,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,GAAG;AAClE,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG;AAC5F,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AACA,QAAK,OAAO,MAAM,OAAO,KAAK,OAAO,MAAM,OAAO,KAAO,OAAO,MAAM,OAAO,KAAK,OAAO,MAAM,OAAO,GAAI;AACxG,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,SAAS,OAAO;AACvB,MAAI,MAAM,OAAO,QAAQ;AACzB,SAAO,MAAM,eAAe,GAAG,IAAI,MAAM,GAAG,IAAI;AAClD;AAEA,SAAS,SAAS,QAAQ,OAAO;AAC/B,UAAQ,MAAM,OAAO,CAAC,KAAK,WAAY,QAAQ,KAAK;AACtD;AAEO,IAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,YAAY,WAAW;AAAC,WAAO,EAAC,QAAO,CAAC,EAAC;AAAA,EAAE;AAAA,EAC3C,OAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B;AACF;", "names": []}