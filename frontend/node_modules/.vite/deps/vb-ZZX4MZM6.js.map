{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/vb.js"], "sourcesContent": ["var ERRORCLASS = 'error';\n\nfunction wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\", \"i\");\n}\n\nvar singleOperators = new RegExp(\"^[\\\\+\\\\-\\\\*/%&\\\\\\\\|\\\\^~<>!]\");\nvar singleDelimiters = new RegExp('^[\\\\(\\\\)\\\\[\\\\]\\\\{\\\\}@,:`=;\\\\.]');\nvar doubleOperators = new RegExp(\"^((==)|(<>)|(<=)|(>=)|(<>)|(<<)|(>>)|(//)|(\\\\*\\\\*))\");\nvar doubleDelimiters = new RegExp(\"^((\\\\+=)|(\\\\-=)|(\\\\*=)|(%=)|(/=)|(&=)|(\\\\|=)|(\\\\^=))\");\nvar tripleDelimiters = new RegExp(\"^((//=)|(>>=)|(<<=)|(\\\\*\\\\*=))\");\nvar identifiers = new RegExp(\"^[_A-Za-z][_A-Za-z0-9]*\");\n\nvar openingKeywords = ['class','module', 'sub','enum','select','while','if','function', 'get','set','property', 'try', 'structure', 'synclock', 'using', 'with'];\nvar middleKeywords = ['else','elseif','case', 'catch', 'finally'];\nvar endKeywords = ['next','loop'];\n\nvar operatorKeywords = ['and', \"andalso\", 'or', 'orelse', 'xor', 'in', 'not', 'is', 'isnot', 'like'];\nvar wordOperators = wordRegexp(operatorKeywords);\n\nvar commonKeywords = [\"#const\", \"#else\", \"#elseif\", \"#end\", \"#if\", \"#region\", \"addhandler\", \"addressof\", \"alias\", \"as\", \"byref\", \"byval\", \"cbool\", \"cbyte\", \"cchar\", \"cdate\", \"cdbl\", \"cdec\", \"cint\", \"clng\", \"cobj\", \"compare\", \"const\", \"continue\", \"csbyte\", \"cshort\", \"csng\", \"cstr\", \"cuint\", \"culng\", \"cushort\", \"declare\", \"default\", \"delegate\", \"dim\", \"directcast\", \"each\", \"erase\", \"error\", \"event\", \"exit\", \"explicit\", \"false\", \"for\", \"friend\", \"gettype\", \"goto\", \"handles\", \"implements\", \"imports\", \"infer\", \"inherits\", \"interface\", \"isfalse\", \"istrue\", \"lib\", \"me\", \"mod\", \"mustinherit\", \"mustoverride\", \"my\", \"mybase\", \"myclass\", \"namespace\", \"narrowing\", \"new\", \"nothing\", \"notinheritable\", \"notoverridable\", \"of\", \"off\", \"on\", \"operator\", \"option\", \"optional\", \"out\", \"overloads\", \"overridable\", \"overrides\", \"paramarray\", \"partial\", \"private\", \"protected\", \"public\", \"raiseevent\", \"readonly\", \"redim\", \"removehandler\", \"resume\", \"return\", \"shadows\", \"shared\", \"static\", \"step\", \"stop\", \"strict\", \"then\", \"throw\", \"to\", \"true\", \"trycast\", \"typeof\", \"until\", \"until\", \"when\", \"widening\", \"withevents\", \"writeonly\"];\n\nvar commontypes = ['object', 'boolean', 'char', 'string', 'byte', 'sbyte', 'short', 'ushort', 'int16', 'uint16', 'integer', 'uinteger', 'int32', 'uint32', 'long', 'ulong', 'int64', 'uint64', 'decimal', 'single', 'double', 'float', 'date', 'datetime', 'intptr', 'uintptr'];\n\nvar keywords = wordRegexp(commonKeywords);\nvar types = wordRegexp(commontypes);\nvar stringPrefixes = '\"';\n\nvar opening = wordRegexp(openingKeywords);\nvar middle = wordRegexp(middleKeywords);\nvar closing = wordRegexp(endKeywords);\nvar doubleClosing = wordRegexp(['end']);\nvar doOpening = wordRegexp(['do']);\n\nvar indentInfo = null;\n\nfunction indent(_stream, state) {\n  state.currentIndent++;\n}\n\nfunction dedent(_stream, state) {\n  state.currentIndent--;\n}\n// tokenizers\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  var ch = stream.peek();\n\n  // Handle Comments\n  if (ch === \"'\") {\n    stream.skipToEnd();\n    return 'comment';\n  }\n\n\n  // Handle Number Literals\n  if (stream.match(/^((&H)|(&O))?[0-9\\.a-f]/i, false)) {\n    var floatLiteral = false;\n    // Floats\n    if (stream.match(/^\\d*\\.\\d+F?/i)) { floatLiteral = true; }\n    else if (stream.match(/^\\d+\\.\\d*F?/)) { floatLiteral = true; }\n    else if (stream.match(/^\\.\\d+F?/)) { floatLiteral = true; }\n\n    if (floatLiteral) {\n      // Float literals may be \"imaginary\"\n      stream.eat(/J/i);\n      return 'number';\n    }\n    // Integers\n    var intLiteral = false;\n    // Hex\n    if (stream.match(/^&H[0-9a-f]+/i)) { intLiteral = true; }\n    // Octal\n    else if (stream.match(/^&O[0-7]+/i)) { intLiteral = true; }\n    // Decimal\n    else if (stream.match(/^[1-9]\\d*F?/)) {\n      // Decimal literals may be \"imaginary\"\n      stream.eat(/J/i);\n      // TODO - Can you have imaginary longs?\n      intLiteral = true;\n    }\n    // Zero by itself with no other piece of number.\n    else if (stream.match(/^0(?![\\dx])/i)) { intLiteral = true; }\n    if (intLiteral) {\n      // Integer literals may be \"long\"\n      stream.eat(/L/i);\n      return 'number';\n    }\n  }\n\n  // Handle Strings\n  if (stream.match(stringPrefixes)) {\n    state.tokenize = tokenStringFactory(stream.current());\n    return state.tokenize(stream, state);\n  }\n\n  // Handle operators and Delimiters\n  if (stream.match(tripleDelimiters) || stream.match(doubleDelimiters)) {\n    return null;\n  }\n  if (stream.match(doubleOperators)\n      || stream.match(singleOperators)\n      || stream.match(wordOperators)) {\n    return 'operator';\n  }\n  if (stream.match(singleDelimiters)) {\n    return null;\n  }\n  if (stream.match(doOpening)) {\n    indent(stream,state);\n    state.doInCurrentLine = true;\n    return 'keyword';\n  }\n  if (stream.match(opening)) {\n    if (! state.doInCurrentLine)\n      indent(stream,state);\n    else\n      state.doInCurrentLine = false;\n    return 'keyword';\n  }\n  if (stream.match(middle)) {\n    return 'keyword';\n  }\n\n  if (stream.match(doubleClosing)) {\n    dedent(stream,state);\n    dedent(stream,state);\n    return 'keyword';\n  }\n  if (stream.match(closing)) {\n    dedent(stream,state);\n    return 'keyword';\n  }\n\n  if (stream.match(types)) {\n    return 'keyword';\n  }\n\n  if (stream.match(keywords)) {\n    return 'keyword';\n  }\n\n  if (stream.match(identifiers)) {\n    return 'variable';\n  }\n\n  // Handle non-detected items\n  stream.next();\n  return ERRORCLASS;\n}\n\nfunction tokenStringFactory(delimiter) {\n  var singleline = delimiter.length == 1;\n  var OUTCLASS = 'string';\n\n  return function(stream, state) {\n    while (!stream.eol()) {\n      stream.eatWhile(/[^'\"]/);\n      if (stream.match(delimiter)) {\n        state.tokenize = tokenBase;\n        return OUTCLASS;\n      } else {\n        stream.eat(/['\"]/);\n      }\n    }\n    if (singleline) {\n      state.tokenize = tokenBase;\n    }\n    return OUTCLASS;\n  };\n}\n\n\nfunction tokenLexer(stream, state) {\n  var style = state.tokenize(stream, state);\n  var current = stream.current();\n\n  // Handle '.' connected identifiers\n  if (current === '.') {\n    style = state.tokenize(stream, state);\n    if (style === 'variable') {\n      return 'variable';\n    } else {\n      return ERRORCLASS;\n    }\n  }\n\n\n  var delimiter_index = '[({'.indexOf(current);\n  if (delimiter_index !== -1) {\n    indent(stream, state );\n  }\n  if (indentInfo === 'dedent') {\n    if (dedent(stream, state)) {\n      return ERRORCLASS;\n    }\n  }\n  delimiter_index = '])}'.indexOf(current);\n  if (delimiter_index !== -1) {\n    if (dedent(stream, state)) {\n      return ERRORCLASS;\n    }\n  }\n\n  return style;\n}\n\nexport const vb = {\n  name: \"vb\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      lastToken: null,\n      currentIndent: 0,\n      nextLineIndent: 0,\n      doInCurrentLine: false\n\n\n    };\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      state.currentIndent += state.nextLineIndent;\n      state.nextLineIndent = 0;\n      state.doInCurrentLine = 0;\n    }\n    var style = tokenLexer(stream, state);\n\n    state.lastToken = {style:style, content: stream.current()};\n\n\n\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var trueText = textAfter.replace(/^\\s+|\\s+$/g, '') ;\n    if (trueText.match(closing) || trueText.match(doubleClosing) || trueText.match(middle)) return cx.unit*(state.currentIndent-1);\n    if(state.currentIndent < 0) return 0;\n    return state.currentIndent * cx.unit;\n  },\n\n  languageData: {\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']},\n    commentTokens: {line: \"'\"},\n    autocomplete: openingKeywords.concat(middleKeywords).concat(endKeywords)\n      .concat(operatorKeywords).concat(commonKeywords).concat(commontypes)\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,aAAa;AAEjB,SAAS,WAAW,OAAO;AACzB,SAAO,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,IAAI,SAAS,GAAG;AAC5D;AAEA,IAAI,kBAAkB,IAAI,OAAO,6BAA6B;AAC9D,IAAI,mBAAmB,IAAI,OAAO,gCAAgC;AAClE,IAAI,kBAAkB,IAAI,OAAO,qDAAqD;AACtF,IAAI,mBAAmB,IAAI,OAAO,sDAAsD;AACxF,IAAI,mBAAmB,IAAI,OAAO,gCAAgC;AAClE,IAAI,cAAc,IAAI,OAAO,yBAAyB;AAEtD,IAAI,kBAAkB,CAAC,SAAQ,UAAU,OAAM,QAAO,UAAS,SAAQ,MAAK,YAAY,OAAM,OAAM,YAAY,OAAO,aAAa,YAAY,SAAS,MAAM;AAC/J,IAAI,iBAAiB,CAAC,QAAO,UAAS,QAAQ,SAAS,SAAS;AAChE,IAAI,cAAc,CAAC,QAAO,MAAM;AAEhC,IAAI,mBAAmB,CAAC,OAAO,WAAW,MAAM,UAAU,OAAO,MAAM,OAAO,MAAM,SAAS,MAAM;AACnG,IAAI,gBAAgB,WAAW,gBAAgB;AAE/C,IAAI,iBAAiB,CAAC,UAAU,SAAS,WAAW,QAAQ,OAAO,WAAW,cAAc,aAAa,SAAS,MAAM,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,WAAW,SAAS,YAAY,UAAU,UAAU,QAAQ,QAAQ,SAAS,SAAS,WAAW,WAAW,WAAW,YAAY,OAAO,cAAc,QAAQ,SAAS,SAAS,SAAS,QAAQ,YAAY,SAAS,OAAO,UAAU,WAAW,QAAQ,WAAW,cAAc,WAAW,SAAS,YAAY,aAAa,WAAW,UAAU,OAAO,MAAM,OAAO,eAAe,gBAAgB,MAAM,UAAU,WAAW,aAAa,aAAa,OAAO,WAAW,kBAAkB,kBAAkB,MAAM,OAAO,MAAM,YAAY,UAAU,YAAY,OAAO,aAAa,eAAe,aAAa,cAAc,WAAW,WAAW,aAAa,UAAU,cAAc,YAAY,SAAS,iBAAiB,UAAU,UAAU,WAAW,UAAU,UAAU,QAAQ,QAAQ,UAAU,QAAQ,SAAS,MAAM,QAAQ,WAAW,UAAU,SAAS,SAAS,QAAQ,YAAY,cAAc,WAAW;AAE/lC,IAAI,cAAc,CAAC,UAAU,WAAW,QAAQ,UAAU,QAAQ,SAAS,SAAS,UAAU,SAAS,UAAU,WAAW,YAAY,SAAS,UAAU,QAAQ,SAAS,SAAS,UAAU,WAAW,UAAU,UAAU,SAAS,QAAQ,YAAY,UAAU,SAAS;AAE9Q,IAAI,WAAW,WAAW,cAAc;AACxC,IAAI,QAAQ,WAAW,WAAW;AAClC,IAAI,iBAAiB;AAErB,IAAI,UAAU,WAAW,eAAe;AACxC,IAAI,SAAS,WAAW,cAAc;AACtC,IAAI,UAAU,WAAW,WAAW;AACpC,IAAI,gBAAgB,WAAW,CAAC,KAAK,CAAC;AACtC,IAAI,YAAY,WAAW,CAAC,IAAI,CAAC;AAEjC,IAAI,aAAa;AAEjB,SAAS,OAAO,SAAS,OAAO;AAC9B,QAAM;AACR;AAEA,SAAS,OAAO,SAAS,OAAO;AAC9B,QAAM;AACR;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,OAAO,KAAK;AAGrB,MAAI,OAAO,KAAK;AACd,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAIA,MAAI,OAAO,MAAM,4BAA4B,KAAK,GAAG;AACnD,QAAI,eAAe;AAEnB,QAAI,OAAO,MAAM,cAAc,GAAG;AAAE,qBAAe;AAAA,IAAM,WAChD,OAAO,MAAM,aAAa,GAAG;AAAE,qBAAe;AAAA,IAAM,WACpD,OAAO,MAAM,UAAU,GAAG;AAAE,qBAAe;AAAA,IAAM;AAE1D,QAAI,cAAc;AAEhB,aAAO,IAAI,IAAI;AACf,aAAO;AAAA,IACT;AAEA,QAAI,aAAa;AAEjB,QAAI,OAAO,MAAM,eAAe,GAAG;AAAE,mBAAa;AAAA,IAAM,WAE/C,OAAO,MAAM,YAAY,GAAG;AAAE,mBAAa;AAAA,IAAM,WAEjD,OAAO,MAAM,aAAa,GAAG;AAEpC,aAAO,IAAI,IAAI;AAEf,mBAAa;AAAA,IACf,WAES,OAAO,MAAM,cAAc,GAAG;AAAE,mBAAa;AAAA,IAAM;AAC5D,QAAI,YAAY;AAEd,aAAO,IAAI,IAAI;AACf,aAAO;AAAA,IACT;AAAA,EACF;AAGA,MAAI,OAAO,MAAM,cAAc,GAAG;AAChC,UAAM,WAAW,mBAAmB,OAAO,QAAQ,CAAC;AACpD,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAGA,MAAI,OAAO,MAAM,gBAAgB,KAAK,OAAO,MAAM,gBAAgB,GAAG;AACpE,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,eAAe,KACzB,OAAO,MAAM,eAAe,KAC5B,OAAO,MAAM,aAAa,GAAG;AAClC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,gBAAgB,GAAG;AAClC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,WAAO,QAAO,KAAK;AACnB,UAAM,kBAAkB;AACxB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,QAAI,CAAE,MAAM;AACV,aAAO,QAAO,KAAK;AAAA;AAEnB,YAAM,kBAAkB;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,MAAM,GAAG;AACxB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,aAAa,GAAG;AAC/B,WAAO,QAAO,KAAK;AACnB,WAAO,QAAO,KAAK;AACnB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,WAAO,QAAO,KAAK;AACnB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,WAAO;AAAA,EACT;AAGA,SAAO,KAAK;AACZ,SAAO;AACT;AAEA,SAAS,mBAAmB,WAAW;AACrC,MAAI,aAAa,UAAU,UAAU;AACrC,MAAI,WAAW;AAEf,SAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,CAAC,OAAO,IAAI,GAAG;AACpB,aAAO,SAAS,OAAO;AACvB,UAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,cAAM,WAAW;AACjB,eAAO;AAAA,MACT,OAAO;AACL,eAAO,IAAI,MAAM;AAAA,MACnB;AAAA,IACF;AACA,QAAI,YAAY;AACd,YAAM,WAAW;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,WAAW,QAAQ,OAAO;AACjC,MAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,MAAI,UAAU,OAAO,QAAQ;AAG7B,MAAI,YAAY,KAAK;AACnB,YAAQ,MAAM,SAAS,QAAQ,KAAK;AACpC,QAAI,UAAU,YAAY;AACxB,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAGA,MAAI,kBAAkB,MAAM,QAAQ,OAAO;AAC3C,MAAI,oBAAoB,IAAI;AAC1B,WAAO,QAAQ,KAAM;AAAA,EACvB;AACA,MAAI,eAAe,UAAU;AAC3B,QAAI,OAAO,QAAQ,KAAK,GAAG;AACzB,aAAO;AAAA,IACT;AAAA,EACF;AACA,oBAAkB,MAAM,QAAQ,OAAO;AACvC,MAAI,oBAAoB,IAAI;AAC1B,QAAI,OAAO,QAAQ,KAAK,GAAG;AACzB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEO,IAAM,KAAK;AAAA,EAChB,MAAM;AAAA,EAEN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW;AAAA,MACX,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,IAGnB;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI,GAAG;AAChB,YAAM,iBAAiB,MAAM;AAC7B,YAAM,iBAAiB;AACvB,YAAM,kBAAkB;AAAA,IAC1B;AACA,QAAI,QAAQ,WAAW,QAAQ,KAAK;AAEpC,UAAM,YAAY,EAAC,OAAa,SAAS,OAAO,QAAQ,EAAC;AAIzD,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,WAAW,UAAU,QAAQ,cAAc,EAAE;AACjD,QAAI,SAAS,MAAM,OAAO,KAAK,SAAS,MAAM,aAAa,KAAK,SAAS,MAAM,MAAM,EAAG,QAAO,GAAG,QAAM,MAAM,gBAAc;AAC5H,QAAG,MAAM,gBAAgB,EAAG,QAAO;AACnC,WAAO,MAAM,gBAAgB,GAAG;AAAA,EAClC;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,UAAU,CAAC,KAAK,KAAK,KAAK,GAAG,EAAC;AAAA,IAC9C,eAAe,EAAC,MAAM,IAAG;AAAA,IACzB,cAAc,gBAAgB,OAAO,cAAc,EAAE,OAAO,WAAW,EACpE,OAAO,gBAAgB,EAAE,OAAO,cAAc,EAAE,OAAO,WAAW;AAAA,EACvE;AACF;", "names": []}