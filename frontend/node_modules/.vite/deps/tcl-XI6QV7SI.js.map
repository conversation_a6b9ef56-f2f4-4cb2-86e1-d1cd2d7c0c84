{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/tcl.js"], "sourcesContent": ["function parseWords(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\nvar keywords = parseWords(\"Tcl safe after append array auto_execok auto_import auto_load \" +\n                          \"auto_mkindex auto_mkindex_old auto_qualify auto_reset bgerror \" +\n                          \"binary break catch cd close concat continue dde eof encoding error \" +\n                          \"eval exec exit expr fblocked fconfigure fcopy file fileevent filename \" +\n                          \"filename flush for foreach format gets glob global history http if \" +\n                          \"incr info interp join lappend lindex linsert list llength load lrange \" +\n                          \"lreplace lsearch lset lsort memory msgcat namespace open package parray \" +\n                          \"pid pkg::create pkg_mkIndex proc puts pwd re_syntax read regex regexp \" +\n                          \"registry regsub rename resource return scan seek set socket source split \" +\n                          \"string subst switch tcl_endOfWord tcl_findLibrary tcl_startOfNextWord \" +\n                          \"tcl_wordBreakAfter tcl_startOfPreviousWord tcl_wordBreakBefore tcltest \" +\n                          \"tclvars tell time trace unknown unset update uplevel upvar variable \" +\n                          \"vwait\");\nvar functions = parseWords(\"if elseif else and not or eq ne in ni for foreach while switch\");\nvar isOperatorChar = /[+\\-*&%=<>!?^\\/\\|]/;\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\nfunction tokenBase(stream, state) {\n  var beforeParams = state.beforeParams;\n  state.beforeParams = false;\n  var ch = stream.next();\n  if ((ch == '\"' || ch == \"'\") && state.inParams) {\n    return chain(stream, state, tokenString(ch));\n  } else if (/[\\[\\]{}\\(\\),;\\.]/.test(ch)) {\n    if (ch == \"(\" && beforeParams) state.inParams = true;\n    else if (ch == \")\") state.inParams = false;\n    return null;\n  } else if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  } else if (ch == \"#\") {\n    if (stream.eat(\"*\"))\n      return chain(stream, state, tokenComment);\n    if (ch == \"#\" && stream.match(/ *\\[ *\\[/))\n      return chain(stream, state, tokenUnparsed);\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (ch == '\"') {\n    stream.skipTo(/\"/);\n    return \"comment\";\n  } else if (ch == \"$\") {\n    stream.eatWhile(/[$_a-z0-9A-Z\\.{:]/);\n    stream.eatWhile(/}/);\n    state.beforeParams = true;\n    return \"builtin\";\n  } else if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"comment\";\n  } else {\n    stream.eatWhile(/[\\w\\$_{}\\xa1-\\uffff]/);\n    var word = stream.current().toLowerCase();\n    if (keywords && keywords.propertyIsEnumerable(word))\n      return \"keyword\";\n    if (functions && functions.propertyIsEnumerable(word)) {\n      state.beforeParams = true;\n      return \"keyword\";\n    }\n    return null;\n  }\n}\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\nfunction tokenUnparsed(stream, state) {\n  var maybeEnd = 0, ch;\n  while (ch = stream.next()) {\n    if (ch == \"#\" && maybeEnd == 2) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    if (ch == \"]\")\n      maybeEnd++;\n    else if (ch != \" \")\n      maybeEnd = 0;\n  }\n  return \"meta\";\n}\nexport const tcl = {\n  name: \"tcl\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      beforeParams: false,\n      inParams: false\n    };\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  },\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,WAAW,KAAK;AACvB,MAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,EAAG,KAAI,MAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AACA,IAAI,WAAW,WAAW,6zBAYO;AACjC,IAAI,YAAY,WAAW,gEAAgE;AAC3F,IAAI,iBAAiB;AACrB,SAAS,MAAM,QAAQ,OAAO,GAAG;AAC/B,QAAM,WAAW;AACjB,SAAO,EAAE,QAAQ,KAAK;AACxB;AACA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,eAAe,MAAM;AACzB,QAAM,eAAe;AACrB,MAAI,KAAK,OAAO,KAAK;AACrB,OAAK,MAAM,OAAO,MAAM,QAAQ,MAAM,UAAU;AAC9C,WAAO,MAAM,QAAQ,OAAO,YAAY,EAAE,CAAC;AAAA,EAC7C,WAAW,mBAAmB,KAAK,EAAE,GAAG;AACtC,QAAI,MAAM,OAAO,aAAc,OAAM,WAAW;AAAA,aACvC,MAAM,IAAK,OAAM,WAAW;AACrC,WAAO;AAAA,EACT,WAAW,KAAK,KAAK,EAAE,GAAG;AACxB,WAAO,SAAS,QAAQ;AACxB,WAAO;AAAA,EACT,WAAW,MAAM,KAAK;AACpB,QAAI,OAAO,IAAI,GAAG;AAChB,aAAO,MAAM,QAAQ,OAAO,YAAY;AAC1C,QAAI,MAAM,OAAO,OAAO,MAAM,UAAU;AACtC,aAAO,MAAM,QAAQ,OAAO,aAAa;AAC3C,WAAO,UAAU;AACjB,WAAO;AAAA,EACT,WAAW,MAAM,KAAK;AACpB,WAAO,OAAO,GAAG;AACjB,WAAO;AAAA,EACT,WAAW,MAAM,KAAK;AACpB,WAAO,SAAS,mBAAmB;AACnC,WAAO,SAAS,GAAG;AACnB,UAAM,eAAe;AACrB,WAAO;AAAA,EACT,WAAW,eAAe,KAAK,EAAE,GAAG;AAClC,WAAO,SAAS,cAAc;AAC9B,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS,sBAAsB;AACtC,QAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AACxC,QAAI,YAAY,SAAS,qBAAqB,IAAI;AAChD,aAAO;AACT,QAAI,aAAa,UAAU,qBAAqB,IAAI,GAAG;AACrD,YAAM,eAAe;AACrB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO,MAAM,MAAM;AACjC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,QAAQ,SAAS,CAAC,SAAS;AAC7B,cAAM;AACN;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,QAAI,IAAK,OAAM,WAAW;AAC1B,WAAO;AAAA,EACT;AACF;AACA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AACA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,WAAW,GAAG;AAClB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,YAAY,GAAG;AAC9B,YAAM,WAAW;AACjB;AAAA,IACF;AACA,QAAI,MAAM;AACR;AAAA,aACO,MAAM;AACb,iBAAW;AAAA,EACf;AACA,SAAO;AACT;AACO,IAAM,MAAM;AAAA,EACjB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAAA,EACA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF;", "names": []}