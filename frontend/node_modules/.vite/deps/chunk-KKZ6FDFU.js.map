{"version": 3, "sources": ["../../@lezer/css/dist/index.js", "../../@codemirror/lang-css/dist/index.js"], "sourcesContent": ["import { ExternalTokenizer, LRParser, LocalTokenGroup } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst descendantOp = 122,\n  Unit = 1,\n  identifier = 123,\n  callee = 124,\n  VariableName = 2,\n  queryIdentifier = 125,\n  queryVariableName = 3,\n  QueryCallee = 4;\n\n/* Hand-written tokenizers for CSS tokens that can't be\n   expressed by <PERSON><PERSON>'s built-in tokenizer. */\n\nconst space = [9, 10, 11, 12, 13, 32, 133, 160, 5760, 8192, 8193, 8194, 8195, 8196, 8197,\n               8198, 8199, 8200, 8201, 8202, 8232, 8233, 8239, 8287, 12288];\nconst colon = 58, parenL = 40, underscore = 95, bracketL = 91, dash = 45, period = 46,\n      hash = 35, percent = 37, ampersand = 38, backslash = 92, newline = 10, asterisk = 42;\n\nfunction isAlpha(ch) { return ch >= 65 && ch <= 90 || ch >= 97 && ch <= 122 || ch >= 161 }\n\nfunction isDigit(ch) { return ch >= 48 && ch <= 57 }\n\nfunction isHex(ch) { return isDigit(ch) || ch >= 97 && ch <= 102 || ch >= 65 && ch <= 70 }\n\nconst identifierTokens = (id, varName, callee) => (input, stack) => {\n  for (let inside = false, dashes = 0, i = 0;; i++) {\n    let {next} = input;\n    if (isAlpha(next) || next == dash || next == underscore || (inside && isDigit(next))) {\n      if (!inside && (next != dash || i > 0)) inside = true;\n      if (dashes === i && next == dash) dashes++;\n      input.advance();\n    } else if (next == backslash && input.peek(1) != newline) {\n      input.advance();\n      if (isHex(input.next)) {\n        do { input.advance(); } while (isHex(input.next))\n        if (input.next == 32) input.advance();\n      } else if (input.next > -1) {\n        input.advance();\n      }\n      inside = true;\n    } else {\n      if (inside) input.acceptToken(\n        dashes == 2 && stack.canShift(VariableName) ? varName : next == parenL ? callee : id\n      );\n      break\n    }\n  }\n};\n\nconst identifiers = new ExternalTokenizer(\n  identifierTokens(identifier, VariableName, callee)\n);\nconst queryIdentifiers = new ExternalTokenizer(\n  identifierTokens(queryIdentifier, queryVariableName, QueryCallee)\n);\n\nconst descendant = new ExternalTokenizer(input => {\n  if (space.includes(input.peek(-1))) {\n    let {next} = input;\n    if (isAlpha(next) || next == underscore || next == hash || next == period ||\n        next == asterisk || next == bracketL || next == colon && isAlpha(input.peek(1)) ||\n        next == dash || next == ampersand)\n      input.acceptToken(descendantOp);\n  }\n});\n\nconst unitToken = new ExternalTokenizer(input => {\n  if (!space.includes(input.peek(-1))) {\n    let {next} = input;\n    if (next == percent) { input.advance(); input.acceptToken(Unit); }\n    if (isAlpha(next)) {\n      do { input.advance(); } while (isAlpha(input.next) || isDigit(input.next))\n      input.acceptToken(Unit);\n    }\n  }\n});\n\nconst cssHighlighting = styleTags({\n  \"AtKeyword import charset namespace keyframes media supports\": tags.definitionKeyword,\n  \"from to selector\": tags.keyword,\n  NamespaceName: tags.namespace,\n  KeyframeName: tags.labelName,\n  KeyframeRangeName: tags.operatorKeyword,\n  TagName: tags.tagName,\n  ClassName: tags.className,\n  PseudoClassName: tags.constant(tags.className),\n  IdName: tags.labelName,\n  \"FeatureName PropertyName\": tags.propertyName,\n  AttributeName: tags.attributeName,\n  NumberLiteral: tags.number,\n  KeywordQuery: tags.keyword,\n  UnaryQueryOp: tags.operatorKeyword,\n  \"CallTag ValueName\": tags.atom,\n  VariableName: tags.variableName,\n  Callee: tags.operatorKeyword,\n  Unit: tags.unit,\n  \"UniversalSelector NestingSelector\": tags.definitionOperator,\n  \"MatchOp CompareOp\": tags.compareOperator,\n  \"ChildOp SiblingOp, LogicOp\": tags.logicOperator,\n  BinOp: tags.arithmeticOperator,\n  Important: tags.modifier,\n  Comment: tags.blockComment,\n  ColorLiteral: tags.color,\n  \"ParenthesizedContent StringLiteral\": tags.string,\n  \":\": tags.punctuation,\n  \"PseudoOp #\": tags.derefOperator,\n  \"; ,\": tags.separator,\n  \"( )\": tags.paren,\n  \"[ ]\": tags.squareBracket,\n  \"{ }\": tags.brace\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_callee = {__proto__:null,lang:38, \"nth-child\":38, \"nth-last-child\":38, \"nth-of-type\":38, \"nth-last-of-type\":38, dir:38, \"host-context\":38, if:84, url:124, \"url-prefix\":124, domain:124, regexp:124};\nconst spec_queryIdentifier = {__proto__:null,or:98, and:98, not:106, only:106, layer:170};\nconst spec_QueryCallee = {__proto__:null,selector:112, layer:166};\nconst spec_AtKeyword = {__proto__:null,\"@import\":162, \"@media\":174, \"@charset\":178, \"@namespace\":182, \"@keyframes\":188, \"@supports\":200, \"@scope\":204};\nconst spec_identifier = {__proto__:null,to:207};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"EbQYQdOOO#qQdOOP#xO`OOOOQP'#Cf'#CfOOQP'#Ce'#CeO#}QdO'#ChO$nQaO'#CcO$xQdO'#CkO%TQdO'#DpO%YQdO'#DrO%_QdO'#DuO%_QdO'#DxOOQP'#FV'#FVO&eQhO'#EhOOQS'#FU'#FUOOQS'#Ek'#EkQYQdOOO&lQdO'#EOO&PQhO'#EUO&lQdO'#EWO'aQdO'#EYO'lQdO'#E]O'tQhO'#EcO(VQdO'#EeO(bQaO'#CfO)VQ`O'#D{O)[Q`O'#F`O)gQdO'#F`QOQ`OOP)qO&jO'#CaPOOO)C@t)C@tOOQP'#Cj'#CjOOQP,59S,59SO#}QdO,59SO)|QdO,59VO%TQdO,5:[O%YQdO,5:^O%_QdO,5:aO%_QdO,5:cO%_QdO,5:dO%_QdO'#ErO*XQ`O,58}O*aQdO'#DzOOQS,58},58}OOQP'#Cn'#CnOOQO'#Dn'#DnOOQP,59V,59VO*hQ`O,59VO*mQ`O,59VOOQP'#Dq'#DqOOQP,5:[,5:[OOQO'#Ds'#DsO*rQpO,5:^O+]QaO,5:aO+sQaO,5:dOOQW'#DZ'#DZO,ZQhO'#DdO,xQhO'#FaO'tQhO'#DbO-WQ`O'#DhOOQW'#F['#F[O-]Q`O,5;SO-eQ`O'#DeOOQS-E8i-E8iOOQ['#Cs'#CsO-jQdO'#CtO.QQdO'#CzO.hQdO'#C}O/OQ!pO'#DPO1RQ!jO,5:jOOQO'#DU'#DUO*mQ`O'#DTO1cQ!nO'#FXO3`Q`O'#DVO3eQ`O'#DkOOQ['#FX'#FXO-`Q`O,5:pO3jQ!bO,5:rOOQS'#E['#E[O3rQ`O,5:tO3wQdO,5:tOOQO'#E_'#E_O4PQ`O,5:wO4UQhO,5:}O%_QdO'#DgOOQS,5;P,5;PO-eQ`O,5;PO4^QdO,5;PO4fQdO,5:gO4vQdO'#EtO5TQ`O,5;zO5TQ`O,5;zPOOO'#Ej'#EjP5`O&jO,58{POOO,58{,58{OOQP1G.n1G.nOOQP1G.q1G.qO*hQ`O1G.qO*mQ`O1G.qOOQP1G/v1G/vO5kQpO1G/xO5sQaO1G/{O6ZQaO1G/}O6qQaO1G0OO7XQaO,5;^OOQO-E8p-E8pOOQS1G.i1G.iO7cQ`O,5:fO7hQdO'#DoO7oQdO'#CrOOQP1G/x1G/xO&lQdO1G/xO7vQ!jO'#DZO8UQ!bO,59vO8^QhO,5:OOOQO'#F]'#F]O8XQ!bO,59zO'tQhO,59xO8fQhO'#EvO8sQ`O,5;{O9OQhO,59|O9uQhO'#DiOOQW,5:S,5:SOOQS1G0n1G0nOOQW,5:P,5:PO9|Q!fO'#FYOOQS'#FY'#FYOOQS'#Em'#EmO;^QdO,59`OOQ[,59`,59`O;tQdO,59fOOQ[,59f,59fO<[QdO,59iOOQ[,59i,59iOOQ[,59k,59kO&lQdO,59mO<rQhO'#EQOOQW'#EQ'#EQO=WQ`O1G0UO1[QhO1G0UOOQ[,59o,59oO'tQhO'#DXOOQ[,59q,59qO=]Q#tO,5:VOOQS1G0[1G0[OOQS1G0^1G0^OOQS1G0`1G0`O=hQ`O1G0`O=mQdO'#E`OOQS1G0c1G0cOOQS1G0i1G0iO=xQaO,5:RO-`Q`O1G0kOOQS1G0k1G0kO-eQ`O1G0kO>PQ!fO1G0ROOQO1G0R1G0ROOQO,5;`,5;`O>gQdO,5;`OOQO-E8r-E8rO>tQ`O1G1fPOOO-E8h-E8hPOOO1G.g1G.gOOQP7+$]7+$]OOQP7+%d7+%dO&lQdO7+%dOOQS1G0Q1G0QO?PQaO'#F_O?ZQ`O,5:ZO?`Q!fO'#ElO@^QdO'#FWO@hQ`O,59^O@mQ!bO7+%dO&lQdO1G/bO@uQhO1G/fOOQW1G/j1G/jOOQW1G/d1G/dOAWQhO,5;bOOQO-E8t-E8tOAfQhO'#DZOAtQhO'#F^OBPQ`O'#F^OBUQ`O,5:TOOQS-E8k-E8kOOQ[1G.z1G.zOOQ[1G/Q1G/QOOQ[1G/T1G/TOOQ[1G/X1G/XOBZQdO,5:lOOQS7+%p7+%pOB`Q`O7+%pOBeQhO'#DYOBmQ`O,59sO'tQhO,59sOOQ[1G/q1G/qOBuQ`O1G/qOOQS7+%z7+%zOBzQbO'#DPOOQO'#Eb'#EbOCYQ`O'#EaOOQO'#Ea'#EaOCeQ`O'#EwOCmQdO,5:zOOQS,5:z,5:zOOQ[1G/m1G/mOOQS7+&V7+&VO-`Q`O7+&VOCxQ!fO'#EsO&lQdO'#EsOEPQdO7+%mOOQO7+%m7+%mOOQO1G0z1G0zOEdQ!bO<<IOOElQdO'#EqOEvQ`O,5;yOOQP1G/u1G/uOOQS-E8j-E8jOFOQdO'#EpOFYQ`O,5;rOOQ]1G.x1G.xOOQP<<IO<<IOOFbQdO7+$|OOQO'#D]'#D]OFiQ!bO7+%QOFqQhO'#EoOF{Q`O,5;xO&lQdO,5;xOOQW1G/o1G/oOOQO'#ES'#ESOGTQ`O1G0WOOQS<<I[<<I[O&lQdO,59tOGnQhO1G/_OOQ[1G/_1G/_OGuQ`O1G/_OOQW-E8l-E8lOOQ[7+%]7+%]OOQO,5:{,5:{O=pQdO'#ExOCeQ`O,5;cOOQS,5;c,5;cOOQS-E8u-E8uOOQS1G0f1G0fOOQS<<Iq<<IqOG}Q!fO,5;_OOQS-E8q-E8qOOQO<<IX<<IXOOQPAN>jAN>jOIUQaO,5;]OOQO-E8o-E8oOI`QdO,5;[OOQO-E8n-E8nOOQW<<Hh<<HhOOQW<<Hl<<HlOIjQhO<<HlOI{QhO,5;ZOJWQ`O,5;ZOOQO-E8m-E8mOJ]QdO1G1dOBZQdO'#EuOJgQ`O7+%rOOQW7+%r7+%rOJoQ!bO1G/`OOQ[7+$y7+$yOJzQhO7+$yPKRQ`O'#EnOOQO,5;d,5;dOOQO-E8v-E8vOOQS1G0}1G0}OKWQ`OAN>WO&lQdO1G0uOK]Q`O7+'OOOQO,5;a,5;aOOQO-E8s-E8sOOQW<<I^<<I^OOQ[<<He<<HePOQW,5;Y,5;YOOQWG23rG23rOKeQdO7+&a\",\n  stateData: \"Kx~O#sOS#tQQ~OW[OZ[O]TO`VOaVOi]OjWOmXO!jYO!mZO!saO!ybO!{cO!}dO#QeO#WfO#YgO#oRO~OQiOW[OZ[O]TO`VOaVOi]OjWOmXO!jYO!mZO!saO!ybO!{cO!}dO#QeO#WfO#YgO#ohO~O#m$SP~P!dO#tmO~O#ooO~O]qO`rOarOjsOmtO!juO!mwO#nvO~OpzO!^xO~P$SOc!QO#o|O#p}O~O#o!RO~O#o!TO~OW[OZ[O]TO`VOaVOjWOmXO!jYO!mZO#oRO~OS!]Oe!YO!V![O!Y!`O#q!XOp$TP~Ok$TP~P&POQ!jOe!cOm!dOp!eOr!mOt!mOz!kO!`!lO#o!bO#p!hO#}!fO~Ot!qO!`!lO#o!pO~Ot!sO#o!sO~OS!]Oe!YO!V![O!Y!`O#q!XO~Oe!vOpzO#Z!xO~O]YX`YX`!pXaYXjYXmYXpYX!^YX!jYX!mYX#nYX~O`!zO~Ok!{O#m$SXo$SX~O#m$SXo$SX~P!dO#u#OO#v#OO#w#QO~Oc#UO#o|O#p}O~OpzO!^xO~Oo$SP~P!dOe#`O~Oe#aO~Ol#bO!h#cO~O]qO`rOarOjsOmtO~Op!ia!^!ia!j!ia!m!ia#n!iad!ia~P*zOp!la!^!la!j!la!m!la#n!lad!la~P*zOR#gOS!]Oe!YOr#gOt#gO!V![O!Y!`O#q#dO#}!fO~O!R#iO!^#jOk$TXp$TX~Oe#mO~Ok#oOpzO~Oe!vO~O]#rO`#rOd#uOi#rOj#rOk#rO~P&lO]#rO`#rOi#rOj#rOk#rOl#wO~P&lO]#rO`#rOi#rOj#rOk#rOo#yO~P&lOP#zOSsXesXksXvsX!VsX!YsX!usX!wsX#qsX!TsXQsX]sX`sXdsXisXjsXmsXpsXrsXtsXzsX!`sX#osX#psX#}sXlsXosX!^sX!qsX#msX~Ov#{O!u#|O!w#}Ok$TP~P'tOe#aOS#{Xk#{Xv#{X!V#{X!Y#{X!u#{X!w#{X#q#{XQ#{X]#{X`#{Xd#{Xi#{Xj#{Xm#{Xp#{Xr#{Xt#{Xz#{X!`#{X#o#{X#p#{X#}#{Xl#{Xo#{X!^#{X!q#{X#m#{X~Oe$RO~Oe$TO~Ok$VOv#{O~Ok$WO~Ot$XO!`!lO~Op$YO~OpzO!R#iO~OpzO#Z$`O~O!q$bOk!oa#m!oao!oa~P&lOk#hX#m#hXo#hX~P!dOk!{O#m$Sao$Sa~O#u#OO#v#OO#w$hO~Ol$jO!h$kO~Op!ii!^!ii!j!ii!m!ii#n!iid!ii~P*zOp!ki!^!ki!j!ki!m!ki#n!kid!ki~P*zOp!li!^!li!j!li!m!li#n!lid!li~P*zOp#fa!^#fa~P$SOo$lO~Od$RP~P%_Od#zP~P&lO`!PXd}X!R}X!T!PX~O`$sO!T$tO~Od$uO!R#iO~Ok#jXp#jX!^#jX~P'tO!^#jOk$Tap$Ta~O!R#iOk!Uap!Ua!^!Uad!Ua`!Ua~OS!]Oe!YO!V![O!Y!`O#q$yO~Od$QP~P9dOv#{OQ#|X]#|X`#|Xd#|Xe#|Xi#|Xj#|Xk#|Xm#|Xp#|Xr#|Xt#|Xz#|X!`#|X#o#|X#p#|X#}#|Xl#|Xo#|X~O]#rO`#rOd%OOi#rOj#rOk#rO~P&lO]#rO`#rOi#rOj#rOk#rOl%PO~P&lO]#rO`#rOi#rOj#rOk#rOo%QO~P&lOe%SOS!tXk!tX!V!tX!Y!tX#q!tX~Ok%TO~Od%YOt%ZO!a%ZO~Ok%[O~Oo%cO#o%^O#}%]O~Od%dO~P$SOv#{O!^%hO!q%jOk!oi#m!oio!oi~P&lOk#ha#m#hao#ha~P!dOk!{O#m$Sio$Si~O!^%mOd$RX~P$SOd%oO~Ov#{OQ#`Xd#`Xe#`Xm#`Xp#`Xr#`Xt#`Xz#`X!^#`X!`#`X#o#`X#p#`X#}#`X~O!^%qOd#zX~P&lOd%sO~Ol%tOv#{O~OR#gOr#gOt#gO#q%vO#}!fO~O!R#iOk#jap#ja!^#ja~O`!PXd}X!R}X!^}X~O!R#iO!^%xOd$QX~O`%zO~Od%{O~O#o%|O~Ok&OO~O`&PO!R#iO~Od&ROk&QO~Od&UO~OP#zOpsX!^sXdsX~O#}%]Op#TX!^#TX~OpzO!^&WO~Oo&[O#o%^O#}%]O~Ov#{OQ#gXe#gXk#gXm#gXp#gXr#gXt#gXz#gX!^#gX!`#gX!q#gX#m#gX#o#gX#p#gX#}#gXo#gX~O!^%hO!q&`Ok!oq#m!oqo!oq~P&lOl&aOv#{O~Od#eX!^#eX~P%_O!^%mOd$Ra~Od#dX!^#dX~P&lO!^%qOd#za~Od&fO~P&lOd&gO!T&hO~Od#cX!^#cX~P9dO!^%xOd$Qa~O]&mOd&oO~OS#bae#ba!V#ba!Y#ba#q#ba~Od&qO~PG]Od&qOk&rO~Ov#{OQ#gae#gak#gam#gap#gar#gat#gaz#ga!^#ga!`#ga!q#ga#m#ga#o#ga#p#ga#}#gao#ga~Od#ea!^#ea~P$SOd#da!^#da~P&lOR#gOr#gOt#gO#q%vO#}%]O~O!R#iOd#ca!^#ca~O`&xO~O!^%xOd$Qi~P&lO]&mOd&|O~Ov#{Od|ik|i~Od&}O~PG]Ok'OO~Od'PO~O!^%xOd$Qq~Od#cq!^#cq~P&lO#s!a#t#}]#}v!m~\",\n  goto: \"2h$UPPPPP$VP$YP$c$uP$cP%X$cPP%_PPP%e%o%oPPPPP%oPP%oP&]P%oP%o'W%oP't'w'}'}(^'}P'}P'}P'}'}P(m'}(yP(|PP)p)v$c)|$c*SP$cP$c$cP*Y*{+YP$YP+aP+dP$YP$YP$YP+j$YP+m+p+s+z$YP$YPP$YP,P,V,f,|-[-b-l-r-x.O.U.`.f.l.rPPPPPPPPPPP.x/R/w/z0|P1U1u2O2R2U2[RnQ_^OP`kz!{$dq[OPYZ`kuvwxz!v!{#`$d%mqSOPYZ`kuvwxz!v!{#`$d%mQpTR#RqQ!OVR#SrQ#S!QS$Q!i!jR$i#U!V!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'Q!U!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QU#g!Y$t&hU%`$Y%b&WR&V%_!V!iac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QR$S!kQ%W$RR&S%Xk!^]bf!Y![!g#i#j#m$P$R%X%xQ#e!YQ${#mQ%w$tQ&j%xR&w&hQ!ygQ#p!`Q$^!xR%f$`R#n!]!U!mac!c!d!e!z#a#c#t#v#x#{$a$k$p$s%h%i%q%u%z&P&d&l&x'QQ!qdR$X!rQ!PVR#TrQ#S!PR$i#TQ!SWR#VsQ!UXR#WtQ{UQ!wgQ#^yQ#o!_Q$U!nQ$[!uQ$_!yQ%e$^Q&Y%aQ&]%fR&v&XSjPzQ!}kQ$c!{R%k$dZiPkz!{$dR$P!gQ%}%SR&z&mR!rdR!teR$Z!tS%a$Y%bR&t&WV%_$Y%b&WQ#PmR$g#PQ`OSkPzU!a`k$dR$d!{Q$p#aY%p$p%u&d&l'QQ%u$sQ&d%qQ&l%zR'Q&xQ#t!cQ#v!dQ#x!eV$}#t#v#xQ%X$RR&T%XQ%y$zS&k%y&yR&y&lQ%r$pR&e%rQ%n$mR&c%nQyUR#]yQ%i$aR&_%iQ!|jS$e!|$fR$f!}Q&n%}R&{&nQ#k!ZR$x#kQ%b$YR&Z%bQ&X%aR&u&X__OP`kz!{$d^UOP`kz!{$dQ!VYQ!WZQ#XuQ#YvQ#ZwQ#[xQ$]!vQ$m#`R&b%mR$q#aQ!gaQ!oc[#q!c!d!e#t#v#xQ$a!zd$o#a$p$s%q%u%z&d&l&x'QQ$r#cQ%R#{S%g$a%iQ%l$kQ&^%hR&p&P]#s!c!d!e#t#v#xW!Z]b!g$PQ!ufQ#f!YQ#l![Q$v#iQ$w#jQ$z#mS%V$R%XR&i%xQ#h!YQ%w$tR&w&hR$|#mR$n#`QlPR#_zQ!_]Q!nbQ$O!gR%U$P\",\n  nodeNames: \"⚠ Unit VariableName VariableName QueryCallee Comment StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector ClassSelector . ClassName PseudoClassSelector : :: PseudoClassName PseudoClassName ) ( ArgList ValueName ParenthesizedValue AtKeyword # ; ] [ BracketedValue } { BracedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp CallExpression Callee IfExpression if ArgList IfBranch KeywordQuery FeatureQuery FeatureName BinaryQuery LogicOp ComparisonQuery CompareOp UnaryQuery UnaryQueryOp ParenthesizedQuery SelectorQuery selector ParenthesizedSelector CallQuery ArgList , CallLiteral CallTag ParenthesizedContent PseudoClassName ArgList IdSelector IdName AttributeSelector AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp Block Declaration PropertyName Important ImportStatement import Layer layer LayerName layer MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList KeyframeSelector KeyframeRangeName SupportsStatement supports ScopeStatement scope to AtRule Styles\",\n  maxTerm: 143,\n  nodeProps: [\n    [\"isolate\", -2,5,36,\"\"],\n    [\"openedBy\", 20,\"(\",28,\"[\",31,\"{\"],\n    [\"closedBy\", 21,\")\",29,\"]\",32,\"}\"]\n  ],\n  propSources: [cssHighlighting],\n  skippedNodes: [0,5,106],\n  repeatNodeCount: 15,\n  tokenData: \"JQ~R!YOX$qX^%i^p$qpq%iqr({rs-ust/itu6Wuv$qvw7Qwx7cxy9Qyz9cz{9h{|:R|}>t}!O?V!O!P?t!P!Q@]!Q![AU![!]BP!]!^B{!^!_C^!_!`DY!`!aDm!a!b$q!b!cEn!c!}$q!}#OG{#O#P$q#P#QH^#Q#R6W#R#o$q#o#pHo#p#q6W#q#rIQ#r#sIc#s#y$q#y#z%i#z$f$q$f$g%i$g#BY$q#BY#BZ%i#BZ$IS$q$IS$I_%i$I_$I|$q$I|$JO%i$JO$JT$q$JT$JU%i$JU$KV$q$KV$KW%i$KW&FU$q&FU&FV%i&FV;'S$q;'S;=`Iz<%lO$q`$tSOy%Qz;'S%Q;'S;=`%c<%lO%Q`%VS!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Q`%fP;=`<%l%Q~%nh#s~OX%QX^'Y^p%Qpq'Yqy%Qz#y%Q#y#z'Y#z$f%Q$f$g'Y$g#BY%Q#BY#BZ'Y#BZ$IS%Q$IS$I_'Y$I_$I|%Q$I|$JO'Y$JO$JT%Q$JT$JU'Y$JU$KV%Q$KV$KW'Y$KW&FU%Q&FU&FV'Y&FV;'S%Q;'S;=`%c<%lO%Q~'ah#s~!a`OX%QX^'Y^p%Qpq'Yqy%Qz#y%Q#y#z'Y#z$f%Q$f$g'Y$g#BY%Q#BY#BZ'Y#BZ$IS%Q$IS$I_'Y$I_$I|%Q$I|$JO'Y$JO$JT%Q$JT$JU'Y$JU$KV%Q$KV$KW'Y$KW&FU%Q&FU&FV'Y&FV;'S%Q;'S;=`%c<%lO%Qj)OUOy%Qz#]%Q#]#^)b#^;'S%Q;'S;=`%c<%lO%Qj)gU!a`Oy%Qz#a%Q#a#b)y#b;'S%Q;'S;=`%c<%lO%Qj*OU!a`Oy%Qz#d%Q#d#e*b#e;'S%Q;'S;=`%c<%lO%Qj*gU!a`Oy%Qz#c%Q#c#d*y#d;'S%Q;'S;=`%c<%lO%Qj+OU!a`Oy%Qz#f%Q#f#g+b#g;'S%Q;'S;=`%c<%lO%Qj+gU!a`Oy%Qz#h%Q#h#i+y#i;'S%Q;'S;=`%c<%lO%Qj,OU!a`Oy%Qz#T%Q#T#U,b#U;'S%Q;'S;=`%c<%lO%Qj,gU!a`Oy%Qz#b%Q#b#c,y#c;'S%Q;'S;=`%c<%lO%Qj-OU!a`Oy%Qz#h%Q#h#i-b#i;'S%Q;'S;=`%c<%lO%Qj-iS!qY!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Q~-xWOY-uZr-urs.bs#O-u#O#P.g#P;'S-u;'S;=`/c<%lO-u~.gOt~~.jRO;'S-u;'S;=`.s;=`O-u~.vXOY-uZr-urs.bs#O-u#O#P.g#P;'S-u;'S;=`/c;=`<%l-u<%lO-u~/fP;=`<%l-uj/nYjYOy%Qz!Q%Q!Q![0^![!c%Q!c!i0^!i#T%Q#T#Z0^#Z;'S%Q;'S;=`%c<%lO%Qj0cY!a`Oy%Qz!Q%Q!Q![1R![!c%Q!c!i1R!i#T%Q#T#Z1R#Z;'S%Q;'S;=`%c<%lO%Qj1WY!a`Oy%Qz!Q%Q!Q![1v![!c%Q!c!i1v!i#T%Q#T#Z1v#Z;'S%Q;'S;=`%c<%lO%Qj1}YrY!a`Oy%Qz!Q%Q!Q![2m![!c%Q!c!i2m!i#T%Q#T#Z2m#Z;'S%Q;'S;=`%c<%lO%Qj2tYrY!a`Oy%Qz!Q%Q!Q![3d![!c%Q!c!i3d!i#T%Q#T#Z3d#Z;'S%Q;'S;=`%c<%lO%Qj3iY!a`Oy%Qz!Q%Q!Q![4X![!c%Q!c!i4X!i#T%Q#T#Z4X#Z;'S%Q;'S;=`%c<%lO%Qj4`YrY!a`Oy%Qz!Q%Q!Q![5O![!c%Q!c!i5O!i#T%Q#T#Z5O#Z;'S%Q;'S;=`%c<%lO%Qj5TY!a`Oy%Qz!Q%Q!Q![5s![!c%Q!c!i5s!i#T%Q#T#Z5s#Z;'S%Q;'S;=`%c<%lO%Qj5zSrY!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Qd6ZUOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Qd6tS!hS!a`Oy%Qz;'S%Q;'S;=`%c<%lO%Qb7VSZQOy%Qz;'S%Q;'S;=`%c<%lO%Q~7fWOY7cZw7cwx.bx#O7c#O#P8O#P;'S7c;'S;=`8z<%lO7c~8RRO;'S7c;'S;=`8[;=`O7c~8_XOY7cZw7cwx.bx#O7c#O#P8O#P;'S7c;'S;=`8z;=`<%l7c<%lO7c~8}P;=`<%l7cj9VSeYOy%Qz;'S%Q;'S;=`%c<%lO%Q~9hOd~n9oUWQvWOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Qj:YWvW!mQOy%Qz!O%Q!O!P:r!P!Q%Q!Q![=w![;'S%Q;'S;=`%c<%lO%Qj:wU!a`Oy%Qz!Q%Q!Q![;Z![;'S%Q;'S;=`%c<%lO%Qj;bY!a`#}YOy%Qz!Q%Q!Q![;Z![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%Qj<VY!a`Oy%Qz{%Q{|<u|}%Q}!O<u!O!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj<zU!a`Oy%Qz!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj=eU!a`#}YOy%Qz!Q%Q!Q![=^![;'S%Q;'S;=`%c<%lO%Qj>O[!a`#}YOy%Qz!O%Q!O!P;Z!P!Q%Q!Q![=w![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%Qj>yS!^YOy%Qz;'S%Q;'S;=`%c<%lO%Qj?[WvWOy%Qz!O%Q!O!P:r!P!Q%Q!Q![=w![;'S%Q;'S;=`%c<%lO%Qj?yU]YOy%Qz!Q%Q!Q![;Z![;'S%Q;'S;=`%c<%lO%Q~@bTvWOy%Qz{@q{;'S%Q;'S;=`%c<%lO%Q~@xS!a`#t~Oy%Qz;'S%Q;'S;=`%c<%lO%QjAZ[#}YOy%Qz!O%Q!O!P;Z!P!Q%Q!Q![=w![!g%Q!g!h<Q!h#X%Q#X#Y<Q#Y;'S%Q;'S;=`%c<%lO%QjBUU`YOy%Qz![%Q![!]Bh!];'S%Q;'S;=`%c<%lO%QbBoSaQ!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QjCQSkYOy%Qz;'S%Q;'S;=`%c<%lO%QhCcU!TWOy%Qz!_%Q!_!`Cu!`;'S%Q;'S;=`%c<%lO%QhC|S!TW!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QlDaS!TW!hSOy%Qz;'S%Q;'S;=`%c<%lO%QjDtV!jQ!TWOy%Qz!_%Q!_!`Cu!`!aEZ!a;'S%Q;'S;=`%c<%lO%QbEbS!jQ!a`Oy%Qz;'S%Q;'S;=`%c<%lO%QjEqYOy%Qz}%Q}!OFa!O!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjFfW!a`Oy%Qz!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjGV[iY!a`Oy%Qz}%Q}!OGO!O!Q%Q!Q![GO![!c%Q!c!}GO!}#T%Q#T#oGO#o;'S%Q;'S;=`%c<%lO%QjHQSmYOy%Qz;'S%Q;'S;=`%c<%lO%QnHcSl^Oy%Qz;'S%Q;'S;=`%c<%lO%QjHtSpYOy%Qz;'S%Q;'S;=`%c<%lO%QjIVSoYOy%Qz;'S%Q;'S;=`%c<%lO%QfIhU!mQOy%Qz!_%Q!_!`6m!`;'S%Q;'S;=`%c<%lO%Q`I}P;=`<%l$q\",\n  tokenizers: [descendant, unitToken, identifiers, queryIdentifiers, 1, 2, 3, 4, new LocalTokenGroup(\"m~RRYZ[z{a~~g~aO#v~~dP!P!Qg~lO#w~~\", 28, 129)],\n  topRules: {\"StyleSheet\":[0,6],\"Styles\":[1,105]},\n  specialized: [{term: 124, get: (value) => spec_callee[value] || -1},{term: 125, get: (value) => spec_queryIdentifier[value] || -1},{term: 4, get: (value) => spec_QueryCallee[value] || -1},{term: 25, get: (value) => spec_AtKeyword[value] || -1},{term: 123, get: (value) => spec_identifier[value] || -1}],\n  tokenPrec: 1963\n});\n\nexport { parser };\n", "import { parser } from '@lezer/css';\nimport { syntax<PERSON>ree, LRLanguage, indentNodeProp, continuedIndent, foldNodeProp, foldInside, LanguageSupport } from '@codemirror/language';\nimport { NodeWeakMap, IterMode } from '@lezer/common';\n\nlet _properties = null;\nfunction properties() {\n    if (!_properties && typeof document == \"object\" && document.body) {\n        let { style } = document.body, names = [], seen = new Set;\n        for (let prop in style)\n            if (prop != \"cssText\" && prop != \"cssFloat\") {\n                if (typeof style[prop] == \"string\") {\n                    if (/[A-Z]/.test(prop))\n                        prop = prop.replace(/[A-Z]/g, ch => \"-\" + ch.toLowerCase());\n                    if (!seen.has(prop)) {\n                        names.push(prop);\n                        seen.add(prop);\n                    }\n                }\n            }\n        _properties = names.sort().map(name => ({ type: \"property\", label: name, apply: name + \": \" }));\n    }\n    return _properties || [];\n}\nconst pseudoClasses = /*@__PURE__*/[\n    \"active\", \"after\", \"any-link\", \"autofill\", \"backdrop\", \"before\",\n    \"checked\", \"cue\", \"default\", \"defined\", \"disabled\", \"empty\",\n    \"enabled\", \"file-selector-button\", \"first\", \"first-child\",\n    \"first-letter\", \"first-line\", \"first-of-type\", \"focus\",\n    \"focus-visible\", \"focus-within\", \"fullscreen\", \"has\", \"host\",\n    \"host-context\", \"hover\", \"in-range\", \"indeterminate\", \"invalid\",\n    \"is\", \"lang\", \"last-child\", \"last-of-type\", \"left\", \"link\", \"marker\",\n    \"modal\", \"not\", \"nth-child\", \"nth-last-child\", \"nth-last-of-type\",\n    \"nth-of-type\", \"only-child\", \"only-of-type\", \"optional\", \"out-of-range\",\n    \"part\", \"placeholder\", \"placeholder-shown\", \"read-only\", \"read-write\",\n    \"required\", \"right\", \"root\", \"scope\", \"selection\", \"slotted\", \"target\",\n    \"target-text\", \"valid\", \"visited\", \"where\"\n].map(name => ({ type: \"class\", label: name }));\nconst values = /*@__PURE__*/[\n    \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"after-white-space\",\n    \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\", \"always\",\n    \"antialiased\", \"appworkspace\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\",\n    \"avoid-page\", \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\",\n    \"bidi-override\", \"blink\", \"block\", \"block-axis\", \"bold\", \"bolder\", \"border\", \"border-box\",\n    \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"bullets\", \"button\", \"button-bevel\",\n    \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"capitalize\",\n    \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\", \"cell\", \"center\", \"checkbox\", \"circle\",\n    \"cjk-decimal\", \"clear\", \"clip\", \"close-quote\", \"col-resize\", \"collapse\", \"color\", \"color-burn\",\n    \"color-dodge\", \"column\", \"column-reverse\", \"compact\", \"condensed\", \"contain\", \"content\",\n    \"contents\", \"content-box\", \"context-menu\", \"continuous\", \"copy\", \"counter\", \"counters\", \"cover\",\n    \"crop\", \"cross\", \"crosshair\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n    \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\", \"destination-in\",\n    \"destination-out\", \"destination-over\", \"difference\", \"disc\", \"discard\", \"disclosure-closed\",\n    \"disclosure-open\", \"document\", \"dot-dash\", \"dot-dot-dash\", \"dotted\", \"double\", \"down\", \"e-resize\",\n    \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\", \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\",\n    \"ethiopic-abegede-gez\", \"ethiopic-halehame-aa-er\", \"ethiopic-halehame-gez\", \"ew-resize\", \"exclusion\",\n    \"expanded\", \"extends\", \"extra-condensed\", \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\",\n    \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\", \"forwards\", \"from\",\n    \"geometricPrecision\", \"graytext\", \"grid\", \"groove\", \"hand\", \"hard-light\", \"help\", \"hidden\", \"hide\",\n    \"higher\", \"highlight\", \"highlighttext\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"icon\", \"ignore\",\n    \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\", \"infobackground\", \"infotext\",\n    \"inherit\", \"initial\", \"inline\", \"inline-axis\", \"inline-block\", \"inline-flex\", \"inline-grid\",\n    \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\", \"italic\", \"justify\", \"keep-all\",\n    \"landscape\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\", \"line-through\", \"linear\",\n    \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\", \"local\", \"logical\", \"loud\", \"lower\",\n    \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\", \"lowercase\", \"ltr\", \"luminosity\", \"manipulation\",\n    \"match\", \"matrix\", \"matrix3d\", \"medium\", \"menu\", \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n    \"mix\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"n-resize\", \"narrower\",\n    \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\", \"no-open-quote\", \"no-repeat\", \"none\",\n    \"normal\", \"not-allowed\", \"nowrap\", \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\",\n    \"oblique\", \"opacity\", \"open-quote\", \"optimizeLegibility\", \"optimizeSpeed\", \"outset\", \"outside\",\n    \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\", \"painted\", \"page\", \"paused\",\n    \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\", \"pointer\", \"polygon\", \"portrait\",\n    \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\", \"progress\", \"push-button\", \"radial-gradient\", \"radio\",\n    \"read-only\", \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\", \"relative\", \"repeat\",\n    \"repeating-linear-gradient\", \"repeating-radial-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n    \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\", \"rotateZ\", \"round\",\n    \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\", \"s-resize\", \"sans-serif\", \"saturation\",\n    \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\", \"scroll\", \"scrollbar\", \"scroll-position\",\n    \"se-resize\", \"self-start\", \"self-end\", \"semi-condensed\", \"semi-expanded\", \"separate\", \"serif\", \"show\",\n    \"single\", \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n    \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\", \"small\", \"small-caps\",\n    \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"source-atop\", \"source-in\", \"source-out\",\n    \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\", \"start\",\n    \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\", \"subpixel-antialiased\", \"svg_masks\",\n    \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\", \"table-caption\", \"table-cell\",\n    \"table-column\", \"table-column-group\", \"table-footer-group\", \"table-header-group\", \"table-row\",\n    \"table-row-group\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thick\", \"thin\",\n    \"threeddarkshadow\", \"threedface\", \"threedhighlight\", \"threedlightshadow\", \"threedshadow\", \"to\", \"top\",\n    \"transform\", \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\", \"transparent\",\n    \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\", \"upper-latin\",\n    \"uppercase\", \"url\", \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\",\n    \"visiblePainted\", \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\", \"window\", \"windowframe\",\n    \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\", \"xx-large\", \"xx-small\"\n].map(name => ({ type: \"keyword\", label: name })).concat(/*@__PURE__*/[\n    \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n    \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n    \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n    \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n    \"darkgray\", \"darkgreen\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n    \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n    \"darkslateblue\", \"darkslategray\", \"darkturquoise\", \"darkviolet\",\n    \"deeppink\", \"deepskyblue\", \"dimgray\", \"dodgerblue\", \"firebrick\",\n    \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n    \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n    \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n    \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n    \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightpink\",\n    \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\",\n    \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n    \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n    \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n    \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n    \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n    \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n    \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n    \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n    \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n    \"slateblue\", \"slategray\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n    \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n    \"whitesmoke\", \"yellow\", \"yellowgreen\"\n].map(name => ({ type: \"constant\", label: name })));\nconst tags = /*@__PURE__*/[\n    \"a\", \"abbr\", \"address\", \"article\", \"aside\", \"b\", \"bdi\", \"bdo\", \"blockquote\", \"body\",\n    \"br\", \"button\", \"canvas\", \"caption\", \"cite\", \"code\", \"col\", \"colgroup\", \"dd\", \"del\",\n    \"details\", \"dfn\", \"dialog\", \"div\", \"dl\", \"dt\", \"em\", \"figcaption\", \"figure\", \"footer\",\n    \"form\", \"header\", \"hgroup\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"hr\", \"html\", \"i\", \"iframe\",\n    \"img\", \"input\", \"ins\", \"kbd\", \"label\", \"legend\", \"li\", \"main\", \"meter\", \"nav\", \"ol\", \"output\",\n    \"p\", \"pre\", \"ruby\", \"section\", \"select\", \"small\", \"source\", \"span\", \"strong\", \"sub\", \"summary\",\n    \"sup\", \"table\", \"tbody\", \"td\", \"template\", \"textarea\", \"tfoot\", \"th\", \"thead\", \"tr\", \"u\", \"ul\"\n].map(name => ({ type: \"type\", label: name }));\nconst atRules = /*@__PURE__*/[\n    \"@charset\", \"@color-profile\", \"@container\", \"@counter-style\", \"@font-face\", \"@font-feature-values\",\n    \"@font-palette-values\", \"@import\", \"@keyframes\", \"@layer\", \"@media\", \"@namespace\", \"@page\",\n    \"@position-try\", \"@property\", \"@scope\", \"@starting-style\", \"@supports\", \"@view-transition\"\n].map(label => ({ type: \"keyword\", label }));\nconst identifier = /^(\\w[\\w-]*|-\\w[\\w-]*|)$/, variable = /^-(-[\\w-]*)?$/;\nfunction isVarArg(node, doc) {\n    var _a;\n    if (node.name == \"(\" || node.type.isError)\n        node = node.parent || node;\n    if (node.name != \"ArgList\")\n        return false;\n    let callee = (_a = node.parent) === null || _a === void 0 ? void 0 : _a.firstChild;\n    if ((callee === null || callee === void 0 ? void 0 : callee.name) != \"Callee\")\n        return false;\n    return doc.sliceString(callee.from, callee.to) == \"var\";\n}\nconst VariablesByNode = /*@__PURE__*/new NodeWeakMap();\nconst declSelector = [\"Declaration\"];\nfunction astTop(node) {\n    for (let cur = node;;) {\n        if (cur.type.isTop)\n            return cur;\n        if (!(cur = cur.parent))\n            return node;\n    }\n}\nfunction variableNames(doc, node, isVariable) {\n    if (node.to - node.from > 4096) {\n        let known = VariablesByNode.get(node);\n        if (known)\n            return known;\n        let result = [], seen = new Set, cursor = node.cursor(IterMode.IncludeAnonymous);\n        if (cursor.firstChild())\n            do {\n                for (let option of variableNames(doc, cursor.node, isVariable))\n                    if (!seen.has(option.label)) {\n                        seen.add(option.label);\n                        result.push(option);\n                    }\n            } while (cursor.nextSibling());\n        VariablesByNode.set(node, result);\n        return result;\n    }\n    else {\n        let result = [], seen = new Set;\n        node.cursor().iterate(node => {\n            var _a;\n            if (isVariable(node) && node.matchContext(declSelector) && ((_a = node.node.nextSibling) === null || _a === void 0 ? void 0 : _a.name) == \":\") {\n                let name = doc.sliceString(node.from, node.to);\n                if (!seen.has(name)) {\n                    seen.add(name);\n                    result.push({ label: name, type: \"variable\" });\n                }\n            }\n        });\n        return result;\n    }\n}\n/**\nCreate a completion source for a CSS dialect, providing a\npredicate for determining what kind of syntax node can act as a\ncompletable variable. This is used by language modes like Sass and\nLess to reuse this package's completion logic.\n*/\nconst defineCSSCompletionSource = (isVariable) => context => {\n    let { state, pos } = context, node = syntaxTree(state).resolveInner(pos, -1);\n    let isDash = node.type.isError && node.from == node.to - 1 && state.doc.sliceString(node.from, node.to) == \"-\";\n    if (node.name == \"PropertyName\" ||\n        (isDash || node.name == \"TagName\") && /^(Block|Styles)$/.test(node.resolve(node.to).name))\n        return { from: node.from, options: properties(), validFor: identifier };\n    if (node.name == \"ValueName\")\n        return { from: node.from, options: values, validFor: identifier };\n    if (node.name == \"PseudoClassName\")\n        return { from: node.from, options: pseudoClasses, validFor: identifier };\n    if (isVariable(node) || (context.explicit || isDash) && isVarArg(node, state.doc))\n        return { from: isVariable(node) || isDash ? node.from : pos,\n            options: variableNames(state.doc, astTop(node), isVariable),\n            validFor: variable };\n    if (node.name == \"TagName\") {\n        for (let { parent } = node; parent; parent = parent.parent)\n            if (parent.name == \"Block\")\n                return { from: node.from, options: properties(), validFor: identifier };\n        return { from: node.from, options: tags, validFor: identifier };\n    }\n    if (node.name == \"AtKeyword\")\n        return { from: node.from, options: atRules, validFor: identifier };\n    if (!context.explicit)\n        return null;\n    let above = node.resolve(pos), before = above.childBefore(pos);\n    if (before && before.name == \":\" && above.name == \"PseudoClassSelector\")\n        return { from: pos, options: pseudoClasses, validFor: identifier };\n    if (before && before.name == \":\" && above.name == \"Declaration\" || above.name == \"ArgList\")\n        return { from: pos, options: values, validFor: identifier };\n    if (above.name == \"Block\" || above.name == \"Styles\")\n        return { from: pos, options: properties(), validFor: identifier };\n    return null;\n};\n/**\nCSS property, variable, and value keyword completion source.\n*/\nconst cssCompletionSource = /*@__PURE__*/defineCSSCompletionSource(n => n.name == \"VariableName\");\n\n/**\nA language provider based on the [Lezer CSS\nparser](https://github.com/lezer-parser/css), extended with\nhighlighting and indentation information.\n*/\nconst cssLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"css\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/indentNodeProp.add({\n                Declaration: /*@__PURE__*/continuedIndent()\n            }),\n            /*@__PURE__*/foldNodeProp.add({\n                \"Block KeyframeList\": foldInside\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"/*\", close: \"*/\" } },\n        indentOnInput: /^\\s*\\}$/,\n        wordChars: \"-\"\n    }\n});\n/**\nLanguage support for CSS.\n*/\nfunction css() {\n    return new LanguageSupport(cssLanguage, cssLanguage.data.of({ autocomplete: cssCompletionSource }));\n}\n\nexport { css, cssCompletionSource, cssLanguage, defineCSSCompletionSource };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAIA,IAAM,eAAe;AAArB,IACE,OAAO;AADT,IAEE,aAAa;AAFf,IAGE,SAAS;AAHX,IAIE,eAAe;AAJjB,IAKE,kBAAkB;AALpB,IAME,oBAAoB;AANtB,IAOE,cAAc;AAKhB,IAAM,QAAQ;AAAA,EAAC;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EACrE;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAK;AAC1E,IAAM,QAAQ;AAAd,IAAkB,SAAS;AAA3B,IAA+B,aAAa;AAA5C,IAAgD,WAAW;AAA3D,IAA+D,OAAO;AAAtE,IAA0E,SAAS;AAAnF,IACM,OAAO;AADb,IACiB,UAAU;AAD3B,IAC+B,YAAY;AAD3C,IAC+C,YAAY;AAD3D,IAC+D,UAAU;AADzE,IAC6E,WAAW;AAExF,SAAS,QAAQ,IAAI;AAAE,SAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM;AAAI;AAEzF,SAAS,QAAQ,IAAI;AAAE,SAAO,MAAM,MAAM,MAAM;AAAG;AAEnD,SAAS,MAAM,IAAI;AAAE,SAAO,QAAQ,EAAE,KAAK,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM;AAAG;AAEzF,IAAM,mBAAmB,CAAC,IAAI,SAASA,YAAW,CAAC,OAAO,UAAU;AAClE,WAAS,SAAS,OAAO,SAAS,GAAG,IAAI,KAAI,KAAK;AAChD,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,IAAI,KAAK,QAAQ,QAAQ,QAAQ,cAAe,UAAU,QAAQ,IAAI,GAAI;AACpF,UAAI,CAAC,WAAW,QAAQ,QAAQ,IAAI,GAAI,UAAS;AACjD,UAAI,WAAW,KAAK,QAAQ,KAAM;AAClC,YAAM,QAAQ;AAAA,IAChB,WAAW,QAAQ,aAAa,MAAM,KAAK,CAAC,KAAK,SAAS;AACxD,YAAM,QAAQ;AACd,UAAI,MAAM,MAAM,IAAI,GAAG;AACrB,WAAG;AAAE,gBAAM,QAAQ;AAAA,QAAG,SAAS,MAAM,MAAM,IAAI;AAC/C,YAAI,MAAM,QAAQ,GAAI,OAAM,QAAQ;AAAA,MACtC,WAAW,MAAM,OAAO,IAAI;AAC1B,cAAM,QAAQ;AAAA,MAChB;AACA,eAAS;AAAA,IACX,OAAO;AACL,UAAI,OAAQ,OAAM;AAAA,QAChB,UAAU,KAAK,MAAM,SAAS,YAAY,IAAI,UAAU,QAAQ,SAASA,UAAS;AAAA,MACpF;AACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,cAAc,IAAI;AAAA,EACtB,iBAAiB,YAAY,cAAc,MAAM;AACnD;AACA,IAAM,mBAAmB,IAAI;AAAA,EAC3B,iBAAiB,iBAAiB,mBAAmB,WAAW;AAClE;AAEA,IAAM,aAAa,IAAI,kBAAkB,WAAS;AAChD,MAAI,MAAM,SAAS,MAAM,KAAK,EAAE,CAAC,GAAG;AAClC,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,IAAI,KAAK,QAAQ,cAAc,QAAQ,QAAQ,QAAQ,UAC/D,QAAQ,YAAY,QAAQ,YAAY,QAAQ,SAAS,QAAQ,MAAM,KAAK,CAAC,CAAC,KAC9E,QAAQ,QAAQ,QAAQ;AAC1B,YAAM,YAAY,YAAY;AAAA,EAClC;AACF,CAAC;AAED,IAAM,YAAY,IAAI,kBAAkB,WAAS;AAC/C,MAAI,CAAC,MAAM,SAAS,MAAM,KAAK,EAAE,CAAC,GAAG;AACnC,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,SAAS;AAAE,YAAM,QAAQ;AAAG,YAAM,YAAY,IAAI;AAAA,IAAG;AACjE,QAAI,QAAQ,IAAI,GAAG;AACjB,SAAG;AAAE,cAAM,QAAQ;AAAA,MAAG,SAAS,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI;AACxE,YAAM,YAAY,IAAI;AAAA,IACxB;AAAA,EACF;AACF,CAAC;AAED,IAAM,kBAAkB,UAAU;AAAA,EAChC,+DAA+D,KAAK;AAAA,EACpE,oBAAoB,KAAK;AAAA,EACzB,eAAe,KAAK;AAAA,EACpB,cAAc,KAAK;AAAA,EACnB,mBAAmB,KAAK;AAAA,EACxB,SAAS,KAAK;AAAA,EACd,WAAW,KAAK;AAAA,EAChB,iBAAiB,KAAK,SAAS,KAAK,SAAS;AAAA,EAC7C,QAAQ,KAAK;AAAA,EACb,4BAA4B,KAAK;AAAA,EACjC,eAAe,KAAK;AAAA,EACpB,eAAe,KAAK;AAAA,EACpB,cAAc,KAAK;AAAA,EACnB,cAAc,KAAK;AAAA,EACnB,qBAAqB,KAAK;AAAA,EAC1B,cAAc,KAAK;AAAA,EACnB,QAAQ,KAAK;AAAA,EACb,MAAM,KAAK;AAAA,EACX,qCAAqC,KAAK;AAAA,EAC1C,qBAAqB,KAAK;AAAA,EAC1B,8BAA8B,KAAK;AAAA,EACnC,OAAO,KAAK;AAAA,EACZ,WAAW,KAAK;AAAA,EAChB,SAAS,KAAK;AAAA,EACd,cAAc,KAAK;AAAA,EACnB,sCAAsC,KAAK;AAAA,EAC3C,KAAK,KAAK;AAAA,EACV,cAAc,KAAK;AAAA,EACnB,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AACd,CAAC;AAGD,IAAM,cAAc,EAAC,WAAU,MAAK,MAAK,IAAI,aAAY,IAAI,kBAAiB,IAAI,eAAc,IAAI,oBAAmB,IAAI,KAAI,IAAI,gBAAe,IAAI,IAAG,IAAI,KAAI,KAAK,cAAa,KAAK,QAAO,KAAK,QAAO,IAAG;AAC9M,IAAM,uBAAuB,EAAC,WAAU,MAAK,IAAG,IAAI,KAAI,IAAI,KAAI,KAAK,MAAK,KAAK,OAAM,IAAG;AACxF,IAAM,mBAAmB,EAAC,WAAU,MAAK,UAAS,KAAK,OAAM,IAAG;AAChE,IAAM,iBAAiB,EAAC,WAAU,MAAK,WAAU,KAAK,UAAS,KAAK,YAAW,KAAK,cAAa,KAAK,cAAa,KAAK,aAAY,KAAK,UAAS,IAAG;AACrJ,IAAM,kBAAkB,EAAC,WAAU,MAAK,IAAG,IAAG;AAC9C,IAAM,SAAS,SAAS,YAAY;AAAA,EAClC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,WAAW,IAAG,GAAE,IAAG,EAAE;AAAA,IACtB,CAAC,YAAY,IAAG,KAAI,IAAG,KAAI,IAAG,GAAG;AAAA,IACjC,CAAC,YAAY,IAAG,KAAI,IAAG,KAAI,IAAG,GAAG;AAAA,EACnC;AAAA,EACA,aAAa,CAAC,eAAe;AAAA,EAC7B,cAAc,CAAC,GAAE,GAAE,GAAG;AAAA,EACtB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,YAAY,WAAW,aAAa,kBAAkB,GAAG,GAAG,GAAG,GAAG,IAAI,gBAAgB,sCAAsC,IAAI,GAAG,CAAC;AAAA,EACjJ,UAAU,EAAC,cAAa,CAAC,GAAE,CAAC,GAAE,UAAS,CAAC,GAAE,GAAG,EAAC;AAAA,EAC9C,aAAa,CAAC,EAAC,MAAM,KAAK,KAAK,CAAC,UAAU,YAAY,KAAK,KAAK,GAAE,GAAE,EAAC,MAAM,KAAK,KAAK,CAAC,UAAU,qBAAqB,KAAK,KAAK,GAAE,GAAE,EAAC,MAAM,GAAG,KAAK,CAAC,UAAU,iBAAiB,KAAK,KAAK,GAAE,GAAE,EAAC,MAAM,IAAI,KAAK,CAAC,UAAU,eAAe,KAAK,KAAK,GAAE,GAAE,EAAC,MAAM,KAAK,KAAK,CAAC,UAAU,gBAAgB,KAAK,KAAK,GAAE,CAAC;AAAA,EAC7S,WAAW;AACb,CAAC;;;ACzID,IAAI,cAAc;AAClB,SAAS,aAAa;AAClB,MAAI,CAAC,eAAe,OAAO,YAAY,YAAY,SAAS,MAAM;AAC9D,QAAI,EAAE,MAAM,IAAI,SAAS,MAAM,QAAQ,CAAC,GAAG,OAAO,oBAAI;AACtD,aAAS,QAAQ;AACb,UAAI,QAAQ,aAAa,QAAQ,YAAY;AACzC,YAAI,OAAO,MAAM,IAAI,KAAK,UAAU;AAChC,cAAI,QAAQ,KAAK,IAAI;AACjB,mBAAO,KAAK,QAAQ,UAAU,QAAM,MAAM,GAAG,YAAY,CAAC;AAC9D,cAAI,CAAC,KAAK,IAAI,IAAI,GAAG;AACjB,kBAAM,KAAK,IAAI;AACf,iBAAK,IAAI,IAAI;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AACJ,kBAAc,MAAM,KAAK,EAAE,IAAI,WAAS,EAAE,MAAM,YAAY,OAAO,MAAM,OAAO,OAAO,KAAK,EAAE;AAAA,EAClG;AACA,SAAO,eAAe,CAAC;AAC3B;AACA,IAAM,gBAA6B;AAAA,EAC/B;AAAA,EAAU;AAAA,EAAS;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EACvD;AAAA,EAAW;AAAA,EAAO;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EACpD;AAAA,EAAW;AAAA,EAAwB;AAAA,EAAS;AAAA,EAC5C;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAiB;AAAA,EAC/C;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAO;AAAA,EACtD;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAY;AAAA,EAAiB;AAAA,EACtD;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAC5D;AAAA,EAAS;AAAA,EAAO;AAAA,EAAa;AAAA,EAAkB;AAAA,EAC/C;AAAA,EAAe;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAY;AAAA,EACzD;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAqB;AAAA,EAAa;AAAA,EACzD;AAAA,EAAY;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAa;AAAA,EAAW;AAAA,EAC9D;AAAA,EAAe;AAAA,EAAS;AAAA,EAAW;AACvC,EAAE,IAAI,WAAS,EAAE,MAAM,SAAS,OAAO,KAAK,EAAE;AAC9C,IAAM,SAAsB;AAAA,EACxB;AAAA,EAAS;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAiB;AAAA,EAClE;AAAA,EAAS;AAAA,EAAS;AAAA,EAAO;AAAA,EAAc;AAAA,EAAc;AAAA,EAAa;AAAA,EAClE;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAS;AAAA,EAClF;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAc;AAAA,EAAa;AAAA,EAAY;AAAA,EACjF;AAAA,EAAiB;AAAA,EAAS;AAAA,EAAS;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAC7E;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAa;AAAA,EAAc;AAAA,EAAW;AAAA,EAAU;AAAA,EAC3E;AAAA,EAAc;AAAA,EAAmB;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAQ;AAAA,EACvE;AAAA,EAAuB;AAAA,EAAW;AAAA,EAAe;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAY;AAAA,EACxF;AAAA,EAAe;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAc;AAAA,EAAY;AAAA,EAAS;AAAA,EAClF;AAAA,EAAe;AAAA,EAAU;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAa;AAAA,EAAW;AAAA,EAC9E;AAAA,EAAY;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAY;AAAA,EACxF;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EACvF;AAAA,EAAwB;AAAA,EAAW;AAAA,EAAkB;AAAA,EAAS;AAAA,EAAoB;AAAA,EAClF;AAAA,EAAmB;AAAA,EAAoB;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAW;AAAA,EACxE;AAAA,EAAmB;AAAA,EAAY;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EACvF;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAe;AAAA,EAAY;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EACzF;AAAA,EAAwB;AAAA,EAA2B;AAAA,EAAyB;AAAA,EAAa;AAAA,EACzF;AAAA,EAAY;AAAA,EAAW;AAAA,EAAmB;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACvF;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAc;AAAA,EAAa;AAAA,EAAY;AAAA,EAC5E;AAAA,EAAsB;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAU;AAAA,EAC5F;AAAA,EAAU;AAAA,EAAa;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EACpF;AAAA,EAAkB;AAAA,EAAmB;AAAA,EAAuB;AAAA,EAAY;AAAA,EAAkB;AAAA,EAC1F;AAAA,EAAW;AAAA,EAAW;AAAA,EAAU;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAe;AAAA,EAC9E;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAU;AAAA,EAAa;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAC/E;AAAA,EAAa;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAW;AAAA,EAAW;AAAA,EAAgB;AAAA,EACvF;AAAA,EAAmB;AAAA,EAAS;AAAA,EAAa;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EAAW;AAAA,EAAQ;AAAA,EAC5F;AAAA,EAAqB;AAAA,EAAe;AAAA,EAAmB;AAAA,EAAa;AAAA,EAAO;AAAA,EAAc;AAAA,EACzF;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAe;AAAA,EAAU;AAAA,EACtF;AAAA,EAAO;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAwB;AAAA,EAAY;AAAA,EAAY;AAAA,EACxF;AAAA,EAAa;AAAA,EAAe;AAAA,EAAkB;AAAA,EAAW;AAAA,EAAiB;AAAA,EAAa;AAAA,EACvF;AAAA,EAAU;AAAA,EAAe;AAAA,EAAU;AAAA,EAAa;AAAA,EAAW;AAAA,EAAW;AAAA,EAAa;AAAA,EACnF;AAAA,EAAW;AAAA,EAAW;AAAA,EAAc;AAAA,EAAsB;AAAA,EAAiB;AAAA,EAAU;AAAA,EACrF;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAe;AAAA,EAAW;AAAA,EAAQ;AAAA,EACrF;AAAA,EAAe;AAAA,EAAc;AAAA,EAAe;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAW;AAAA,EAClF;AAAA,EAAO;AAAA,EAAY;AAAA,EAAY;AAAA,EAAe;AAAA,EAAY;AAAA,EAAe;AAAA,EAAmB;AAAA,EAC5F;AAAA,EAAa;AAAA,EAAc;AAAA,EAA6B;AAAA,EAAa;AAAA,EAAU;AAAA,EAAY;AAAA,EAC3F;AAAA,EAA6B;AAAA,EAA6B;AAAA,EAAY;AAAA,EAAY;AAAA,EAAS;AAAA,EAC3F;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EACxF;AAAA,EAAO;AAAA,EAAc;AAAA,EAAe;AAAA,EAAO;AAAA,EAAU;AAAA,EAAW;AAAA,EAAY;AAAA,EAAc;AAAA,EAC1F;AAAA,EAAS;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAa;AAAA,EACnF;AAAA,EAAa;AAAA,EAAc;AAAA,EAAY;AAAA,EAAkB;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAS;AAAA,EAC/F;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAoB;AAAA,EAAS;AAAA,EACjE;AAAA,EAAmB;AAAA,EAA0B;AAAA,EAAwB;AAAA,EAAQ;AAAA,EAAS;AAAA,EACtF;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAc;AAAA,EAAS;AAAA,EAAe;AAAA,EAAa;AAAA,EAC/E;AAAA,EAAe;AAAA,EAAS;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAU;AAAA,EAChG;AAAA,EAAU;AAAA,EAAc;AAAA,EAAW;AAAA,EAAU;AAAA,EAAc;AAAA,EAAO;AAAA,EAAwB;AAAA,EAC1F;AAAA,EAAS;AAAA,EAAa;AAAA,EAAY;AAAA,EAAW;AAAA,EAAa;AAAA,EAAS;AAAA,EAAiB;AAAA,EACpF;AAAA,EAAgB;AAAA,EAAsB;AAAA,EAAsB;AAAA,EAAsB;AAAA,EAClF;AAAA,EAAmB;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAY;AAAA,EAAY;AAAA,EAAa;AAAA,EAAS;AAAA,EACxF;AAAA,EAAoB;AAAA,EAAc;AAAA,EAAmB;AAAA,EAAqB;AAAA,EAAgB;AAAA,EAAM;AAAA,EAChG;AAAA,EAAa;AAAA,EAAa;AAAA,EAAe;AAAA,EAAc;AAAA,EAAc;AAAA,EAAc;AAAA,EACnF;AAAA,EAAmB;AAAA,EAAkB;AAAA,EAAa;AAAA,EAAsB;AAAA,EAAS;AAAA,EAAM;AAAA,EACvF;AAAA,EAAa;AAAA,EAAO;AAAA,EAAO;AAAA,EAAY;AAAA,EAAiB;AAAA,EAAY;AAAA,EAAW;AAAA,EAC/E;AAAA,EAAkB;AAAA,EAAiB;AAAA,EAAU;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAU;AAAA,EAC5F;AAAA,EAAc;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAW;AAAA,EAAO;AAAA,EAAY;AAC5F,EAAE,IAAI,WAAS,EAAE,MAAM,WAAW,OAAO,KAAK,EAAE,EAAE,OAAoB;AAAA,EAClE;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAS;AAAA,EAC5D;AAAA,EAAU;AAAA,EAAS;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAc;AAAA,EAC3D;AAAA,EAAa;AAAA,EAAa;AAAA,EAAc;AAAA,EAAa;AAAA,EAAS;AAAA,EAC9D;AAAA,EAAY;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAY;AAAA,EACvD;AAAA,EAAY;AAAA,EAAa;AAAA,EAAa;AAAA,EAAe;AAAA,EACrD;AAAA,EAAc;AAAA,EAAc;AAAA,EAAW;AAAA,EAAc;AAAA,EACrD;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAAiB;AAAA,EACnD;AAAA,EAAY;AAAA,EAAe;AAAA,EAAW;AAAA,EAAc;AAAA,EACpD;AAAA,EAAe;AAAA,EAAe;AAAA,EAAW;AAAA,EAAa;AAAA,EACtD;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAe;AAAA,EAC7D;AAAA,EAAW;AAAA,EAAa;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AAAA,EACpD;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAa;AAAA,EAC3D;AAAA,EAAa;AAAA,EAAwB;AAAA,EAAa;AAAA,EAAc;AAAA,EAChE;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAChD;AAAA,EAAkB;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAS;AAAA,EAC/D;AAAA,EAAU;AAAA,EAAoB;AAAA,EAAc;AAAA,EAAgB;AAAA,EAC5D;AAAA,EAAkB;AAAA,EAAmB;AAAA,EAAqB;AAAA,EAC1D;AAAA,EAAmB;AAAA,EAAgB;AAAA,EAAa;AAAA,EAAa;AAAA,EAC7D;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAS;AAAA,EAAa;AAAA,EAAU;AAAA,EAClE;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAiB;AAAA,EACzD;AAAA,EAAc;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACnD;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAO;AAAA,EAAa;AAAA,EAAa;AAAA,EAC5D;AAAA,EAAU;AAAA,EAAc;AAAA,EAAY;AAAA,EAAY;AAAA,EAAU;AAAA,EAAU;AAAA,EACpE;AAAA,EAAa;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAa;AAAA,EAC9D;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAU;AAAA,EAAa;AAAA,EAAU;AAAA,EAAS;AAAA,EAC7D;AAAA,EAAc;AAAA,EAAU;AAC5B,EAAE,IAAI,WAAS,EAAE,MAAM,YAAY,OAAO,KAAK,EAAE,CAAC;AAClD,IAAMC,QAAoB;AAAA,EACtB;AAAA,EAAK;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAAK;AAAA,EAAO;AAAA,EAAO;AAAA,EAAc;AAAA,EAC7E;AAAA,EAAM;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAY;AAAA,EAAM;AAAA,EAC9E;AAAA,EAAW;AAAA,EAAO;AAAA,EAAU;AAAA,EAAO;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAc;AAAA,EAAU;AAAA,EAC7E;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAK;AAAA,EACnF;AAAA,EAAO;AAAA,EAAS;AAAA,EAAO;AAAA,EAAO;AAAA,EAAS;AAAA,EAAU;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAO;AAAA,EAAM;AAAA,EACrF;AAAA,EAAK;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAO;AAAA,EACrF;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAM;AAAA,EAAY;AAAA,EAAY;AAAA,EAAS;AAAA,EAAM;AAAA,EAAS;AAAA,EAAM;AAAA,EAAK;AAC9F,EAAE,IAAI,WAAS,EAAE,MAAM,QAAQ,OAAO,KAAK,EAAE;AAC7C,IAAM,UAAuB;AAAA,EACzB;AAAA,EAAY;AAAA,EAAkB;AAAA,EAAc;AAAA,EAAkB;AAAA,EAAc;AAAA,EAC5E;AAAA,EAAwB;AAAA,EAAW;AAAA,EAAc;AAAA,EAAU;AAAA,EAAU;AAAA,EAAc;AAAA,EACnF;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAU;AAAA,EAAmB;AAAA,EAAa;AAC5E,EAAE,IAAI,YAAU,EAAE,MAAM,WAAW,MAAM,EAAE;AAC3C,IAAMC,cAAa;AAAnB,IAA8C,WAAW;AACzD,SAAS,SAAS,MAAM,KAAK;AACzB,MAAI;AACJ,MAAI,KAAK,QAAQ,OAAO,KAAK,KAAK;AAC9B,WAAO,KAAK,UAAU;AAC1B,MAAI,KAAK,QAAQ;AACb,WAAO;AACX,MAAIC,WAAU,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AACxE,OAAKA,YAAW,QAAQA,YAAW,SAAS,SAASA,QAAO,SAAS;AACjE,WAAO;AACX,SAAO,IAAI,YAAYA,QAAO,MAAMA,QAAO,EAAE,KAAK;AACtD;AACA,IAAM,kBAA+B,IAAI,YAAY;AACrD,IAAM,eAAe,CAAC,aAAa;AACnC,SAAS,OAAO,MAAM;AAClB,WAAS,MAAM,UAAQ;AACnB,QAAI,IAAI,KAAK;AACT,aAAO;AACX,QAAI,EAAE,MAAM,IAAI;AACZ,aAAO;AAAA,EACf;AACJ;AACA,SAAS,cAAc,KAAK,MAAM,YAAY;AAC1C,MAAI,KAAK,KAAK,KAAK,OAAO,MAAM;AAC5B,QAAI,QAAQ,gBAAgB,IAAI,IAAI;AACpC,QAAI;AACA,aAAO;AACX,QAAI,SAAS,CAAC,GAAG,OAAO,oBAAI,OAAK,SAAS,KAAK,OAAO,SAAS,gBAAgB;AAC/E,QAAI,OAAO,WAAW;AAClB,SAAG;AACC,iBAAS,UAAU,cAAc,KAAK,OAAO,MAAM,UAAU;AACzD,cAAI,CAAC,KAAK,IAAI,OAAO,KAAK,GAAG;AACzB,iBAAK,IAAI,OAAO,KAAK;AACrB,mBAAO,KAAK,MAAM;AAAA,UACtB;AAAA,MACR,SAAS,OAAO,YAAY;AAChC,oBAAgB,IAAI,MAAM,MAAM;AAChC,WAAO;AAAA,EACX,OACK;AACD,QAAI,SAAS,CAAC,GAAG,OAAO,oBAAI;AAC5B,SAAK,OAAO,EAAE,QAAQ,CAAAC,UAAQ;AAC1B,UAAI;AACJ,UAAI,WAAWA,KAAI,KAAKA,MAAK,aAAa,YAAY,OAAO,KAAKA,MAAK,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,KAAK;AAC3I,YAAI,OAAO,IAAI,YAAYA,MAAK,MAAMA,MAAK,EAAE;AAC7C,YAAI,CAAC,KAAK,IAAI,IAAI,GAAG;AACjB,eAAK,IAAI,IAAI;AACb,iBAAO,KAAK,EAAE,OAAO,MAAM,MAAM,WAAW,CAAC;AAAA,QACjD;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ;AAOA,IAAM,4BAA4B,CAAC,eAAe,aAAW;AACzD,MAAI,EAAE,OAAO,IAAI,IAAI,SAAS,OAAO,WAAW,KAAK,EAAE,aAAa,KAAK,EAAE;AAC3E,MAAI,SAAS,KAAK,KAAK,WAAW,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAM,IAAI,YAAY,KAAK,MAAM,KAAK,EAAE,KAAK;AAC3G,MAAI,KAAK,QAAQ,mBACZ,UAAU,KAAK,QAAQ,cAAc,mBAAmB,KAAK,KAAK,QAAQ,KAAK,EAAE,EAAE,IAAI;AACxF,WAAO,EAAE,MAAM,KAAK,MAAM,SAAS,WAAW,GAAG,UAAUF,YAAW;AAC1E,MAAI,KAAK,QAAQ;AACb,WAAO,EAAE,MAAM,KAAK,MAAM,SAAS,QAAQ,UAAUA,YAAW;AACpE,MAAI,KAAK,QAAQ;AACb,WAAO,EAAE,MAAM,KAAK,MAAM,SAAS,eAAe,UAAUA,YAAW;AAC3E,MAAI,WAAW,IAAI,MAAM,QAAQ,YAAY,WAAW,SAAS,MAAM,MAAM,GAAG;AAC5E,WAAO;AAAA,MAAE,MAAM,WAAW,IAAI,KAAK,SAAS,KAAK,OAAO;AAAA,MACpD,SAAS,cAAc,MAAM,KAAK,OAAO,IAAI,GAAG,UAAU;AAAA,MAC1D,UAAU;AAAA,IAAS;AAC3B,MAAI,KAAK,QAAQ,WAAW;AACxB,aAAS,EAAE,OAAO,IAAI,MAAM,QAAQ,SAAS,OAAO;AAChD,UAAI,OAAO,QAAQ;AACf,eAAO,EAAE,MAAM,KAAK,MAAM,SAAS,WAAW,GAAG,UAAUA,YAAW;AAC9E,WAAO,EAAE,MAAM,KAAK,MAAM,SAASD,OAAM,UAAUC,YAAW;AAAA,EAClE;AACA,MAAI,KAAK,QAAQ;AACb,WAAO,EAAE,MAAM,KAAK,MAAM,SAAS,SAAS,UAAUA,YAAW;AACrE,MAAI,CAAC,QAAQ;AACT,WAAO;AACX,MAAI,QAAQ,KAAK,QAAQ,GAAG,GAAG,SAAS,MAAM,YAAY,GAAG;AAC7D,MAAI,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ;AAC9C,WAAO,EAAE,MAAM,KAAK,SAAS,eAAe,UAAUA,YAAW;AACrE,MAAI,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ,iBAAiB,MAAM,QAAQ;AAC7E,WAAO,EAAE,MAAM,KAAK,SAAS,QAAQ,UAAUA,YAAW;AAC9D,MAAI,MAAM,QAAQ,WAAW,MAAM,QAAQ;AACvC,WAAO,EAAE,MAAM,KAAK,SAAS,WAAW,GAAG,UAAUA,YAAW;AACpE,SAAO;AACX;AAIA,IAAM,sBAAmC,0BAA0B,OAAK,EAAE,QAAQ,cAAc;AAOhG,IAAM,cAA2B,WAAW,OAAO;AAAA,EAC/C,MAAM;AAAA,EACN,QAAqB,OAAO,UAAU;AAAA,IAClC,OAAO;AAAA,MACU,eAAe,IAAI;AAAA,QAC5B,aAA0B,gBAAgB;AAAA,MAC9C,CAAC;AAAA,MACY,aAAa,IAAI;AAAA,QAC1B,sBAAsB;AAAA,MAC1B,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAAA,EACD,cAAc;AAAA,IACV,eAAe,EAAE,OAAO,EAAE,MAAM,MAAM,OAAO,KAAK,EAAE;AAAA,IACpD,eAAe;AAAA,IACf,WAAW;AAAA,EACf;AACJ,CAAC;AAID,SAAS,MAAM;AACX,SAAO,IAAI,gBAAgB,aAAa,YAAY,KAAK,GAAG,EAAE,cAAc,oBAAoB,CAAC,CAAC;AACtG;", "names": ["callee", "tags", "identifier", "callee", "node"]}