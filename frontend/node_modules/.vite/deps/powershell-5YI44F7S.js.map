{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/powershell.js"], "sourcesContent": ["function buildRegexp(patterns, options) {\n  options = options || {};\n  var prefix = options.prefix !== undefined ? options.prefix : '^';\n  var suffix = options.suffix !== undefined ? options.suffix : '\\\\b';\n\n  for (var i = 0; i < patterns.length; i++) {\n    if (patterns[i] instanceof RegExp) {\n      patterns[i] = patterns[i].source;\n    }\n    else {\n      patterns[i] = patterns[i].replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n    }\n  }\n\n  return new RegExp(prefix + '(' + patterns.join('|') + ')' + suffix, 'i');\n}\n\nvar notCharacterOrDash = '(?=[^A-Za-z\\\\d\\\\-_]|$)';\nvar varNames = /[\\w\\-:]/\nvar keywords = buildRegexp([\n  /begin|break|catch|continue|data|default|do|dynamicparam/,\n  /else|elseif|end|exit|filter|finally|for|foreach|from|function|if|in/,\n  /param|process|return|switch|throw|trap|try|until|where|while/\n], { suffix: notCharacterOrDash });\n\nvar punctuation = /[\\[\\]{},;`\\\\\\.]|@[({]/;\nvar wordOperators = buildRegexp([\n  'f',\n  /b?not/,\n  /[ic]?split/, 'join',\n  /is(not)?/, 'as',\n  /[ic]?(eq|ne|[gl][te])/,\n  /[ic]?(not)?(like|match|contains)/,\n  /[ic]?replace/,\n  /b?(and|or|xor)/\n], { prefix: '-' });\nvar symbolOperators = /[+\\-*\\/%]=|\\+\\+|--|\\.\\.|[+\\-*&^%:=!|\\/]|<(?!#)|(?!#)>/;\nvar operators = buildRegexp([wordOperators, symbolOperators], { suffix: '' });\n\nvar numbers = /^((0x[\\da-f]+)|((\\d+\\.\\d+|\\d\\.|\\.\\d+|\\d+)(e[\\+\\-]?\\d+)?))[ld]?([kmgtp]b)?/i;\n\nvar identifiers = /^[A-Za-z\\_][A-Za-z\\-\\_\\d]*\\b/;\n\nvar symbolBuiltins = /[A-Z]:|%|\\?/i;\nvar namedBuiltins = buildRegexp([\n  /Add-(Computer|Content|History|Member|PSSnapin|Type)/,\n  /Checkpoint-Computer/,\n  /Clear-(Content|EventLog|History|Host|Item(Property)?|Variable)/,\n  /Compare-Object/,\n  /Complete-Transaction/,\n  /Connect-PSSession/,\n  /ConvertFrom-(Csv|Json|SecureString|StringData)/,\n  /Convert-Path/,\n  /ConvertTo-(Csv|Html|Json|SecureString|Xml)/,\n  /Copy-Item(Property)?/,\n  /Debug-Process/,\n  /Disable-(ComputerRestore|PSBreakpoint|PSRemoting|PSSessionConfiguration)/,\n  /Disconnect-PSSession/,\n  /Enable-(ComputerRestore|PSBreakpoint|PSRemoting|PSSessionConfiguration)/,\n  /(Enter|Exit)-PSSession/,\n  /Export-(Alias|Clixml|Console|Counter|Csv|FormatData|ModuleMember|PSSession)/,\n  /ForEach-Object/,\n  /Format-(Custom|List|Table|Wide)/,\n  new RegExp('Get-(Acl|Alias|AuthenticodeSignature|ChildItem|Command|ComputerRestorePoint|Content|ControlPanelItem|Counter|Credential'\n             + '|Culture|Date|Event|EventLog|EventSubscriber|ExecutionPolicy|FormatData|Help|History|Host|HotFix|Item|ItemProperty|Job'\n             + '|Location|Member|Module|PfxCertificate|Process|PSBreakpoint|PSCallStack|PSDrive|PSProvider|PSSession|PSSessionConfiguration'\n             + '|PSSnapin|Random|Service|TraceSource|Transaction|TypeData|UICulture|Unique|Variable|Verb|WinEvent|WmiObject)'),\n  /Group-Object/,\n  /Import-(Alias|Clixml|Counter|Csv|LocalizedData|Module|PSSession)/,\n  /ImportSystemModules/,\n  /Invoke-(Command|Expression|History|Item|RestMethod|WebRequest|WmiMethod)/,\n  /Join-Path/,\n  /Limit-EventLog/,\n  /Measure-(Command|Object)/,\n  /Move-Item(Property)?/,\n  new RegExp('New-(Alias|Event|EventLog|Item(Property)?|Module|ModuleManifest|Object|PSDrive|PSSession|PSSessionConfigurationFile'\n             + '|PSSessionOption|PSTransportOption|Service|TimeSpan|Variable|WebServiceProxy|WinEvent)'),\n  /Out-(Default|File|GridView|Host|Null|Printer|String)/,\n  /Pause/,\n  /(Pop|Push)-Location/,\n  /Read-Host/,\n  /Receive-(Job|PSSession)/,\n  /Register-(EngineEvent|ObjectEvent|PSSessionConfiguration|WmiEvent)/,\n  /Remove-(Computer|Event|EventLog|Item(Property)?|Job|Module|PSBreakpoint|PSDrive|PSSession|PSSnapin|TypeData|Variable|WmiObject)/,\n  /Rename-(Computer|Item(Property)?)/,\n  /Reset-ComputerMachinePassword/,\n  /Resolve-Path/,\n  /Restart-(Computer|Service)/,\n  /Restore-Computer/,\n  /Resume-(Job|Service)/,\n  /Save-Help/,\n  /Select-(Object|String|Xml)/,\n  /Send-MailMessage/,\n  new RegExp('Set-(Acl|Alias|AuthenticodeSignature|Content|Date|ExecutionPolicy|Item(Property)?|Location|PSBreakpoint|PSDebug' +\n             '|PSSessionConfiguration|Service|StrictMode|TraceSource|Variable|WmiInstance)'),\n  /Show-(Command|ControlPanelItem|EventLog)/,\n  /Sort-Object/,\n  /Split-Path/,\n  /Start-(Job|Process|Service|Sleep|Transaction|Transcript)/,\n  /Stop-(Computer|Job|Process|Service|Transcript)/,\n  /Suspend-(Job|Service)/,\n  /TabExpansion2/,\n  /Tee-Object/,\n  /Test-(ComputerSecureChannel|Connection|ModuleManifest|Path|PSSessionConfigurationFile)/,\n  /Trace-Command/,\n  /Unblock-File/,\n  /Undo-Transaction/,\n  /Unregister-(Event|PSSessionConfiguration)/,\n  /Update-(FormatData|Help|List|TypeData)/,\n  /Use-Transaction/,\n  /Wait-(Event|Job|Process)/,\n  /Where-Object/,\n  /Write-(Debug|Error|EventLog|Host|Output|Progress|Verbose|Warning)/,\n  /cd|help|mkdir|more|oss|prompt/,\n  /ac|asnp|cat|cd|chdir|clc|clear|clhy|cli|clp|cls|clv|cnsn|compare|copy|cp|cpi|cpp|cvpa|dbp|del|diff|dir|dnsn|ebp/,\n  /echo|epal|epcsv|epsn|erase|etsn|exsn|fc|fl|foreach|ft|fw|gal|gbp|gc|gci|gcm|gcs|gdr|ghy|gi|gjb|gl|gm|gmo|gp|gps/,\n  /group|gsn|gsnp|gsv|gu|gv|gwmi|h|history|icm|iex|ihy|ii|ipal|ipcsv|ipmo|ipsn|irm|ise|iwmi|iwr|kill|lp|ls|man|md/,\n  /measure|mi|mount|move|mp|mv|nal|ndr|ni|nmo|npssc|nsn|nv|ogv|oh|popd|ps|pushd|pwd|r|rbp|rcjb|rcsn|rd|rdr|ren|ri/,\n  /rjb|rm|rmdir|rmo|rni|rnp|rp|rsn|rsnp|rujb|rv|rvpa|rwmi|sajb|sal|saps|sasv|sbp|sc|select|set|shcm|si|sl|sleep|sls/,\n  /sort|sp|spjb|spps|spsv|start|sujb|sv|swmi|tee|trcm|type|where|wjb|write/\n], { prefix: '', suffix: '' });\nvar variableBuiltins = buildRegexp([\n  /[$?^_]|Args|ConfirmPreference|ConsoleFileName|DebugPreference|Error|ErrorActionPreference|ErrorView|ExecutionContext/,\n  /FormatEnumerationLimit|Home|Host|Input|MaximumAliasCount|MaximumDriveCount|MaximumErrorCount|MaximumFunctionCount/,\n  /MaximumHistoryCount|MaximumVariableCount|MyInvocation|NestedPromptLevel|OutputEncoding|Pid|Profile|ProgressPreference/,\n  /PSBoundParameters|PSCommandPath|PSCulture|PSDefaultParameterValues|PSEmailServer|PSHome|PSScriptRoot|PSSessionApplicationName/,\n  /PSSessionConfigurationName|PSSessionOption|PSUICulture|PSVersionTable|Pwd|ShellId|StackTrace|VerbosePreference/,\n  /WarningPreference|WhatIfPreference/,\n\n  /Event|EventArgs|EventSubscriber|Sender/,\n  /Matches|Ofs|ForEach|LastExitCode|PSCmdlet|PSItem|PSSenderInfo|This/,\n  /true|false|null/\n], { prefix: '\\\\$', suffix: '' });\n\nvar builtins = buildRegexp([symbolBuiltins, namedBuiltins, variableBuiltins], { suffix: notCharacterOrDash });\n\nvar grammar = {\n  keyword: keywords,\n  number: numbers,\n  operator: operators,\n  builtin: builtins,\n  punctuation: punctuation,\n  variable: identifiers\n};\n\n// tokenizers\nfunction tokenBase(stream, state) {\n  // Handle Comments\n  //var ch = stream.peek();\n\n  var parent = state.returnStack[state.returnStack.length - 1];\n  if (parent && parent.shouldReturnFrom(state)) {\n    state.tokenize = parent.tokenize;\n    state.returnStack.pop();\n    return state.tokenize(stream, state);\n  }\n\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  if (stream.eat('(')) {\n    state.bracketNesting += 1;\n    return 'punctuation';\n  }\n\n  if (stream.eat(')')) {\n    state.bracketNesting -= 1;\n    return 'punctuation';\n  }\n\n  for (var key in grammar) {\n    if (stream.match(grammar[key])) {\n      return key;\n    }\n  }\n\n  var ch = stream.next();\n\n  // single-quote string\n  if (ch === \"'\") {\n    return tokenSingleQuoteString(stream, state);\n  }\n\n  if (ch === '$') {\n    return tokenVariable(stream, state);\n  }\n\n  // double-quote string\n  if (ch === '\"') {\n    return tokenDoubleQuoteString(stream, state);\n  }\n\n  if (ch === '<' && stream.eat('#')) {\n    state.tokenize = tokenComment;\n    return tokenComment(stream, state);\n  }\n\n  if (ch === '#') {\n    stream.skipToEnd();\n    return 'comment';\n  }\n\n  if (ch === '@') {\n    var quoteMatch = stream.eat(/[\"']/);\n    if (quoteMatch && stream.eol()) {\n      state.tokenize = tokenMultiString;\n      state.startQuote = quoteMatch[0];\n      return tokenMultiString(stream, state);\n    } else if (stream.eol()) {\n      return 'error';\n    } else if (stream.peek().match(/[({]/)) {\n      return 'punctuation';\n    } else if (stream.peek().match(varNames)) {\n      // splatted variable\n      return tokenVariable(stream, state);\n    }\n  }\n  return 'error';\n}\n\nfunction tokenSingleQuoteString(stream, state) {\n  var ch;\n  while ((ch = stream.peek()) != null) {\n    stream.next();\n\n    if (ch === \"'\" && !stream.eat(\"'\")) {\n      state.tokenize = tokenBase;\n      return 'string';\n    }\n  }\n\n  return 'error';\n}\n\nfunction tokenDoubleQuoteString(stream, state) {\n  var ch;\n  while ((ch = stream.peek()) != null) {\n    if (ch === '$') {\n      state.tokenize = tokenStringInterpolation;\n      return 'string';\n    }\n\n    stream.next();\n    if (ch === '`') {\n      stream.next();\n      continue;\n    }\n\n    if (ch === '\"' && !stream.eat('\"')) {\n      state.tokenize = tokenBase;\n      return 'string';\n    }\n  }\n\n  return 'error';\n}\n\nfunction tokenStringInterpolation(stream, state) {\n  return tokenInterpolation(stream, state, tokenDoubleQuoteString);\n}\n\nfunction tokenMultiStringReturn(stream, state) {\n  state.tokenize = tokenMultiString;\n  state.startQuote = '\"'\n  return tokenMultiString(stream, state);\n}\n\nfunction tokenHereStringInterpolation(stream, state) {\n  return tokenInterpolation(stream, state, tokenMultiStringReturn);\n}\n\nfunction tokenInterpolation(stream, state, parentTokenize) {\n  if (stream.match('$(')) {\n    var savedBracketNesting = state.bracketNesting;\n    state.returnStack.push({\n      /*jshint loopfunc:true */\n      shouldReturnFrom: function(state) {\n        return state.bracketNesting === savedBracketNesting;\n      },\n      tokenize: parentTokenize\n    });\n    state.tokenize = tokenBase;\n    state.bracketNesting += 1;\n    return 'punctuation';\n  } else {\n    stream.next();\n    state.returnStack.push({\n      shouldReturnFrom: function() { return true; },\n      tokenize: parentTokenize\n    });\n    state.tokenize = tokenVariable;\n    return state.tokenize(stream, state);\n  }\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == '>') {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch === '#');\n  }\n  return 'comment';\n}\n\nfunction tokenVariable(stream, state) {\n  var ch = stream.peek();\n  if (stream.eat('{')) {\n    state.tokenize = tokenVariableWithBraces;\n    return tokenVariableWithBraces(stream, state);\n  } else if (ch != undefined && ch.match(varNames)) {\n    stream.eatWhile(varNames);\n    state.tokenize = tokenBase;\n    return 'variable';\n  } else {\n    state.tokenize = tokenBase;\n    return 'error';\n  }\n}\n\nfunction tokenVariableWithBraces(stream, state) {\n  var ch;\n  while ((ch = stream.next()) != null) {\n    if (ch === '}') {\n      state.tokenize = tokenBase;\n      break;\n    }\n  }\n  return 'variable';\n}\n\nfunction tokenMultiString(stream, state) {\n  var quote = state.startQuote;\n  if (stream.sol() && stream.match(new RegExp(quote + '@'))) {\n    state.tokenize = tokenBase;\n  }\n  else if (quote === '\"') {\n    while (!stream.eol()) {\n      var ch = stream.peek();\n      if (ch === '$') {\n        state.tokenize = tokenHereStringInterpolation;\n        return 'string';\n      }\n\n      stream.next();\n      if (ch === '`') {\n        stream.next();\n      }\n    }\n  }\n  else {\n    stream.skipToEnd();\n  }\n\n  return 'string';\n}\n\nexport const powerShell = {\n  name: \"powershell\",\n\n  startState: function() {\n    return {\n      returnStack: [],\n      bracketNesting: 0,\n      tokenize: tokenBase\n    };\n  },\n\n  token: function(stream, state) {\n    return state.tokenize(stream, state);\n  },\n\n  languageData: {\n    commentTokens: {line: \"#\", block: {open: \"<#\", close: \"#>\"}}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,YAAY,UAAU,SAAS;AACtC,YAAU,WAAW,CAAC;AACtB,MAAI,SAAS,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAC7D,MAAI,SAAS,QAAQ,WAAW,SAAY,QAAQ,SAAS;AAE7D,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,SAAS,CAAC,aAAa,QAAQ;AACjC,eAAS,CAAC,IAAI,SAAS,CAAC,EAAE;AAAA,IAC5B,OACK;AACH,eAAS,CAAC,IAAI,SAAS,CAAC,EAAE,QAAQ,0BAA0B,MAAM;AAAA,IACpE;AAAA,EACF;AAEA,SAAO,IAAI,OAAO,SAAS,MAAM,SAAS,KAAK,GAAG,IAAI,MAAM,QAAQ,GAAG;AACzE;AAEA,IAAI,qBAAqB;AACzB,IAAI,WAAW;AACf,IAAI,WAAW,YAAY;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AACF,GAAG,EAAE,QAAQ,mBAAmB,CAAC;AAEjC,IAAI,cAAc;AAClB,IAAI,gBAAgB,YAAY;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EAAc;AAAA,EACd;AAAA,EAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG,EAAE,QAAQ,IAAI,CAAC;AAClB,IAAI,kBAAkB;AACtB,IAAI,YAAY,YAAY,CAAC,eAAe,eAAe,GAAG,EAAE,QAAQ,GAAG,CAAC;AAE5E,IAAI,UAAU;AAEd,IAAI,cAAc;AAElB,IAAI,iBAAiB;AACrB,IAAI,gBAAgB,YAAY;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,OAAO,sdAGgH;AAAA,EAC3H;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,OAAO,2MAC0F;AAAA,EACrG;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,OAAO,6LAC8E;AAAA,EACzF;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG,EAAE,QAAQ,IAAI,QAAQ,GAAG,CAAC;AAC7B,IAAI,mBAAmB,YAAY;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AACF,GAAG,EAAE,QAAQ,OAAO,QAAQ,GAAG,CAAC;AAEhC,IAAI,WAAW,YAAY,CAAC,gBAAgB,eAAe,gBAAgB,GAAG,EAAE,QAAQ,mBAAmB,CAAC;AAE5G,IAAI,UAAU;AAAA,EACZ,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT;AAAA,EACA,UAAU;AACZ;AAGA,SAAS,UAAU,QAAQ,OAAO;AAIhC,MAAI,SAAS,MAAM,YAAY,MAAM,YAAY,SAAS,CAAC;AAC3D,MAAI,UAAU,OAAO,iBAAiB,KAAK,GAAG;AAC5C,UAAM,WAAW,OAAO;AACxB,UAAM,YAAY,IAAI;AACtB,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAEA,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,UAAM,kBAAkB;AACxB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,UAAM,kBAAkB;AACxB,WAAO;AAAA,EACT;AAEA,WAAS,OAAO,SAAS;AACvB,QAAI,OAAO,MAAM,QAAQ,GAAG,CAAC,GAAG;AAC9B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,KAAK,OAAO,KAAK;AAGrB,MAAI,OAAO,KAAK;AACd,WAAO,uBAAuB,QAAQ,KAAK;AAAA,EAC7C;AAEA,MAAI,OAAO,KAAK;AACd,WAAO,cAAc,QAAQ,KAAK;AAAA,EACpC;AAGA,MAAI,OAAO,KAAK;AACd,WAAO,uBAAuB,QAAQ,KAAK;AAAA,EAC7C;AAEA,MAAI,OAAO,OAAO,OAAO,IAAI,GAAG,GAAG;AACjC,UAAM,WAAW;AACjB,WAAO,aAAa,QAAQ,KAAK;AAAA,EACnC;AAEA,MAAI,OAAO,KAAK;AACd,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,KAAK;AACd,QAAI,aAAa,OAAO,IAAI,MAAM;AAClC,QAAI,cAAc,OAAO,IAAI,GAAG;AAC9B,YAAM,WAAW;AACjB,YAAM,aAAa,WAAW,CAAC;AAC/B,aAAO,iBAAiB,QAAQ,KAAK;AAAA,IACvC,WAAW,OAAO,IAAI,GAAG;AACvB,aAAO;AAAA,IACT,WAAW,OAAO,KAAK,EAAE,MAAM,MAAM,GAAG;AACtC,aAAO;AAAA,IACT,WAAW,OAAO,KAAK,EAAE,MAAM,QAAQ,GAAG;AAExC,aAAO,cAAc,QAAQ,KAAK;AAAA,IACpC;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,uBAAuB,QAAQ,OAAO;AAC7C,MAAI;AACJ,UAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,WAAO,KAAK;AAEZ,QAAI,OAAO,OAAO,CAAC,OAAO,IAAI,GAAG,GAAG;AAClC,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,uBAAuB,QAAQ,OAAO;AAC7C,MAAI;AACJ,UAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,QAAI,OAAO,KAAK;AACd,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAEA,WAAO,KAAK;AACZ,QAAI,OAAO,KAAK;AACd,aAAO,KAAK;AACZ;AAAA,IACF;AAEA,QAAI,OAAO,OAAO,CAAC,OAAO,IAAI,GAAG,GAAG;AAClC,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,yBAAyB,QAAQ,OAAO;AAC/C,SAAO,mBAAmB,QAAQ,OAAO,sBAAsB;AACjE;AAEA,SAAS,uBAAuB,QAAQ,OAAO;AAC7C,QAAM,WAAW;AACjB,QAAM,aAAa;AACnB,SAAO,iBAAiB,QAAQ,KAAK;AACvC;AAEA,SAAS,6BAA6B,QAAQ,OAAO;AACnD,SAAO,mBAAmB,QAAQ,OAAO,sBAAsB;AACjE;AAEA,SAAS,mBAAmB,QAAQ,OAAO,gBAAgB;AACzD,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,QAAI,sBAAsB,MAAM;AAChC,UAAM,YAAY,KAAK;AAAA;AAAA,MAErB,kBAAkB,SAASA,QAAO;AAChC,eAAOA,OAAM,mBAAmB;AAAA,MAClC;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW;AACjB,UAAM,kBAAkB;AACxB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,KAAK;AACZ,UAAM,YAAY,KAAK;AAAA,MACrB,kBAAkB,WAAW;AAAE,eAAO;AAAA,MAAM;AAAA,MAC5C,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW;AACjB,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AACF;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO;AACtB,UAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,QAAI,YAAY,MAAM,KAAK;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,OAAO;AAAA,EACrB;AACA,SAAO;AACT;AAEA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,OAAO,IAAI,GAAG,GAAG;AACnB,UAAM,WAAW;AACjB,WAAO,wBAAwB,QAAQ,KAAK;AAAA,EAC9C,WAAW,MAAM,UAAa,GAAG,MAAM,QAAQ,GAAG;AAChD,WAAO,SAAS,QAAQ;AACxB,UAAM,WAAW;AACjB,WAAO;AAAA,EACT,OAAO;AACL,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,wBAAwB,QAAQ,OAAO;AAC9C,MAAI;AACJ,UAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,QAAI,OAAO,KAAK;AACd,YAAM,WAAW;AACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,iBAAiB,QAAQ,OAAO;AACvC,MAAI,QAAQ,MAAM;AAClB,MAAI,OAAO,IAAI,KAAK,OAAO,MAAM,IAAI,OAAO,QAAQ,GAAG,CAAC,GAAG;AACzD,UAAM,WAAW;AAAA,EACnB,WACS,UAAU,KAAK;AACtB,WAAO,CAAC,OAAO,IAAI,GAAG;AACpB,UAAI,KAAK,OAAO,KAAK;AACrB,UAAI,OAAO,KAAK;AACd,cAAM,WAAW;AACjB,eAAO;AAAA,MACT;AAEA,aAAO,KAAK;AACZ,UAAI,OAAO,KAAK;AACd,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF,OACK;AACH,WAAO,UAAU;AAAA,EACnB;AAEA,SAAO;AACT;AAEO,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EAEN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,aAAa,CAAC;AAAA,MACd,gBAAgB;AAAA,MAChB,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,KAAK,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAC7D;AACF;", "names": ["state"]}