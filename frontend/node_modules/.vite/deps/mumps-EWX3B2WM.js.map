{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/mumps.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\", \"i\");\n}\n\nvar singleOperators = new RegExp(\"^[\\\\+\\\\-\\\\*/&#!_?\\\\\\\\<>=\\\\'\\\\[\\\\]]\");\nvar doubleOperators = new RegExp(\"^(('=)|(<=)|(>=)|('>)|('<)|([[)|(]])|(^$))\");\nvar singleDelimiters = new RegExp(\"^[\\\\.,:]\");\nvar brackets = new RegExp(\"[()]\");\nvar identifiers = new RegExp(\"^[%A-Za-z][A-Za-z0-9]*\");\nvar commandKeywords = [\"break\",\"close\",\"do\",\"else\",\"for\",\"goto\", \"halt\", \"hang\", \"if\", \"job\",\"kill\",\"lock\",\"merge\",\"new\",\"open\", \"quit\", \"read\", \"set\", \"tcommit\", \"trollback\", \"tstart\", \"use\", \"view\", \"write\", \"xecute\", \"b\",\"c\",\"d\",\"e\",\"f\",\"g\", \"h\", \"i\", \"j\",\"k\",\"l\",\"m\",\"n\",\"o\", \"q\", \"r\", \"s\", \"tc\", \"tro\", \"ts\", \"u\", \"v\", \"w\", \"x\"];\n// The following list includes intrinsic functions _and_ special variables\nvar intrinsicFuncsWords = [\"\\\\$ascii\", \"\\\\$char\", \"\\\\$data\", \"\\\\$ecode\", \"\\\\$estack\", \"\\\\$etrap\", \"\\\\$extract\", \"\\\\$find\", \"\\\\$fnumber\", \"\\\\$get\", \"\\\\$horolog\", \"\\\\$io\", \"\\\\$increment\", \"\\\\$job\", \"\\\\$justify\", \"\\\\$length\", \"\\\\$name\", \"\\\\$next\", \"\\\\$order\", \"\\\\$piece\", \"\\\\$qlength\", \"\\\\$qsubscript\", \"\\\\$query\", \"\\\\$quit\", \"\\\\$random\", \"\\\\$reverse\", \"\\\\$select\", \"\\\\$stack\", \"\\\\$test\", \"\\\\$text\", \"\\\\$translate\", \"\\\\$view\", \"\\\\$x\", \"\\\\$y\", \"\\\\$a\", \"\\\\$c\", \"\\\\$d\", \"\\\\$e\", \"\\\\$ec\", \"\\\\$es\", \"\\\\$et\", \"\\\\$f\", \"\\\\$fn\", \"\\\\$g\", \"\\\\$h\", \"\\\\$i\", \"\\\\$j\", \"\\\\$l\", \"\\\\$n\", \"\\\\$na\", \"\\\\$o\", \"\\\\$p\", \"\\\\$q\", \"\\\\$ql\", \"\\\\$qs\", \"\\\\$r\", \"\\\\$re\", \"\\\\$s\", \"\\\\$st\", \"\\\\$t\", \"\\\\$tr\", \"\\\\$v\", \"\\\\$z\"];\nvar intrinsicFuncs = wordRegexp(intrinsicFuncsWords);\nvar command = wordRegexp(commandKeywords);\n\nfunction tokenBase(stream, state) {\n  if (stream.sol()) {\n    state.label = true;\n    state.commandMode = 0;\n  }\n\n  // The <space> character has meaning in MUMPS. Ignoring consecutive\n  // spaces would interfere with interpreting whether the next non-space\n  // character belongs to the command or argument context.\n\n  // Examine each character and update a mode variable whose interpretation is:\n  //   >0 => command    0 => argument    <0 => command post-conditional\n  var ch = stream.peek();\n\n  if (ch == \" \" || ch == \"\\t\") { // Pre-process <space>\n    state.label = false;\n    if (state.commandMode == 0)\n      state.commandMode = 1;\n    else if ((state.commandMode < 0) || (state.commandMode == 2))\n      state.commandMode = 0;\n  } else if ((ch != \".\") && (state.commandMode > 0)) {\n    if (ch == \":\")\n      state.commandMode = -1;   // SIS - Command post-conditional\n    else\n      state.commandMode = 2;\n  }\n\n  // Do not color parameter list as line tag\n  if ((ch === \"(\") || (ch === \"\\u0009\"))\n    state.label = false;\n\n  // MUMPS comment starts with \";\"\n  if (ch === \";\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  // Number Literals // SIS/RLM - MUMPS permits canonic number followed by concatenate operator\n  if (stream.match(/^[-+]?\\d+(\\.\\d+)?([eE][-+]?\\d+)?/))\n    return \"number\";\n\n  // Handle Strings\n  if (ch == '\"') {\n    if (stream.skipTo('\"')) {\n      stream.next();\n      return \"string\";\n    } else {\n      stream.skipToEnd();\n      return \"error\";\n    }\n  }\n\n  // Handle operators and Delimiters\n  if (stream.match(doubleOperators) || stream.match(singleOperators))\n    return \"operator\";\n\n  // Prevents leading \".\" in DO block from falling through to error\n  if (stream.match(singleDelimiters))\n    return null;\n\n  if (brackets.test(ch)) {\n    stream.next();\n    return \"bracket\";\n  }\n\n  if (state.commandMode > 0 && stream.match(command))\n    return \"controlKeyword\";\n\n  if (stream.match(intrinsicFuncs))\n    return \"builtin\";\n\n  if (stream.match(identifiers))\n    return \"variable\";\n\n  // Detect dollar-sign when not a documented intrinsic function\n  // \"^\" may introduce a GVN or SSVN - Color same as function\n  if (ch === \"$\" || ch === \"^\") {\n    stream.next();\n    return \"builtin\";\n  }\n\n  // MUMPS Indirection\n  if (ch === \"@\") {\n    stream.next();\n    return \"string.special\";\n  }\n\n  if (/[\\w%]/.test(ch)) {\n    stream.eatWhile(/[\\w%]/);\n    return \"variable\";\n  }\n\n  // Handle non-detected items\n  stream.next();\n  return \"error\";\n}\n\nexport const mumps = {\n  name: \"mumps\",\n  startState: function() {\n    return {\n      label: false,\n      commandMode: 0\n    };\n  },\n\n  token: function(stream, state) {\n    var style = tokenBase(stream, state);\n    if (state.label) return \"tag\";\n    return style;\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,WAAW,OAAO;AACzB,SAAO,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,IAAI,SAAS,GAAG;AAC5D;AAEA,IAAI,kBAAkB,IAAI,OAAO,oCAAoC;AACrE,IAAI,kBAAkB,IAAI,OAAO,4CAA4C;AAC7E,IAAI,mBAAmB,IAAI,OAAO,UAAU;AAC5C,IAAI,WAAW,IAAI,OAAO,MAAM;AAChC,IAAI,cAAc,IAAI,OAAO,wBAAwB;AACrD,IAAI,kBAAkB,CAAC,SAAQ,SAAQ,MAAK,QAAO,OAAM,QAAQ,QAAQ,QAAQ,MAAM,OAAM,QAAO,QAAO,SAAQ,OAAM,QAAQ,QAAQ,QAAQ,OAAO,WAAW,aAAa,UAAU,OAAO,QAAQ,SAAS,UAAU,KAAI,KAAI,KAAI,KAAI,KAAI,KAAK,KAAK,KAAK,KAAI,KAAI,KAAI,KAAI,KAAI,KAAK,KAAK,KAAK,KAAK,MAAM,OAAO,MAAM,KAAK,KAAK,KAAK,GAAG;AAE5U,IAAI,sBAAsB,CAAC,YAAY,WAAW,WAAW,YAAY,aAAa,YAAY,cAAc,WAAW,cAAc,UAAU,cAAc,SAAS,gBAAgB,UAAU,cAAc,aAAa,WAAW,WAAW,YAAY,YAAY,cAAc,iBAAiB,YAAY,WAAW,aAAa,cAAc,aAAa,YAAY,WAAW,WAAW,gBAAgB,WAAW,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,SAAS,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM;AACxqB,IAAI,iBAAiB,WAAW,mBAAmB;AACnD,IAAI,UAAU,WAAW,eAAe;AAExC,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,OAAO,IAAI,GAAG;AAChB,UAAM,QAAQ;AACd,UAAM,cAAc;AAAA,EACtB;AAQA,MAAI,KAAK,OAAO,KAAK;AAErB,MAAI,MAAM,OAAO,MAAM,KAAM;AAC3B,UAAM,QAAQ;AACd,QAAI,MAAM,eAAe;AACvB,YAAM,cAAc;AAAA,aACZ,MAAM,cAAc,KAAO,MAAM,eAAe;AACxD,YAAM,cAAc;AAAA,EACxB,WAAY,MAAM,OAAS,MAAM,cAAc,GAAI;AACjD,QAAI,MAAM;AACR,YAAM,cAAc;AAAA;AAEpB,YAAM,cAAc;AAAA,EACxB;AAGA,MAAK,OAAO,OAAS,OAAO;AAC1B,UAAM,QAAQ;AAGhB,MAAI,OAAO,KAAK;AACd,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,kCAAkC;AACjD,WAAO;AAGT,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,OAAO,GAAG,GAAG;AACtB,aAAO,KAAK;AACZ,aAAO;AAAA,IACT,OAAO;AACL,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AAGA,MAAI,OAAO,MAAM,eAAe,KAAK,OAAO,MAAM,eAAe;AAC/D,WAAO;AAGT,MAAI,OAAO,MAAM,gBAAgB;AAC/B,WAAO;AAET,MAAI,SAAS,KAAK,EAAE,GAAG;AACrB,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,cAAc,KAAK,OAAO,MAAM,OAAO;AAC/C,WAAO;AAET,MAAI,OAAO,MAAM,cAAc;AAC7B,WAAO;AAET,MAAI,OAAO,MAAM,WAAW;AAC1B,WAAO;AAIT,MAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,KAAK;AACd,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,WAAO,SAAS,OAAO;AACvB,WAAO;AAAA,EACT;AAGA,SAAO,KAAK;AACZ,SAAO;AACT;AAEO,IAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,QAAQ,UAAU,QAAQ,KAAK;AACnC,QAAI,MAAM,MAAO,QAAO;AACxB,WAAO;AAAA,EACT;AACF;", "names": []}