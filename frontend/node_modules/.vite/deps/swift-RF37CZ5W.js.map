{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/swift.js"], "sourcesContent": ["function wordSet(words) {\n  var set = {}\n  for (var i = 0; i < words.length; i++) set[words[i]] = true\n  return set\n}\n\nvar keywords = wordSet([\"_\",\"var\",\"let\",\"actor\",\"class\",\"enum\",\"extension\",\"import\",\"protocol\",\"struct\",\"func\",\"typealias\",\"associatedtype\",\n                        \"open\",\"public\",\"internal\",\"fileprivate\",\"private\",\"deinit\",\"init\",\"new\",\"override\",\"self\",\"subscript\",\"super\",\n                        \"convenience\",\"dynamic\",\"final\",\"indirect\",\"lazy\",\"required\",\"static\",\"unowned\",\"unowned(safe)\",\"unowned(unsafe)\",\"weak\",\"as\",\"is\",\n                        \"break\",\"case\",\"continue\",\"default\",\"else\",\"fallthrough\",\"for\",\"guard\",\"if\",\"in\",\"repeat\",\"switch\",\"where\",\"while\",\n                        \"defer\",\"return\",\"inout\",\"mutating\",\"nonmutating\",\"isolated\",\"nonisolated\",\"catch\",\"do\",\"rethrows\",\"throw\",\"throws\",\"async\",\"await\",\"try\",\"didSet\",\"get\",\"set\",\"willSet\",\n                        \"assignment\",\"associativity\",\"infix\",\"left\",\"none\",\"operator\",\"postfix\",\"precedence\",\"precedencegroup\",\"prefix\",\"right\",\n                        \"Any\",\"AnyObject\",\"Type\",\"dynamicType\",\"Self\",\"Protocol\",\"__COLUMN__\",\"__FILE__\",\"__FUNCTION__\",\"__LINE__\"])\nvar definingKeywords = wordSet([\"var\",\"let\",\"actor\",\"class\",\"enum\",\"extension\",\"import\",\"protocol\",\"struct\",\"func\",\"typealias\",\"associatedtype\",\"for\"])\nvar atoms = wordSet([\"true\",\"false\",\"nil\",\"self\",\"super\",\"_\"])\nvar types = wordSet([\"Array\",\"Bool\",\"Character\",\"Dictionary\",\"Double\",\"Float\",\"Int\",\"Int8\",\"Int16\",\"Int32\",\"Int64\",\"Never\",\"Optional\",\"Set\",\"String\",\n                     \"UInt8\",\"UInt16\",\"UInt32\",\"UInt64\",\"Void\"])\nvar operators = \"+-/*%=|&<>~^?!\"\nvar punc = \":;,.(){}[]\"\nvar binary = /^\\-?0b[01][01_]*/\nvar octal = /^\\-?0o[0-7][0-7_]*/\nvar hexadecimal = /^\\-?0x[\\dA-Fa-f][\\dA-Fa-f_]*(?:(?:\\.[\\dA-Fa-f][\\dA-Fa-f_]*)?[Pp]\\-?\\d[\\d_]*)?/\nvar decimal = /^\\-?\\d[\\d_]*(?:\\.\\d[\\d_]*)?(?:[Ee]\\-?\\d[\\d_]*)?/\nvar identifier = /^\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1/\nvar property = /^\\.(?:\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1)/\nvar instruction = /^\\#[A-Za-z]+/\nvar attribute = /^@(?:\\$\\d+|(`?)[_A-Za-z][_A-Za-z$0-9]*\\1)/\n//var regexp = /^\\/(?!\\s)(?:\\/\\/)?(?:\\\\.|[^\\/])+\\//\n\nfunction tokenBase(stream, state, prev) {\n  if (stream.sol()) state.indented = stream.indentation()\n  if (stream.eatSpace()) return null\n\n  var ch = stream.peek()\n  if (ch == \"/\") {\n    if (stream.match(\"//\")) {\n      stream.skipToEnd()\n      return \"comment\"\n    }\n    if (stream.match(\"/*\")) {\n      state.tokenize.push(tokenComment)\n      return tokenComment(stream, state)\n    }\n  }\n  if (stream.match(instruction)) return \"builtin\"\n  if (stream.match(attribute)) return \"attribute\"\n  if (stream.match(binary)) return \"number\"\n  if (stream.match(octal)) return \"number\"\n  if (stream.match(hexadecimal)) return \"number\"\n  if (stream.match(decimal)) return \"number\"\n  if (stream.match(property)) return \"property\"\n  if (operators.indexOf(ch) > -1) {\n    stream.next()\n    return \"operator\"\n  }\n  if (punc.indexOf(ch) > -1) {\n    stream.next()\n    stream.match(\"..\")\n    return \"punctuation\"\n  }\n  var stringMatch\n  if (stringMatch = stream.match(/(\"\"\"|\"|')/)) {\n    var tokenize = tokenString.bind(null, stringMatch[0])\n    state.tokenize.push(tokenize)\n    return tokenize(stream, state)\n  }\n\n  if (stream.match(identifier)) {\n    var ident = stream.current()\n    if (types.hasOwnProperty(ident)) return \"type\"\n    if (atoms.hasOwnProperty(ident)) return \"atom\"\n    if (keywords.hasOwnProperty(ident)) {\n      if (definingKeywords.hasOwnProperty(ident))\n        state.prev = \"define\"\n      return \"keyword\"\n    }\n    if (prev == \"define\") return \"def\"\n    return \"variable\"\n  }\n\n  stream.next()\n  return null\n}\n\nfunction tokenUntilClosingParen() {\n  var depth = 0\n  return function(stream, state, prev) {\n    var inner = tokenBase(stream, state, prev)\n    if (inner == \"punctuation\") {\n      if (stream.current() == \"(\") ++depth\n      else if (stream.current() == \")\") {\n        if (depth == 0) {\n          stream.backUp(1)\n          state.tokenize.pop()\n          return state.tokenize[state.tokenize.length - 1](stream, state)\n        }\n        else --depth\n      }\n    }\n    return inner\n  }\n}\n\nfunction tokenString(openQuote, stream, state) {\n  var singleLine = openQuote.length == 1\n  var ch, escaped = false\n  while (ch = stream.peek()) {\n    if (escaped) {\n      stream.next()\n      if (ch == \"(\") {\n        state.tokenize.push(tokenUntilClosingParen())\n        return \"string\"\n      }\n      escaped = false\n    } else if (stream.match(openQuote)) {\n      state.tokenize.pop()\n      return \"string\"\n    } else {\n      stream.next()\n      escaped = ch == \"\\\\\"\n    }\n  }\n  if (singleLine) {\n    state.tokenize.pop()\n  }\n  return \"string\"\n}\n\nfunction tokenComment(stream, state) {\n  var ch\n  while (ch = stream.next()) {\n    if (ch === \"/\" && stream.eat(\"*\")) {\n      state.tokenize.push(tokenComment)\n    } else if (ch === \"*\" && stream.eat(\"/\")) {\n      state.tokenize.pop()\n      break\n    }\n  }\n  return \"comment\"\n}\n\nfunction Context(prev, align, indented) {\n  this.prev = prev\n  this.align = align\n  this.indented = indented\n}\n\nfunction pushContext(state, stream) {\n  var align = stream.match(/^\\s*($|\\/[\\/\\*]|[)}\\]])/, false) ? null : stream.column() + 1\n  state.context = new Context(state.context, align, state.indented)\n}\n\nfunction popContext(state) {\n  if (state.context) {\n    state.indented = state.context.indented\n    state.context = state.context.prev\n  }\n}\n\nexport const swift = {\n  name: \"swift\",\n  startState: function() {\n    return {\n      prev: null,\n      context: null,\n      indented: 0,\n      tokenize: []\n    }\n  },\n\n  token: function(stream, state) {\n    var prev = state.prev\n    state.prev = null\n    var tokenize = state.tokenize[state.tokenize.length - 1] || tokenBase\n    var style = tokenize(stream, state, prev)\n    if (!style || style == \"comment\") state.prev = prev\n    else if (!state.prev) state.prev = style\n\n    if (style == \"punctuation\") {\n      var bracket = /[\\(\\[\\{]|([\\]\\)\\}])/.exec(stream.current())\n      if (bracket) (bracket[1] ? popContext : pushContext)(state, stream)\n    }\n\n    return style\n  },\n\n  indent: function(state, textAfter, iCx) {\n    var cx = state.context\n    if (!cx) return 0\n    var closing = /^[\\]\\}\\)]/.test(textAfter)\n    if (cx.align != null) return cx.align - (closing ? 1 : 0)\n    return cx.indented + (closing ? 0 : iCx.unit)\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[\\)\\}\\]]$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]}\n  }\n}\n"], "mappings": ";;;AAAA,SAAS,QAAQ,OAAO;AACtB,MAAI,MAAM,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,KAAI,MAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AAEA,IAAI,WAAW,QAAQ;AAAA,EAAC;AAAA,EAAI;AAAA,EAAM;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAY;AAAA,EAAS;AAAA,EAAW;AAAA,EAAS;AAAA,EAAO;AAAA,EAAY;AAAA,EACnG;AAAA,EAAO;AAAA,EAAS;AAAA,EAAW;AAAA,EAAc;AAAA,EAAU;AAAA,EAAS;AAAA,EAAO;AAAA,EAAM;AAAA,EAAW;AAAA,EAAO;AAAA,EAAY;AAAA,EACvG;AAAA,EAAc;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAO;AAAA,EAAW;AAAA,EAAS;AAAA,EAAU;AAAA,EAAgB;AAAA,EAAkB;AAAA,EAAO;AAAA,EAAK;AAAA,EAC9H;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAW;AAAA,EAAU;AAAA,EAAO;AAAA,EAAc;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAK;AAAA,EAAK;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAC3G;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAc;AAAA,EAAW;AAAA,EAAc;AAAA,EAAQ;AAAA,EAAK;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAS;AAAA,EAAM;AAAA,EAAM;AAAA,EAC/J;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAW;AAAA,EAAU;AAAA,EAAa;AAAA,EAAkB;AAAA,EAAS;AAAA,EAChH;AAAA,EAAM;AAAA,EAAY;AAAA,EAAO;AAAA,EAAc;AAAA,EAAO;AAAA,EAAW;AAAA,EAAa;AAAA,EAAW;AAAA,EAAe;AAAU,CAAC;AACnI,IAAI,mBAAmB,QAAQ,CAAC,OAAM,OAAM,SAAQ,SAAQ,QAAO,aAAY,UAAS,YAAW,UAAS,QAAO,aAAY,kBAAiB,KAAK,CAAC;AACtJ,IAAI,QAAQ,QAAQ,CAAC,QAAO,SAAQ,OAAM,QAAO,SAAQ,GAAG,CAAC;AAC7D,IAAI,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAY;AAAA,EAAa;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAM;AAAA,EACvH;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAM,CAAC;AAC/D,IAAI,YAAY;AAChB,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,YAAY;AAGhB,SAAS,UAAU,QAAQ,OAAO,MAAM;AACtC,MAAI,OAAO,IAAI,EAAG,OAAM,WAAW,OAAO,YAAY;AACtD,MAAI,OAAO,SAAS,EAAG,QAAO;AAE9B,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,MAAM,IAAI,GAAG;AACtB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,IAAI,GAAG;AACtB,YAAM,SAAS,KAAK,YAAY;AAChC,aAAO,aAAa,QAAQ,KAAK;AAAA,IACnC;AAAA,EACF;AACA,MAAI,OAAO,MAAM,WAAW,EAAG,QAAO;AACtC,MAAI,OAAO,MAAM,SAAS,EAAG,QAAO;AACpC,MAAI,OAAO,MAAM,MAAM,EAAG,QAAO;AACjC,MAAI,OAAO,MAAM,KAAK,EAAG,QAAO;AAChC,MAAI,OAAO,MAAM,WAAW,EAAG,QAAO;AACtC,MAAI,OAAO,MAAM,OAAO,EAAG,QAAO;AAClC,MAAI,OAAO,MAAM,QAAQ,EAAG,QAAO;AACnC,MAAI,UAAU,QAAQ,EAAE,IAAI,IAAI;AAC9B,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AACA,MAAI,KAAK,QAAQ,EAAE,IAAI,IAAI;AACzB,WAAO,KAAK;AACZ,WAAO,MAAM,IAAI;AACjB,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI,cAAc,OAAO,MAAM,WAAW,GAAG;AAC3C,QAAI,WAAW,YAAY,KAAK,MAAM,YAAY,CAAC,CAAC;AACpD,UAAM,SAAS,KAAK,QAAQ;AAC5B,WAAO,SAAS,QAAQ,KAAK;AAAA,EAC/B;AAEA,MAAI,OAAO,MAAM,UAAU,GAAG;AAC5B,QAAI,QAAQ,OAAO,QAAQ;AAC3B,QAAI,MAAM,eAAe,KAAK,EAAG,QAAO;AACxC,QAAI,MAAM,eAAe,KAAK,EAAG,QAAO;AACxC,QAAI,SAAS,eAAe,KAAK,GAAG;AAClC,UAAI,iBAAiB,eAAe,KAAK;AACvC,cAAM,OAAO;AACf,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,SAAU,QAAO;AAC7B,WAAO;AAAA,EACT;AAEA,SAAO,KAAK;AACZ,SAAO;AACT;AAEA,SAAS,yBAAyB;AAChC,MAAI,QAAQ;AACZ,SAAO,SAAS,QAAQ,OAAO,MAAM;AACnC,QAAI,QAAQ,UAAU,QAAQ,OAAO,IAAI;AACzC,QAAI,SAAS,eAAe;AAC1B,UAAI,OAAO,QAAQ,KAAK,IAAK,GAAE;AAAA,eACtB,OAAO,QAAQ,KAAK,KAAK;AAChC,YAAI,SAAS,GAAG;AACd,iBAAO,OAAO,CAAC;AACf,gBAAM,SAAS,IAAI;AACnB,iBAAO,MAAM,SAAS,MAAM,SAAS,SAAS,CAAC,EAAE,QAAQ,KAAK;AAAA,QAChE,MACK,GAAE;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,WAAW,QAAQ,OAAO;AAC7C,MAAI,aAAa,UAAU,UAAU;AACrC,MAAI,IAAI,UAAU;AAClB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,SAAS;AACX,aAAO,KAAK;AACZ,UAAI,MAAM,KAAK;AACb,cAAM,SAAS,KAAK,uBAAuB,CAAC;AAC5C,eAAO;AAAA,MACT;AACA,gBAAU;AAAA,IACZ,WAAW,OAAO,MAAM,SAAS,GAAG;AAClC,YAAM,SAAS,IAAI;AACnB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,KAAK;AACZ,gBAAU,MAAM;AAAA,IAClB;AAAA,EACF;AACA,MAAI,YAAY;AACd,UAAM,SAAS,IAAI;AAAA,EACrB;AACA,SAAO;AACT;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI;AACJ,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,OAAO,OAAO,OAAO,IAAI,GAAG,GAAG;AACjC,YAAM,SAAS,KAAK,YAAY;AAAA,IAClC,WAAW,OAAO,OAAO,OAAO,IAAI,GAAG,GAAG;AACxC,YAAM,SAAS,IAAI;AACnB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,MAAM,OAAO,UAAU;AACtC,OAAK,OAAO;AACZ,OAAK,QAAQ;AACb,OAAK,WAAW;AAClB;AAEA,SAAS,YAAY,OAAO,QAAQ;AAClC,MAAI,QAAQ,OAAO,MAAM,2BAA2B,KAAK,IAAI,OAAO,OAAO,OAAO,IAAI;AACtF,QAAM,UAAU,IAAI,QAAQ,MAAM,SAAS,OAAO,MAAM,QAAQ;AAClE;AAEA,SAAS,WAAW,OAAO;AACzB,MAAI,MAAM,SAAS;AACjB,UAAM,WAAW,MAAM,QAAQ;AAC/B,UAAM,UAAU,MAAM,QAAQ;AAAA,EAChC;AACF;AAEO,IAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,MAAM;AACjB,UAAM,OAAO;AACb,QAAI,WAAW,MAAM,SAAS,MAAM,SAAS,SAAS,CAAC,KAAK;AAC5D,QAAI,QAAQ,SAAS,QAAQ,OAAO,IAAI;AACxC,QAAI,CAAC,SAAS,SAAS,UAAW,OAAM,OAAO;AAAA,aACtC,CAAC,MAAM,KAAM,OAAM,OAAO;AAEnC,QAAI,SAAS,eAAe;AAC1B,UAAI,UAAU,sBAAsB,KAAK,OAAO,QAAQ,CAAC;AACzD,UAAI,QAAS,EAAC,QAAQ,CAAC,IAAI,aAAa,aAAa,OAAO,MAAM;AAAA,IACpE;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,KAAK;AACtC,QAAI,KAAK,MAAM;AACf,QAAI,CAAC,GAAI,QAAO;AAChB,QAAI,UAAU,YAAY,KAAK,SAAS;AACxC,QAAI,GAAG,SAAS,KAAM,QAAO,GAAG,SAAS,UAAU,IAAI;AACvD,WAAO,GAAG,YAAY,UAAU,IAAI,IAAI;AAAA,EAC1C;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,IAC5D,eAAe,EAAC,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,EAAC;AAAA,EAC1D;AACF;", "names": []}