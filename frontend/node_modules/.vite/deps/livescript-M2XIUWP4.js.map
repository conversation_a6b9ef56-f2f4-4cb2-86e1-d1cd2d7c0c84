{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/livescript.js"], "sourcesContent": ["var tokenBase = function(stream, state) {\n  var next_rule = state.next || \"start\";\n  if (next_rule) {\n    state.next = state.next;\n    var nr = Rules[next_rule];\n    if (nr.splice) {\n      for (var i$ = 0; i$ < nr.length; ++i$) {\n        var r = nr[i$];\n        if (r.regex && stream.match(r.regex)) {\n          state.next = r.next || state.next;\n          return r.token;\n        }\n      }\n      stream.next();\n      return 'error';\n    }\n    if (stream.match(r = Rules[next_rule])) {\n      if (r.regex && stream.match(r.regex)) {\n        state.next = r.next;\n        return r.token;\n      } else {\n        stream.next();\n        return 'error';\n      }\n    }\n  }\n  stream.next();\n  return 'error';\n};\n\nvar identifier = '(?![\\\\d\\\\s])[$\\\\w\\\\xAA-\\\\uFFDC](?:(?!\\\\s)[$\\\\w\\\\xAA-\\\\uFFDC]|-[A-Za-z])*';\nvar indenter = RegExp('(?:[({[=:]|[-~]>|\\\\b(?:e(?:lse|xport)|d(?:o|efault)|t(?:ry|hen)|finally|import(?:\\\\s*all)?|const|var|let|new|catch(?:\\\\s*' + identifier + ')?))\\\\s*$');\nvar keywordend = '(?![$\\\\w]|-[A-Za-z]|\\\\s*:(?![:=]))';\nvar stringfill = {\n  token: 'string',\n  regex: '.+'\n};\nvar Rules = {\n  start: [\n    {\n      token: 'docComment',\n      regex: '/\\\\*',\n      next: 'comment'\n    }, {\n      token: 'comment',\n      regex: '#.*'\n    }, {\n      token: 'keyword',\n      regex: '(?:t(?:h(?:is|row|en)|ry|ypeof!?)|c(?:on(?:tinue|st)|a(?:se|tch)|lass)|i(?:n(?:stanceof)?|mp(?:ort(?:\\\\s+all)?|lements)|[fs])|d(?:e(?:fault|lete|bugger)|o)|f(?:or(?:\\\\s+own)?|inally|unction)|s(?:uper|witch)|e(?:lse|x(?:tends|port)|val)|a(?:nd|rguments)|n(?:ew|ot)|un(?:less|til)|w(?:hile|ith)|o[fr]|return|break|let|var|loop)' + keywordend\n    }, {\n      token: 'atom',\n      regex: '(?:true|false|yes|no|on|off|null|void|undefined)' + keywordend\n    }, {\n      token: 'invalid',\n      regex: '(?:p(?:ackage|r(?:ivate|otected)|ublic)|i(?:mplements|nterface)|enum|static|yield)' + keywordend\n    }, {\n      token: 'className.standard',\n      regex: '(?:R(?:e(?:gExp|ferenceError)|angeError)|S(?:tring|yntaxError)|E(?:rror|valError)|Array|Boolean|Date|Function|Number|Object|TypeError|URIError)' + keywordend\n    }, {\n      token: 'variableName.function.standard',\n      regex: '(?:is(?:NaN|Finite)|parse(?:Int|Float)|Math|JSON|(?:en|de)codeURI(?:Component)?)' + keywordend\n    }, {\n      token: 'variableName.standard',\n      regex: '(?:t(?:hat|il|o)|f(?:rom|allthrough)|it|by|e)' + keywordend\n    }, {\n      token: 'variableName',\n      regex: identifier + '\\\\s*:(?![:=])'\n    }, {\n      token: 'variableName',\n      regex: identifier\n    }, {\n      token: 'operatorKeyword',\n      regex: '(?:\\\\.{3}|\\\\s+\\\\?)'\n    }, {\n      token: 'keyword',\n      regex: '(?:@+|::|\\\\.\\\\.)',\n      next: 'key'\n    }, {\n      token: 'operatorKeyword',\n      regex: '\\\\.\\\\s*',\n      next: 'key'\n    }, {\n      token: 'string',\n      regex: '\\\\\\\\\\\\S[^\\\\s,;)}\\\\]]*'\n    }, {\n      token: 'docString',\n      regex: '\\'\\'\\'',\n      next: 'qdoc'\n    }, {\n      token: 'docString',\n      regex: '\"\"\"',\n      next: 'qqdoc'\n    }, {\n      token: 'string',\n      regex: '\\'',\n      next: 'qstring'\n    }, {\n      token: 'string',\n      regex: '\"',\n      next: 'qqstring'\n    }, {\n      token: 'string',\n      regex: '`',\n      next: 'js'\n    }, {\n      token: 'string',\n      regex: '<\\\\[',\n      next: 'words'\n    }, {\n      token: 'regexp',\n      regex: '//',\n      next: 'heregex'\n    }, {\n      token: 'regexp',\n      regex: '\\\\/(?:[^[\\\\/\\\\n\\\\\\\\]*(?:(?:\\\\\\\\.|\\\\[[^\\\\]\\\\n\\\\\\\\]*(?:\\\\\\\\.[^\\\\]\\\\n\\\\\\\\]*)*\\\\])[^[\\\\/\\\\n\\\\\\\\]*)*)\\\\/[gimy$]{0,4}',\n      next: 'key'\n    }, {\n      token: 'number',\n      regex: '(?:0x[\\\\da-fA-F][\\\\da-fA-F_]*|(?:[2-9]|[12]\\\\d|3[0-6])r[\\\\da-zA-Z][\\\\da-zA-Z_]*|(?:\\\\d[\\\\d_]*(?:\\\\.\\\\d[\\\\d_]*)?|\\\\.\\\\d[\\\\d_]*)(?:e[+-]?\\\\d[\\\\d_]*)?[\\\\w$]*)'\n    }, {\n      token: 'paren',\n      regex: '[({[]'\n    }, {\n      token: 'paren',\n      regex: '[)}\\\\]]',\n      next: 'key'\n    }, {\n      token: 'operatorKeyword',\n      regex: '\\\\S+'\n    }, {\n      token: 'content',\n      regex: '\\\\s+'\n    }\n  ],\n  heregex: [\n    {\n      token: 'regexp',\n      regex: '.*?//[gimy$?]{0,4}',\n      next: 'start'\n    }, {\n      token: 'regexp',\n      regex: '\\\\s*#{'\n    }, {\n      token: 'comment',\n      regex: '\\\\s+(?:#.*)?'\n    }, {\n      token: 'regexp',\n      regex: '\\\\S+'\n    }\n  ],\n  key: [\n    {\n      token: 'operatorKeyword',\n      regex: '[.?@!]+'\n    }, {\n      token: 'variableName',\n      regex: identifier,\n      next: 'start'\n    }, {\n      token: 'content',\n      regex: '',\n      next: 'start'\n    }\n  ],\n  comment: [\n    {\n      token: 'docComment',\n      regex: '.*?\\\\*/',\n      next: 'start'\n    }, {\n      token: 'docComment',\n      regex: '.+'\n    }\n  ],\n  qdoc: [\n    {\n      token: 'string',\n      regex: \".*?'''\",\n      next: 'key'\n    }, stringfill\n  ],\n  qqdoc: [\n    {\n      token: 'string',\n      regex: '.*?\"\"\"',\n      next: 'key'\n    }, stringfill\n  ],\n  qstring: [\n    {\n      token: 'string',\n      regex: '[^\\\\\\\\\\']*(?:\\\\\\\\.[^\\\\\\\\\\']*)*\\'',\n      next: 'key'\n    }, stringfill\n  ],\n  qqstring: [\n    {\n      token: 'string',\n      regex: '[^\\\\\\\\\"]*(?:\\\\\\\\.[^\\\\\\\\\"]*)*\"',\n      next: 'key'\n    }, stringfill\n  ],\n  js: [\n    {\n      token: 'string',\n      regex: '[^\\\\\\\\`]*(?:\\\\\\\\.[^\\\\\\\\`]*)*`',\n      next: 'key'\n    }, stringfill\n  ],\n  words: [\n    {\n      token: 'string',\n      regex: '.*?\\\\]>',\n      next: 'key'\n    }, stringfill\n  ]\n};\nfor (var idx in Rules) {\n  var r = Rules[idx];\n  if (r.splice) {\n    for (var i = 0, len = r.length; i < len; ++i) {\n      var rr = r[i];\n      if (typeof rr.regex === 'string') {\n        Rules[idx][i].regex = new RegExp('^' + rr.regex);\n      }\n    }\n  } else if (typeof rr.regex === 'string') {\n    Rules[idx].regex = new RegExp('^' + r.regex);\n  }\n}\n\nexport const liveScript = {\n  name: \"livescript\",\n  startState: function(){\n    return {\n      next: 'start',\n      lastToken: {style: null, indent: 0, content: \"\"}\n    };\n  },\n  token: function(stream, state){\n    while (stream.pos == stream.start)\n      var style = tokenBase(stream, state);\n    state.lastToken = {\n      style: style,\n      indent: stream.indentation(),\n      content: stream.current()\n    };\n    return style.replace(/\\./g, ' ');\n  },\n  indent: function(state){\n    var indentation = state.lastToken.indent;\n    if (state.lastToken.content.match(indenter)) {\n      indentation += 2;\n    }\n    return indentation;\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,YAAY,SAAS,QAAQ,OAAO;AACtC,MAAI,YAAY,MAAM,QAAQ;AAC9B,MAAI,WAAW;AACb,UAAM,OAAO,MAAM;AACnB,QAAI,KAAK,MAAM,SAAS;AACxB,QAAI,GAAG,QAAQ;AACb,eAAS,KAAK,GAAG,KAAK,GAAG,QAAQ,EAAE,IAAI;AACrC,YAAI,IAAI,GAAG,EAAE;AACb,YAAI,EAAE,SAAS,OAAO,MAAM,EAAE,KAAK,GAAG;AACpC,gBAAM,OAAO,EAAE,QAAQ,MAAM;AAC7B,iBAAO,EAAE;AAAA,QACX;AAAA,MACF;AACA,aAAO,KAAK;AACZ,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,IAAI,MAAM,SAAS,CAAC,GAAG;AACtC,UAAI,EAAE,SAAS,OAAO,MAAM,EAAE,KAAK,GAAG;AACpC,cAAM,OAAO,EAAE;AACf,eAAO,EAAE;AAAA,MACX,OAAO;AACL,eAAO,KAAK;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO,KAAK;AACZ,SAAO;AACT;AAEA,IAAI,aAAa;AACjB,IAAI,WAAW,OAAO,8HAA8H,aAAa,WAAW;AAC5K,IAAI,aAAa;AACjB,IAAI,aAAa;AAAA,EACf,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAI,QAAQ;AAAA,EACV,OAAO;AAAA,IACL;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO,0UAA0U;AAAA,IACnV;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO,qDAAqD;AAAA,IAC9D;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO,uFAAuF;AAAA,IAChG;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO,oJAAoJ;AAAA,IAC7J;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO,qFAAqF;AAAA,IAC9F;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO,kDAAkD;AAAA,IAC3D;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO,aAAa;AAAA,IACtB;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,KAAK;AAAA,IACH;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,MACD,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,EACL;AAAA,EACA,OAAO;AAAA,IACL;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,EACL;AAAA,EACA,SAAS;AAAA,IACP;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,EACL;AAAA,EACA,UAAU;AAAA,IACR;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,EACL;AAAA,EACA,IAAI;AAAA,IACF;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,EACL;AAAA,EACA,OAAO;AAAA,IACL;AAAA,MACE,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IAAG;AAAA,EACL;AACF;AACA,KAAS,OAAO,OAAO;AACjB,MAAI,MAAM,GAAG;AACjB,MAAI,EAAE,QAAQ;AACZ,SAAS,IAAI,GAAG,MAAM,EAAE,QAAQ,IAAI,KAAK,EAAE,GAAG;AACxC,WAAK,EAAE,CAAC;AACZ,UAAI,OAAO,GAAG,UAAU,UAAU;AAChC,cAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,IAAI,OAAO,MAAM,GAAG,KAAK;AAAA,MACjD;AAAA,IACF;AAAA,EACF,WAAW,OAAO,GAAG,UAAU,UAAU;AACvC,UAAM,GAAG,EAAE,QAAQ,IAAI,OAAO,MAAM,EAAE,KAAK;AAAA,EAC7C;AACF;AAXM;AAGI;AADG;AAAO;AAHX;AAcF,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EACN,YAAY,WAAU;AACpB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,WAAW,EAAC,OAAO,MAAM,QAAQ,GAAG,SAAS,GAAE;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAM;AAC5B,WAAO,OAAO,OAAO,OAAO;AAC1B,UAAI,QAAQ,UAAU,QAAQ,KAAK;AACrC,UAAM,YAAY;AAAA,MAChB;AAAA,MACA,QAAQ,OAAO,YAAY;AAAA,MAC3B,SAAS,OAAO,QAAQ;AAAA,IAC1B;AACA,WAAO,MAAM,QAAQ,OAAO,GAAG;AAAA,EACjC;AAAA,EACA,QAAQ,SAAS,OAAM;AACrB,QAAI,cAAc,MAAM,UAAU;AAClC,QAAI,MAAM,UAAU,QAAQ,MAAM,QAAQ,GAAG;AAC3C,qBAAe;AAAA,IACjB;AACA,WAAO;AAAA,EACT;AACF;", "names": []}