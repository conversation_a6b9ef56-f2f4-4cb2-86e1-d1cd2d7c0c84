{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/turtle.js"], "sourcesContent": ["var curPunc;\n\nfunction wordRegexp(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n}\nvar ops = wordRegexp([]);\nvar keywords = wordRegexp([\"@prefix\", \"@base\", \"a\"]);\nvar operatorChars = /[*+\\-<>=&|]/;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  curPunc = null;\n  if (ch == \"<\" && !stream.match(/^[\\s\\u00a0=]/, false)) {\n    stream.match(/^[^\\s\\u00a0>]*>?/);\n    return \"atom\";\n  }\n  else if (ch == \"\\\"\" || ch == \"'\") {\n    state.tokenize = tokenLiteral(ch);\n    return state.tokenize(stream, state);\n  }\n  else if (/[{}\\(\\),\\.;\\[\\]]/.test(ch)) {\n    curPunc = ch;\n    return null;\n  }\n  else if (ch == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  else if (operatorChars.test(ch)) {\n    stream.eatWhile(operatorChars);\n    return null;\n  }\n  else if (ch == \":\") {\n    return \"operator\";\n  } else {\n    stream.eatWhile(/[_\\w\\d]/);\n    if(stream.peek() == \":\") {\n      return \"variableName.special\";\n    } else {\n      var word = stream.current();\n\n      if(keywords.test(word)) {\n        return \"meta\";\n      }\n\n      if(ch >= \"A\" && ch <= \"Z\") {\n        return \"comment\";\n      } else {\n        return \"keyword\";\n      }\n    }\n    var word = stream.current();\n    if (ops.test(word))\n      return null;\n    else if (keywords.test(word))\n      return \"meta\";\n    else\n      return \"variable\";\n  }\n}\n\nfunction tokenLiteral(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    return \"string\";\n  };\n}\n\nfunction pushContext(state, type, col) {\n  state.context = {prev: state.context, indent: state.indent, col: col, type: type};\n}\nfunction popContext(state) {\n  state.indent = state.context.indent;\n  state.context = state.context.prev;\n}\n\nexport const turtle = {\n  name: \"turtle\",\n  startState: function() {\n    return {tokenize: tokenBase,\n            context: null,\n            indent: 0,\n            col: 0};\n  },\n\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if (state.context && state.context.align == null) state.context.align = false;\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n\n    if (style != \"comment\" && state.context && state.context.align == null && state.context.type != \"pattern\") {\n      state.context.align = true;\n    }\n\n    if (curPunc == \"(\") pushContext(state, \")\", stream.column());\n    else if (curPunc == \"[\") pushContext(state, \"]\", stream.column());\n    else if (curPunc == \"{\") pushContext(state, \"}\", stream.column());\n    else if (/[\\]\\}\\)]/.test(curPunc)) {\n      while (state.context && state.context.type == \"pattern\") popContext(state);\n      if (state.context && curPunc == state.context.type) popContext(state);\n    }\n    else if (curPunc == \".\" && state.context && state.context.type == \"pattern\") popContext(state);\n    else if (/atom|string|variable/.test(style) && state.context) {\n      if (/[\\}\\]]/.test(state.context.type))\n        pushContext(state, \"pattern\", stream.column());\n      else if (state.context.type == \"pattern\" && !state.context.align) {\n        state.context.align = true;\n        state.context.col = stream.column();\n      }\n    }\n\n    return style;\n  },\n\n  indent: function(state, textAfter, cx) {\n    var firstChar = textAfter && textAfter.charAt(0);\n    var context = state.context;\n    if (/[\\]\\}]/.test(firstChar))\n      while (context && context.type == \"pattern\") context = context.prev;\n\n    var closing = context && firstChar == context.type;\n    if (!context)\n      return 0;\n    else if (context.type == \"pattern\")\n      return context.col;\n    else if (context.align)\n      return context.col + (closing ? 0 : 1);\n    else\n      return context.indent + (closing ? 0 : cx.unit);\n  },\n\n  languageData: {\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "mappings": ";;;AAAA,IAAI;AAEJ,SAAS,WAAW,OAAO;AACzB,SAAO,IAAI,OAAO,SAAS,MAAM,KAAK,GAAG,IAAI,MAAM,GAAG;AACxD;AACA,IAAI,MAAM,WAAW,CAAC,CAAC;AACvB,IAAI,WAAW,WAAW,CAAC,WAAW,SAAS,GAAG,CAAC;AACnD,IAAI,gBAAgB;AAEpB,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AACrB,YAAU;AACV,MAAI,MAAM,OAAO,CAAC,OAAO,MAAM,gBAAgB,KAAK,GAAG;AACrD,WAAO,MAAM,kBAAkB;AAC/B,WAAO;AAAA,EACT,WACS,MAAM,OAAQ,MAAM,KAAK;AAChC,UAAM,WAAW,aAAa,EAAE;AAChC,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC,WACS,mBAAmB,KAAK,EAAE,GAAG;AACpC,cAAU;AACV,WAAO;AAAA,EACT,WACS,MAAM,KAAK;AAClB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT,WACS,cAAc,KAAK,EAAE,GAAG;AAC/B,WAAO,SAAS,aAAa;AAC7B,WAAO;AAAA,EACT,WACS,MAAM,KAAK;AAClB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS,SAAS;AACzB,QAAG,OAAO,KAAK,KAAK,KAAK;AACvB,aAAO;AAAA,IACT,OAAO;AACL,UAAI,OAAO,OAAO,QAAQ;AAE1B,UAAG,SAAS,KAAK,IAAI,GAAG;AACtB,eAAO;AAAA,MACT;AAEA,UAAG,MAAM,OAAO,MAAM,KAAK;AACzB,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,OAAO,OAAO,QAAQ;AAC1B,QAAI,IAAI,KAAK,IAAI;AACf,aAAO;AAAA,aACA,SAAS,KAAK,IAAI;AACzB,aAAO;AAAA;AAEP,aAAO;AAAA,EACX;AACF;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO;AACrB,YAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,UAAI,MAAM,SAAS,CAAC,SAAS;AAC3B,cAAM,WAAW;AACjB;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,MAAM;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,OAAO,MAAM,KAAK;AACrC,QAAM,UAAU,EAAC,MAAM,MAAM,SAAS,QAAQ,MAAM,QAAQ,KAAU,KAAU;AAClF;AACA,SAAS,WAAW,OAAO;AACzB,QAAM,SAAS,MAAM,QAAQ;AAC7B,QAAM,UAAU,MAAM,QAAQ;AAChC;AAEO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MAAC,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,KAAK;AAAA,IAAC;AAAA,EAChB;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAM,OAAM,QAAQ,QAAQ;AACxE,YAAM,SAAS,OAAO,YAAY;AAAA,IACpC;AACA,QAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AAExC,QAAI,SAAS,aAAa,MAAM,WAAW,MAAM,QAAQ,SAAS,QAAQ,MAAM,QAAQ,QAAQ,WAAW;AACzG,YAAM,QAAQ,QAAQ;AAAA,IACxB;AAEA,QAAI,WAAW,IAAK,aAAY,OAAO,KAAK,OAAO,OAAO,CAAC;AAAA,aAClD,WAAW,IAAK,aAAY,OAAO,KAAK,OAAO,OAAO,CAAC;AAAA,aACvD,WAAW,IAAK,aAAY,OAAO,KAAK,OAAO,OAAO,CAAC;AAAA,aACvD,WAAW,KAAK,OAAO,GAAG;AACjC,aAAO,MAAM,WAAW,MAAM,QAAQ,QAAQ,UAAW,YAAW,KAAK;AACzE,UAAI,MAAM,WAAW,WAAW,MAAM,QAAQ,KAAM,YAAW,KAAK;AAAA,IACtE,WACS,WAAW,OAAO,MAAM,WAAW,MAAM,QAAQ,QAAQ,UAAW,YAAW,KAAK;AAAA,aACpF,uBAAuB,KAAK,KAAK,KAAK,MAAM,SAAS;AAC5D,UAAI,SAAS,KAAK,MAAM,QAAQ,IAAI;AAClC,oBAAY,OAAO,WAAW,OAAO,OAAO,CAAC;AAAA,eACtC,MAAM,QAAQ,QAAQ,aAAa,CAAC,MAAM,QAAQ,OAAO;AAChE,cAAM,QAAQ,QAAQ;AACtB,cAAM,QAAQ,MAAM,OAAO,OAAO;AAAA,MACpC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,YAAY,aAAa,UAAU,OAAO,CAAC;AAC/C,QAAI,UAAU,MAAM;AACpB,QAAI,SAAS,KAAK,SAAS;AACzB,aAAO,WAAW,QAAQ,QAAQ,UAAW,WAAU,QAAQ;AAEjE,QAAI,UAAU,WAAW,aAAa,QAAQ;AAC9C,QAAI,CAAC;AACH,aAAO;AAAA,aACA,QAAQ,QAAQ;AACvB,aAAO,QAAQ;AAAA,aACR,QAAQ;AACf,aAAO,QAAQ,OAAO,UAAU,IAAI;AAAA;AAEpC,aAAO,QAAQ,UAAU,UAAU,IAAI,GAAG;AAAA,EAC9C;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,IAAG;AAAA,EAC3B;AACF;", "names": []}