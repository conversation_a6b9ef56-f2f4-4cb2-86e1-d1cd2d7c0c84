{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/ttcn.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nconst parserConfig = {\n  name: \"ttcn\",\n  keywords: words(\"activate address alive all alt altstep and and4b any\" +\n                  \" break case component const continue control deactivate\" +\n                  \" display do else encode enumerated except exception\" +\n                  \" execute extends extension external for from function\" +\n                  \" goto group if import in infinity inout interleave\" +\n                  \" label language length log match message mixed mod\" +\n                  \" modifies module modulepar mtc noblock not not4b nowait\" +\n                  \" of on optional or or4b out override param pattern port\" +\n                  \" procedure record recursive rem repeat return runs select\" +\n                  \" self sender set signature system template testcase to\" +\n                  \" type union value valueof var variant while with xor xor4b\"),\n  builtin: words(\"bit2hex bit2int bit2oct bit2str char2int char2oct encvalue\" +\n                 \" decomp decvalue float2int float2str hex2bit hex2int\" +\n                 \" hex2oct hex2str int2bit int2char int2float int2hex\" +\n                 \" int2oct int2str int2unichar isbound ischosen ispresent\" +\n                 \" isvalue lengthof log2str oct2bit oct2char oct2hex oct2int\" +\n                 \" oct2str regexp replace rnd sizeof str2bit str2float\" +\n                 \" str2hex str2int str2oct substr unichar2int unichar2char\" +\n                 \" enum2int\"),\n  types: words(\"anytype bitstring boolean char charstring default float\" +\n               \" hexstring integer objid octetstring universal verdicttype timer\"),\n  timerOps: words(\"read running start stop timeout\"),\n  portOps: words(\"call catch check clear getcall getreply halt raise receive\" +\n                 \" reply send trigger\"),\n  configOps: words(\"create connect disconnect done kill killed map unmap\"),\n  verdictOps: words(\"getverdict setverdict\"),\n  sutOps: words(\"action\"),\n  functionOps: words(\"apply derefers refers\"),\n\n  verdictConsts: words(\"error fail inconc none pass\"),\n  booleanConsts: words(\"true false\"),\n  otherConsts: words(\"null NULL omit\"),\n\n  visibilityModifiers: words(\"private public friend\"),\n  templateMatch: words(\"complement ifpresent subset superset permutation\"),\n  multiLineStrings: true\n}\n\nvar wordList = []\nfunction add(obj) {\n  if (obj) for (var prop in obj) if (obj.hasOwnProperty(prop))\n    wordList.push(prop);\n}\nadd(parserConfig.keywords);\nadd(parserConfig.builtin);\nadd(parserConfig.timerOps);\nadd(parserConfig.portOps);\n\nvar keywords = parserConfig.keywords || {},\n    builtin = parserConfig.builtin || {},\n    timerOps = parserConfig.timerOps || {},\n    portOps  = parserConfig.portOps || {},\n    configOps = parserConfig.configOps || {},\n    verdictOps = parserConfig.verdictOps || {},\n    sutOps = parserConfig.sutOps || {},\n    functionOps = parserConfig.functionOps || {},\n\n    verdictConsts = parserConfig.verdictConsts || {},\n    booleanConsts = parserConfig.booleanConsts || {},\n    otherConsts   = parserConfig.otherConsts || {},\n\n    types = parserConfig.types || {},\n    visibilityModifiers = parserConfig.visibilityModifiers || {},\n    templateMatch = parserConfig.templateMatch || {},\n    multiLineStrings = parserConfig.multiLineStrings,\n    indentStatements = parserConfig.indentStatements !== false;\nvar isOperatorChar = /[+\\-*&@=<>!\\/]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[\\[\\]{}\\(\\),;\\\\:\\?\\.]/.test(ch)) {\n    curPunc = ch;\n    return \"punctuation\";\n  }\n  if (ch == \"#\"){\n    stream.skipToEnd();\n    return \"atom\";\n  }\n  if (ch == \"%\"){\n    stream.eatWhile(/\\b/);\n    return \"atom\";\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    if(ch == \"@\"){\n      if(stream.match(\"try\") || stream.match(\"catch\")\n         || stream.match(\"lazy\")){\n        return \"keyword\";\n      }\n    }\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_\\xa1-\\uffff]/);\n  var cur = stream.current();\n\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  if (builtin.propertyIsEnumerable(cur)) return \"builtin\";\n\n  if (timerOps.propertyIsEnumerable(cur)) return \"def\";\n  if (configOps.propertyIsEnumerable(cur)) return \"def\";\n  if (verdictOps.propertyIsEnumerable(cur)) return \"def\";\n  if (portOps.propertyIsEnumerable(cur)) return \"def\";\n  if (sutOps.propertyIsEnumerable(cur)) return \"def\";\n  if (functionOps.propertyIsEnumerable(cur)) return \"def\";\n\n  if (verdictConsts.propertyIsEnumerable(cur)) return \"string\";\n  if (booleanConsts.propertyIsEnumerable(cur)) return \"string\";\n  if (otherConsts.propertyIsEnumerable(cur)) return \"string\";\n\n  if (types.propertyIsEnumerable(cur)) return \"typeName.standard\";\n  if (visibilityModifiers.propertyIsEnumerable(cur))\n    return \"modifier\";\n  if (templateMatch.propertyIsEnumerable(cur)) return \"atom\";\n\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped){\n        var afterQuote = stream.peek();\n        //look if the character after the quote is like the B in '10100010'B\n        if (afterQuote){\n          afterQuote = afterQuote.toLowerCase();\n          if(afterQuote == \"b\" || afterQuote == \"h\" || afterQuote == \"o\")\n            stream.next();\n        }\n        end = true; break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !(escaped || multiLineStrings))\n      state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\n\nfunction pushContext(state, col, type) {\n  var indent = state.indented;\n  if (state.context && state.context.type == \"statement\")\n    indent = state.context.indented;\n  return state.context = new Context(indent, col, type, null, state.context);\n}\n\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n//Interface\nexport const ttcn = {\n  name: \"ttcn\",\n  startState: function() {\n    return {\n      tokenize: null,\n      context: new Context(0, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\")\n        && ctx.type == \"statement\"){\n      popContext(state);\n    }\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (indentStatements &&\n             (((ctx.type == \"}\" || ctx.type == \"top\") && curPunc != ';') ||\n              (ctx.type == \"statement\" && curPunc == \"newstatement\")))\n      pushContext(state, stream.column(), \"statement\");\n\n    state.startOfLine = false;\n\n    return style;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    autocomplete: wordList\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,MAAM,KAAK;AAClB,MAAI,MAAM,CAAC,GAAGA,SAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE,EAAG,KAAIA,OAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AAEA,IAAM,eAAe;AAAA,EACnB,MAAM;AAAA,EACN,UAAU,MAAM,glBAU4D;AAAA,EAC5E,SAAS,MAAM,yYAOW;AAAA,EAC1B,OAAO,MAAM,yHACkE;AAAA,EAC/E,UAAU,MAAM,iCAAiC;AAAA,EACjD,SAAS,MAAM,+EACqB;AAAA,EACpC,WAAW,MAAM,sDAAsD;AAAA,EACvE,YAAY,MAAM,uBAAuB;AAAA,EACzC,QAAQ,MAAM,QAAQ;AAAA,EACtB,aAAa,MAAM,uBAAuB;AAAA,EAE1C,eAAe,MAAM,6BAA6B;AAAA,EAClD,eAAe,MAAM,YAAY;AAAA,EACjC,aAAa,MAAM,gBAAgB;AAAA,EAEnC,qBAAqB,MAAM,uBAAuB;AAAA,EAClD,eAAe,MAAM,kDAAkD;AAAA,EACvE,kBAAkB;AACpB;AAEA,IAAI,WAAW,CAAC;AAChB,SAAS,IAAI,KAAK;AAChB,MAAI;AAAK,aAAS,QAAQ,IAAK,KAAI,IAAI,eAAe,IAAI;AACxD,eAAS,KAAK,IAAI;AAAA;AACtB;AACA,IAAI,aAAa,QAAQ;AACzB,IAAI,aAAa,OAAO;AACxB,IAAI,aAAa,QAAQ;AACzB,IAAI,aAAa,OAAO;AAExB,IAAI,WAAW,aAAa,YAAY,CAAC;AAAzC,IACI,UAAU,aAAa,WAAW,CAAC;AADvC,IAEI,WAAW,aAAa,YAAY,CAAC;AAFzC,IAGI,UAAW,aAAa,WAAW,CAAC;AAHxC,IAII,YAAY,aAAa,aAAa,CAAC;AAJ3C,IAKI,aAAa,aAAa,cAAc,CAAC;AAL7C,IAMI,SAAS,aAAa,UAAU,CAAC;AANrC,IAOI,cAAc,aAAa,eAAe,CAAC;AAP/C,IASI,gBAAgB,aAAa,iBAAiB,CAAC;AATnD,IAUI,gBAAgB,aAAa,iBAAiB,CAAC;AAVnD,IAWI,cAAgB,aAAa,eAAe,CAAC;AAXjD,IAaI,QAAQ,aAAa,SAAS,CAAC;AAbnC,IAcI,sBAAsB,aAAa,uBAAuB,CAAC;AAd/D,IAeI,gBAAgB,aAAa,iBAAiB,CAAC;AAfnD,IAgBI,mBAAmB,aAAa;AAhBpC,IAiBI,mBAAmB,aAAa,qBAAqB;AACzD,IAAI,iBAAiB;AACrB,IAAI;AAEJ,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AAErB,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AACA,MAAI,wBAAwB,KAAK,EAAE,GAAG;AACpC,cAAU;AACV,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAI;AACZ,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAI;AACZ,WAAO,SAAS,IAAI;AACpB,WAAO;AAAA,EACT;AACA,MAAI,KAAK,KAAK,EAAE,GAAG;AACjB,WAAO,SAAS,QAAQ;AACxB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAM,WAAW;AACjB,aAAO,aAAa,QAAQ,KAAK;AAAA,IACnC;AACA,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,eAAe,KAAK,EAAE,GAAG;AAC3B,QAAG,MAAM,KAAI;AACX,UAAG,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,OAAO,KACxC,OAAO,MAAM,MAAM,GAAE;AACzB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,SAAS,cAAc;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,oBAAoB;AACpC,MAAI,MAAM,OAAO,QAAQ;AAEzB,MAAI,SAAS,qBAAqB,GAAG,EAAG,QAAO;AAC/C,MAAI,QAAQ,qBAAqB,GAAG,EAAG,QAAO;AAE9C,MAAI,SAAS,qBAAqB,GAAG,EAAG,QAAO;AAC/C,MAAI,UAAU,qBAAqB,GAAG,EAAG,QAAO;AAChD,MAAI,WAAW,qBAAqB,GAAG,EAAG,QAAO;AACjD,MAAI,QAAQ,qBAAqB,GAAG,EAAG,QAAO;AAC9C,MAAI,OAAO,qBAAqB,GAAG,EAAG,QAAO;AAC7C,MAAI,YAAY,qBAAqB,GAAG,EAAG,QAAO;AAElD,MAAI,cAAc,qBAAqB,GAAG,EAAG,QAAO;AACpD,MAAI,cAAc,qBAAqB,GAAG,EAAG,QAAO;AACpD,MAAI,YAAY,qBAAqB,GAAG,EAAG,QAAO;AAElD,MAAI,MAAM,qBAAqB,GAAG,EAAG,QAAO;AAC5C,MAAI,oBAAoB,qBAAqB,GAAG;AAC9C,WAAO;AACT,MAAI,cAAc,qBAAqB,GAAG,EAAG,QAAO;AAEpD,SAAO;AACT;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO,MAAM,MAAM;AACjC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,QAAQ,SAAS,CAAC,SAAQ;AAC5B,YAAI,aAAa,OAAO,KAAK;AAE7B,YAAI,YAAW;AACb,uBAAa,WAAW,YAAY;AACpC,cAAG,cAAc,OAAO,cAAc,OAAO,cAAc;AACzD,mBAAO,KAAK;AAAA,QAChB;AACA,cAAM;AAAM;AAAA,MACd;AACA,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,QAAI,OAAO,EAAE,WAAW;AACtB,YAAM,WAAW;AACnB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,QAAQ,UAAU,QAAQ,MAAM,OAAO,MAAM;AACpD,OAAK,WAAW;AAChB,OAAK,SAAS;AACd,OAAK,OAAO;AACZ,OAAK,QAAQ;AACb,OAAK,OAAO;AACd;AAEA,SAAS,YAAY,OAAO,KAAK,MAAM;AACrC,MAAI,SAAS,MAAM;AACnB,MAAI,MAAM,WAAW,MAAM,QAAQ,QAAQ;AACzC,aAAS,MAAM,QAAQ;AACzB,SAAO,MAAM,UAAU,IAAI,QAAQ,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO;AAC3E;AAEA,SAAS,WAAW,OAAO;AACzB,MAAI,IAAI,MAAM,QAAQ;AACtB,MAAI,KAAK,OAAO,KAAK,OAAO,KAAK;AAC/B,UAAM,WAAW,MAAM,QAAQ;AACjC,SAAO,MAAM,UAAU,MAAM,QAAQ;AACvC;AAGO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,SAAS,IAAI,QAAQ,GAAG,GAAG,OAAO,KAAK;AAAA,MACvC,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AAAA,EACF;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,MAAM,MAAM;AAChB,QAAI,OAAO,IAAI,GAAG;AAChB,UAAI,IAAI,SAAS,KAAM,KAAI,QAAQ;AACnC,YAAM,WAAW,OAAO,YAAY;AACpC,YAAM,cAAc;AAAA,IACtB;AACA,QAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,cAAU;AACV,QAAI,SAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACvD,QAAI,SAAS,UAAW,QAAO;AAC/B,QAAI,IAAI,SAAS,KAAM,KAAI,QAAQ;AAEnC,SAAK,WAAW,OAAO,WAAW,OAAO,WAAW,QAC7C,IAAI,QAAQ,aAAY;AAC7B,iBAAW,KAAK;AAAA,IAClB,WACS,WAAW,IAAK,aAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW,IAAK,aAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW,IAAK,aAAY,OAAO,OAAO,OAAO,GAAG,GAAG;AAAA,aACvD,WAAW,KAAK;AACvB,aAAO,IAAI,QAAQ,YAAa,OAAM,WAAW,KAAK;AACtD,UAAI,IAAI,QAAQ,IAAK,OAAM,WAAW,KAAK;AAC3C,aAAO,IAAI,QAAQ,YAAa,OAAM,WAAW,KAAK;AAAA,IACxD,WACS,WAAW,IAAI,KAAM,YAAW,KAAK;AAAA,aACrC,sBACG,IAAI,QAAQ,OAAO,IAAI,QAAQ,UAAU,WAAW,OACrD,IAAI,QAAQ,eAAe,WAAW;AAC/C,kBAAY,OAAO,OAAO,OAAO,GAAG,WAAW;AAEjD,UAAM,cAAc;AAEpB,WAAO;AAAA,EACT;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,IAC5D,cAAc;AAAA,EAChB;AACF;", "names": ["words"]}