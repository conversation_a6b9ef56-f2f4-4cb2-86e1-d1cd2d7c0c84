{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/oz.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nvar singleOperators = /[\\^@!\\|<>#~\\.\\*\\-\\+\\\\/,=]/;\nvar doubleOperators = /(<-)|(:=)|(=<)|(>=)|(<=)|(<:)|(>:)|(=:)|(\\\\=)|(\\\\=:)|(!!)|(==)|(::)/;\nvar tripleOperators = /(:::)|(\\.\\.\\.)|(=<:)|(>=:)/;\n\nvar middle = [\"in\", \"then\", \"else\", \"of\", \"elseof\", \"elsecase\", \"elseif\", \"catch\",\n              \"finally\", \"with\", \"require\", \"prepare\", \"import\", \"export\", \"define\", \"do\"];\nvar end = [\"end\"];\n\nvar atoms = wordRegexp([\"true\", \"false\", \"nil\", \"unit\"]);\nvar commonKeywords = wordRegexp([\"andthen\", \"at\", \"attr\", \"declare\", \"feat\", \"from\", \"lex\",\n                                 \"mod\", \"div\", \"mode\", \"orelse\", \"parser\", \"prod\", \"prop\", \"scanner\", \"self\", \"syn\", \"token\"]);\nvar openingKeywords = wordRegexp([\"local\", \"proc\", \"fun\", \"case\", \"class\", \"if\", \"cond\", \"or\", \"dis\",\n                                  \"choice\", \"not\", \"thread\", \"try\", \"raise\", \"lock\", \"for\", \"suchthat\", \"meth\", \"functor\"]);\nvar middleKeywords = wordRegexp(middle);\nvar endKeywords = wordRegexp(end);\n\n// Tokenizers\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  // Brackets\n  if(stream.match(/[{}]/)) {\n    return \"bracket\";\n  }\n\n  // Special [] keyword\n  if (stream.match('[]')) {\n    return \"keyword\"\n  }\n\n  // Operators\n  if (stream.match(tripleOperators) || stream.match(doubleOperators)) {\n    return \"operator\";\n  }\n\n  // Atoms\n  if(stream.match(atoms)) {\n    return 'atom';\n  }\n\n  // Opening keywords\n  var matched = stream.match(openingKeywords);\n  if (matched) {\n    if (!state.doInCurrentLine)\n      state.currentIndent++;\n    else\n      state.doInCurrentLine = false;\n\n    // Special matching for signatures\n    if(matched[0] == \"proc\" || matched[0] == \"fun\")\n      state.tokenize = tokenFunProc;\n    else if(matched[0] == \"class\")\n      state.tokenize = tokenClass;\n    else if(matched[0] == \"meth\")\n      state.tokenize = tokenMeth;\n\n    return 'keyword';\n  }\n\n  // Middle and other keywords\n  if (stream.match(middleKeywords) || stream.match(commonKeywords)) {\n    return \"keyword\"\n  }\n\n  // End keywords\n  if (stream.match(endKeywords)) {\n    state.currentIndent--;\n    return 'keyword';\n  }\n\n  // Eat the next char for next comparisons\n  var ch = stream.next();\n\n  // Strings\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n\n  // Numbers\n  if (/[~\\d]/.test(ch)) {\n    if (ch == \"~\") {\n      if(! /^[0-9]/.test(stream.peek()))\n        return null;\n      else if (( stream.next() == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/)) || stream.match(/^[0-9]*(\\.[0-9]+)?([eE][~+]?[0-9]+)?/))\n        return \"number\";\n    }\n\n    if ((ch == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/)) || stream.match(/^[0-9]*(\\.[0-9]+)?([eE][~+]?[0-9]+)?/))\n      return \"number\";\n\n    return null;\n  }\n\n  // Comments\n  if (ch == \"%\") {\n    stream.skipToEnd();\n    return 'comment';\n  }\n  else if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      state.tokenize = tokenComment;\n      return tokenComment(stream, state);\n    }\n  }\n\n  // Single operators\n  if(singleOperators.test(ch)) {\n    return \"operator\";\n  }\n\n  // If nothing match, we skip the entire alphanumerical block\n  stream.eatWhile(/\\w/);\n\n  return \"variable\";\n}\n\nfunction tokenClass(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n  stream.match(/([A-Z][A-Za-z0-9_]*)|(`.+`)/);\n  state.tokenize = tokenBase;\n  return \"type\"\n}\n\nfunction tokenMeth(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n  stream.match(/([a-zA-Z][A-Za-z0-9_]*)|(`.+`)/);\n  state.tokenize = tokenBase;\n  return \"def\"\n}\n\nfunction tokenFunProc(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  if(!state.hasPassedFirstStage && stream.eat(\"{\")) {\n    state.hasPassedFirstStage = true;\n    return \"bracket\";\n  }\n  else if(state.hasPassedFirstStage) {\n    stream.match(/([A-Z][A-Za-z0-9_]*)|(`.+`)|\\$/);\n    state.hasPassedFirstStage = false;\n    state.tokenize = tokenBase;\n    return \"def\"\n  }\n  else {\n    state.tokenize = tokenBase;\n    return null;\n  }\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \"/\" && maybeEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote) {\n  return function (stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped)\n      state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nfunction buildElectricInputRegEx() {\n  // Reindentation should occur on [] or on a match of any of\n  // the block closing keywords, at the end of a line.\n  var allClosings = middle.concat(end);\n  return new RegExp(\"[\\\\[\\\\]]|(\" + allClosings.join(\"|\") + \")$\");\n}\n\nexport const oz = {\n  name: \"oz\",\n\n  startState: function () {\n    return {\n      tokenize: tokenBase,\n      currentIndent: 0,\n      doInCurrentLine: false,\n      hasPassedFirstStage: false\n    };\n  },\n\n  token: function (stream, state) {\n    if (stream.sol())\n      state.doInCurrentLine = 0;\n\n    return state.tokenize(stream, state);\n  },\n\n  indent: function (state, textAfter, cx) {\n    var trueText = textAfter.replace(/^\\s+|\\s+$/g, '');\n\n    if (trueText.match(endKeywords) || trueText.match(middleKeywords) || trueText.match(/(\\[])/))\n      return cx.unit * (state.currentIndent - 1);\n\n    if (state.currentIndent < 0)\n      return 0;\n\n    return state.currentIndent * cx.unit\n  },\n\n  languageData: {\n    indentOnInut: buildElectricInputRegEx(),\n    commentTokens: {line: \"%\", block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,WAAW,OAAO;AACzB,SAAO,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,IAAI,OAAO;AACvD;AAEA,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AAEtB,IAAI,SAAS;AAAA,EAAC;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAC5D;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAI;AACzF,IAAI,MAAM,CAAC,KAAK;AAEhB,IAAI,QAAQ,WAAW,CAAC,QAAQ,SAAS,OAAO,MAAM,CAAC;AACvD,IAAI,iBAAiB,WAAW;AAAA,EAAC;AAAA,EAAW;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EACpD;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAO;AAAO,CAAC;AAC7H,IAAI,kBAAkB,WAAW;AAAA,EAAC;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAM;AAAA,EAAQ;AAAA,EAAM;AAAA,EAC7D;AAAA,EAAU;AAAA,EAAO;AAAA,EAAU;AAAA,EAAO;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAY;AAAA,EAAQ;AAAS,CAAC;AAC1H,IAAI,iBAAiB,WAAW,MAAM;AACtC,IAAI,cAAc,WAAW,GAAG;AAGhC,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAGA,MAAG,OAAO,MAAM,MAAM,GAAG;AACvB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,eAAe,KAAK,OAAO,MAAM,eAAe,GAAG;AAClE,WAAO;AAAA,EACT;AAGA,MAAG,OAAO,MAAM,KAAK,GAAG;AACtB,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,OAAO,MAAM,eAAe;AAC1C,MAAI,SAAS;AACX,QAAI,CAAC,MAAM;AACT,YAAM;AAAA;AAEN,YAAM,kBAAkB;AAG1B,QAAG,QAAQ,CAAC,KAAK,UAAU,QAAQ,CAAC,KAAK;AACvC,YAAM,WAAW;AAAA,aACX,QAAQ,CAAC,KAAK;AACpB,YAAM,WAAW;AAAA,aACX,QAAQ,CAAC,KAAK;AACpB,YAAM,WAAW;AAEnB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,cAAc,KAAK,OAAO,MAAM,cAAc,GAAG;AAChE,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,UAAM;AACN,WAAO;AAAA,EACT;AAGA,MAAI,KAAK,OAAO,KAAK;AAGrB,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAGA,MAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,QAAI,MAAM,KAAK;AACb,UAAG,CAAE,SAAS,KAAK,OAAO,KAAK,CAAC;AAC9B,eAAO;AAAA,eACE,OAAO,KAAK,KAAK,OAAO,OAAO,MAAM,mBAAmB,KAAM,OAAO,MAAM,sCAAsC;AAC1H,eAAO;AAAA,IACX;AAEA,QAAK,MAAM,OAAO,OAAO,MAAM,mBAAmB,KAAM,OAAO,MAAM,sCAAsC;AACzG,aAAO;AAET,WAAO;AAAA,EACT;AAGA,MAAI,MAAM,KAAK;AACb,WAAO,UAAU;AACjB,WAAO;AAAA,EACT,WACS,MAAM,KAAK;AAClB,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAM,WAAW;AACjB,aAAO,aAAa,QAAQ,KAAK;AAAA,IACnC;AAAA,EACF;AAGA,MAAG,gBAAgB,KAAK,EAAE,GAAG;AAC3B,WAAO;AAAA,EACT;AAGA,SAAO,SAAS,IAAI;AAEpB,SAAO;AACT;AAEA,SAAS,WAAW,QAAQ,OAAO;AACjC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,6BAA6B;AAC1C,QAAM,WAAW;AACjB,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AACA,SAAO,MAAM,gCAAgC;AAC7C,QAAM,WAAW;AACjB,SAAO;AACT;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAG,CAAC,MAAM,uBAAuB,OAAO,IAAI,GAAG,GAAG;AAChD,UAAM,sBAAsB;AAC5B,WAAO;AAAA,EACT,WACQ,MAAM,qBAAqB;AACjC,WAAO,MAAM,gCAAgC;AAC7C,UAAM,sBAAsB;AAC5B,UAAM,WAAW;AACjB,WAAO;AAAA,EACT,OACK;AACH,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,UAAU,OAAO,MAAMA,OAAM;AACjC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,QAAQ,SAAS,CAAC,SAAS;AAC7B,QAAAA,OAAM;AACN;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,QAAIA,QAAO,CAAC;AACV,YAAM,WAAW;AACnB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,0BAA0B;AAGjC,MAAI,cAAc,OAAO,OAAO,GAAG;AACnC,SAAO,IAAI,OAAO,eAAe,YAAY,KAAK,GAAG,IAAI,IAAI;AAC/D;AAEO,IAAM,KAAK;AAAA,EAChB,MAAM;AAAA,EAEN,YAAY,WAAY;AACtB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,IACvB;AAAA,EACF;AAAA,EAEA,OAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,OAAO,IAAI;AACb,YAAM,kBAAkB;AAE1B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAAA,EAEA,QAAQ,SAAU,OAAO,WAAW,IAAI;AACtC,QAAI,WAAW,UAAU,QAAQ,cAAc,EAAE;AAEjD,QAAI,SAAS,MAAM,WAAW,KAAK,SAAS,MAAM,cAAc,KAAK,SAAS,MAAM,OAAO;AACzF,aAAO,GAAG,QAAQ,MAAM,gBAAgB;AAE1C,QAAI,MAAM,gBAAgB;AACxB,aAAO;AAET,WAAO,MAAM,gBAAgB,GAAG;AAAA,EAClC;AAAA,EAEA,cAAc;AAAA,IACZ,cAAc,wBAAwB;AAAA,IACtC,eAAe,EAAC,MAAM,KAAK,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAC7D;AACF;", "names": ["end"]}