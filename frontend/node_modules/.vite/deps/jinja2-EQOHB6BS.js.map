{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/jinja2.js"], "sourcesContent": ["var keywords = [\"and\", \"as\", \"block\", \"endblock\", \"by\", \"cycle\", \"debug\", \"else\", \"elif\",\n                \"extends\", \"filter\", \"endfilter\", \"firstof\", \"do\", \"for\",\n                \"endfor\", \"if\", \"endif\", \"ifchanged\", \"endifchanged\",\n                \"ifequal\", \"endifequal\", \"ifnotequal\", \"set\", \"raw\", \"endraw\",\n                \"endifnotequal\", \"in\", \"include\", \"load\", \"not\", \"now\", \"or\",\n                \"parsed\", \"regroup\", \"reversed\", \"spaceless\", \"call\", \"endcall\", \"macro\",\n                \"endmacro\", \"endspaceless\", \"ssi\", \"templatetag\", \"openblock\",\n                \"closeblock\", \"openvariable\", \"closevariable\", \"without\", \"context\",\n                \"openbrace\", \"closebrace\", \"opencomment\",\n                \"closecomment\", \"widthratio\", \"url\", \"with\", \"endwith\",\n                \"get_current_language\", \"trans\", \"endtrans\", \"noop\", \"blocktrans\",\n                \"endblocktrans\", \"get_available_languages\",\n                \"get_current_language_bidi\", \"pluralize\", \"autoescape\", \"endautoescape\"],\n    operator = /^[+\\-*&%=<>!?|~^]/,\n    sign = /^[:\\[\\(\\{]/,\n    atom = [\"true\", \"false\"],\n    number = /^(\\d[+\\-\\*\\/])?\\d+(\\.\\d+)?/;\n\nkeywords = new RegExp(\"((\" + keywords.join(\")|(\") + \"))\\\\b\");\natom = new RegExp(\"((\" + atom.join(\")|(\") + \"))\\\\b\");\n\nfunction tokenBase (stream, state) {\n  var ch = stream.peek();\n\n  //Comment\n  if (state.incomment) {\n    if(!stream.skipTo(\"#}\")) {\n      stream.skipToEnd();\n    } else {\n      stream.eatWhile(/\\#|}/);\n      state.incomment = false;\n    }\n    return \"comment\";\n    //Tag\n  } else if (state.intag) {\n    //After operator\n    if(state.operator) {\n      state.operator = false;\n      if(stream.match(atom)) {\n        return \"atom\";\n      }\n      if(stream.match(number)) {\n        return \"number\";\n      }\n    }\n    //After sign\n    if(state.sign) {\n      state.sign = false;\n      if(stream.match(atom)) {\n        return \"atom\";\n      }\n      if(stream.match(number)) {\n        return \"number\";\n      }\n    }\n\n    if(state.instring) {\n      if(ch == state.instring) {\n        state.instring = false;\n      }\n      stream.next();\n      return \"string\";\n    } else if(ch == \"'\" || ch == '\"') {\n      state.instring = ch;\n      stream.next();\n      return \"string\";\n    } else if (state.inbraces > 0 && ch ==\")\") {\n      stream.next()\n      state.inbraces--;\n    }\n    else if (ch == \"(\") {\n      stream.next()\n      state.inbraces++;\n    }\n    else if (state.inbrackets > 0 && ch ==\"]\") {\n      stream.next()\n      state.inbrackets--;\n    }\n    else if (ch == \"[\") {\n      stream.next()\n      state.inbrackets++;\n    } else if (!state.lineTag && (stream.match(state.intag + \"}\") || stream.eat(\"-\") && stream.match(state.intag + \"}\"))) {\n      state.intag = false;\n      return \"tag\";\n    } else if(stream.match(operator)) {\n      state.operator = true;\n      return \"operator\";\n    } else if(stream.match(sign)) {\n      state.sign = true;\n    } else {\n      if (stream.column() == 1 && state.lineTag && stream.match(keywords)) {\n        //allow nospace after tag before the keyword\n        return \"keyword\";\n      }\n      if(stream.eat(\" \") || stream.sol()) {\n        if(stream.match(keywords)) {\n          return \"keyword\";\n        }\n        if(stream.match(atom)) {\n          return \"atom\";\n        }\n        if(stream.match(number)) {\n          return \"number\";\n        }\n        if(stream.sol()) {\n          stream.next();\n        }\n      } else {\n        stream.next();\n      }\n\n    }\n    return \"variable\";\n  } else if (stream.eat(\"{\")) {\n    if (stream.eat(\"#\")) {\n      state.incomment = true;\n      if(!stream.skipTo(\"#}\")) {\n        stream.skipToEnd();\n      } else {\n        stream.eatWhile(/\\#|}/);\n        state.incomment = false;\n      }\n      return \"comment\";\n      //Open tag\n    } else if (ch = stream.eat(/\\{|%/)) {\n      //Cache close tag\n      state.intag = ch;\n      state.inbraces = 0;\n      state.inbrackets = 0;\n      if(ch == \"{\") {\n        state.intag = \"}\";\n      }\n      stream.eat(\"-\");\n      return \"tag\";\n    }\n    //Line statements\n  } else if (stream.eat('#')) {\n    if (stream.peek() == '#') {\n      stream.skipToEnd();\n      return \"comment\"\n    }\n    else if (!stream.eol()) {\n      state.intag = true;\n      state.lineTag = true;\n      state.inbraces = 0;\n      state.inbrackets = 0;\n      return \"tag\";\n    }\n  }\n  stream.next();\n};\n\nexport const jinja2 = {\n  name: \"jinja2\",\n  startState: function () {\n    return {tokenize: tokenBase, inbrackets: 0, inbraces: 0};\n  },\n  token: function(stream, state) {\n    var style = state.tokenize(stream, state);\n    if (stream.eol() && state.lineTag && !state.instring && state.inbraces == 0 && state.inbrackets == 0) {\n      //Close line statement at the EOL\n      state.intag = false\n      state.lineTag = false\n    }\n    return style;\n  },\n  languageData: {\n    commentTokens: {block: {open: \"{#\", close: \"#}\", line: \"##\"}}\n  }\n};\n"], "mappings": ";;;AAAA,IAAI,WAAW;AAAA,EAAC;AAAA,EAAO;AAAA,EAAM;AAAA,EAAS;AAAA,EAAY;AAAA,EAAM;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAClE;AAAA,EAAW;AAAA,EAAU;AAAA,EAAa;AAAA,EAAW;AAAA,EAAM;AAAA,EACnD;AAAA,EAAU;AAAA,EAAM;AAAA,EAAS;AAAA,EAAa;AAAA,EACtC;AAAA,EAAW;AAAA,EAAc;AAAA,EAAc;AAAA,EAAO;AAAA,EAAO;AAAA,EACrD;AAAA,EAAiB;AAAA,EAAM;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EACxD;AAAA,EAAU;AAAA,EAAW;AAAA,EAAY;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAW;AAAA,EACjE;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAO;AAAA,EAAe;AAAA,EAClD;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAiB;AAAA,EAAW;AAAA,EAC1D;AAAA,EAAa;AAAA,EAAc;AAAA,EAC3B;AAAA,EAAgB;AAAA,EAAc;AAAA,EAAO;AAAA,EAAQ;AAAA,EAC7C;AAAA,EAAwB;AAAA,EAAS;AAAA,EAAY;AAAA,EAAQ;AAAA,EACrD;AAAA,EAAiB;AAAA,EACjB;AAAA,EAA6B;AAAA,EAAa;AAAA,EAAc;AAAe;AAZvF,IAaI,WAAW;AAbf,IAcI,OAAO;AAdX,IAeI,OAAO,CAAC,QAAQ,OAAO;AAf3B,IAgBI,SAAS;AAEb,WAAW,IAAI,OAAO,OAAO,SAAS,KAAK,KAAK,IAAI,OAAO;AAC3D,OAAO,IAAI,OAAO,OAAO,KAAK,KAAK,KAAK,IAAI,OAAO;AAEnD,SAAS,UAAW,QAAQ,OAAO;AACjC,MAAI,KAAK,OAAO,KAAK;AAGrB,MAAI,MAAM,WAAW;AACnB,QAAG,CAAC,OAAO,OAAO,IAAI,GAAG;AACvB,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,SAAS,MAAM;AACtB,YAAM,YAAY;AAAA,IACpB;AACA,WAAO;AAAA,EAET,WAAW,MAAM,OAAO;AAEtB,QAAG,MAAM,UAAU;AACjB,YAAM,WAAW;AACjB,UAAG,OAAO,MAAM,IAAI,GAAG;AACrB,eAAO;AAAA,MACT;AACA,UAAG,OAAO,MAAM,MAAM,GAAG;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAG,MAAM,MAAM;AACb,YAAM,OAAO;AACb,UAAG,OAAO,MAAM,IAAI,GAAG;AACrB,eAAO;AAAA,MACT;AACA,UAAG,OAAO,MAAM,MAAM,GAAG;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAG,MAAM,UAAU;AACjB,UAAG,MAAM,MAAM,UAAU;AACvB,cAAM,WAAW;AAAA,MACnB;AACA,aAAO,KAAK;AACZ,aAAO;AAAA,IACT,WAAU,MAAM,OAAO,MAAM,KAAK;AAChC,YAAM,WAAW;AACjB,aAAO,KAAK;AACZ,aAAO;AAAA,IACT,WAAW,MAAM,WAAW,KAAK,MAAK,KAAK;AACzC,aAAO,KAAK;AACZ,YAAM;AAAA,IACR,WACS,MAAM,KAAK;AAClB,aAAO,KAAK;AACZ,YAAM;AAAA,IACR,WACS,MAAM,aAAa,KAAK,MAAK,KAAK;AACzC,aAAO,KAAK;AACZ,YAAM;AAAA,IACR,WACS,MAAM,KAAK;AAClB,aAAO,KAAK;AACZ,YAAM;AAAA,IACR,WAAW,CAAC,MAAM,YAAY,OAAO,MAAM,MAAM,QAAQ,GAAG,KAAK,OAAO,IAAI,GAAG,KAAK,OAAO,MAAM,MAAM,QAAQ,GAAG,IAAI;AACpH,YAAM,QAAQ;AACd,aAAO;AAAA,IACT,WAAU,OAAO,MAAM,QAAQ,GAAG;AAChC,YAAM,WAAW;AACjB,aAAO;AAAA,IACT,WAAU,OAAO,MAAM,IAAI,GAAG;AAC5B,YAAM,OAAO;AAAA,IACf,OAAO;AACL,UAAI,OAAO,OAAO,KAAK,KAAK,MAAM,WAAW,OAAO,MAAM,QAAQ,GAAG;AAEnE,eAAO;AAAA,MACT;AACA,UAAG,OAAO,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG;AAClC,YAAG,OAAO,MAAM,QAAQ,GAAG;AACzB,iBAAO;AAAA,QACT;AACA,YAAG,OAAO,MAAM,IAAI,GAAG;AACrB,iBAAO;AAAA,QACT;AACA,YAAG,OAAO,MAAM,MAAM,GAAG;AACvB,iBAAO;AAAA,QACT;AACA,YAAG,OAAO,IAAI,GAAG;AACf,iBAAO,KAAK;AAAA,QACd;AAAA,MACF,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IAEF;AACA,WAAO;AAAA,EACT,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAM,YAAY;AAClB,UAAG,CAAC,OAAO,OAAO,IAAI,GAAG;AACvB,eAAO,UAAU;AAAA,MACnB,OAAO;AACL,eAAO,SAAS,MAAM;AACtB,cAAM,YAAY;AAAA,MACpB;AACA,aAAO;AAAA,IAET,WAAW,KAAK,OAAO,IAAI,MAAM,GAAG;AAElC,YAAM,QAAQ;AACd,YAAM,WAAW;AACjB,YAAM,aAAa;AACnB,UAAG,MAAM,KAAK;AACZ,cAAM,QAAQ;AAAA,MAChB;AACA,aAAO,IAAI,GAAG;AACd,aAAO;AAAA,IACT;AAAA,EAEF,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,QAAI,OAAO,KAAK,KAAK,KAAK;AACxB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT,WACS,CAAC,OAAO,IAAI,GAAG;AACtB,YAAM,QAAQ;AACd,YAAM,UAAU;AAChB,YAAM,WAAW;AACjB,YAAM,aAAa;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,KAAK;AACd;AAEO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,YAAY,WAAY;AACtB,WAAO,EAAC,UAAU,WAAW,YAAY,GAAG,UAAU,EAAC;AAAA,EACzD;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,QAAI,OAAO,IAAI,KAAK,MAAM,WAAW,CAAC,MAAM,YAAY,MAAM,YAAY,KAAK,MAAM,cAAc,GAAG;AAEpG,YAAM,QAAQ;AACd,YAAM,UAAU;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,eAAe,EAAC,OAAO,EAAC,MAAM,MAAM,OAAO,MAAM,MAAM,KAAI,EAAC;AAAA,EAC9D;AACF;", "names": []}