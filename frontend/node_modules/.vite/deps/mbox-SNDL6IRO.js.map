{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/mbox.js"], "sourcesContent": ["var rfc2822 = [\n  \"From\", \"Sender\", \"Reply-To\", \"To\", \"Cc\", \"Bcc\", \"Message-ID\",\n  \"In-Reply-To\", \"References\", \"Resent-From\", \"Resent-Sender\", \"Resent-To\",\n  \"Resent-Cc\", \"Resent-Bcc\", \"Resent-Message-ID\", \"Return-Path\", \"Received\"\n];\nvar rfc2822NoEmail = [\n  \"Date\", \"Subject\", \"Comments\", \"Keywords\", \"Resent-Date\"\n];\n\nvar whitespace = /^[ \\t]/;\nvar separator = /^From /; // See RFC 4155\nvar rfc2822Header = new RegExp(\"^(\" + rfc2822.join(\"|\") + \"): \");\nvar rfc2822HeaderNoEmail = new RegExp(\"^(\" + rfc2822NoEmail.join(\"|\") + \"): \");\nvar header = /^[^:]+:/; // Optional fields defined in RFC 2822\nvar email = /^[^ ]+@[^ ]+/;\nvar untilEmail = /^.*?(?=[^ ]+?@[^ ]+)/;\nvar bracketedEmail = /^<.*?>/;\nvar untilBracketedEmail = /^.*?(?=<.*>)/;\n\nfunction styleForHeader(header) {\n  if (header === \"Subject\") return \"header\";\n  return \"string\";\n}\n\nfunction readToken(stream, state) {\n  if (stream.sol()) {\n    // From last line\n    state.inSeparator = false;\n    if (state.inHeader && stream.match(whitespace)) {\n      // Header folding\n      return null;\n    } else {\n      state.inHeader = false;\n      state.header = null;\n    }\n\n    if (stream.match(separator)) {\n      state.inHeaders = true;\n      state.inSeparator = true;\n      return \"atom\";\n    }\n\n    var match;\n    var emailPermitted = false;\n    if ((match = stream.match(rfc2822HeaderNoEmail)) ||\n        (emailPermitted = true) && (match = stream.match(rfc2822Header))) {\n      state.inHeaders = true;\n      state.inHeader = true;\n      state.emailPermitted = emailPermitted;\n      state.header = match[1];\n      return \"atom\";\n    }\n\n    // Use vim's heuristics: recognize custom headers only if the line is in a\n    // block of legitimate headers.\n    if (state.inHeaders && (match = stream.match(header))) {\n      state.inHeader = true;\n      state.emailPermitted = true;\n      state.header = match[1];\n      return \"atom\";\n    }\n\n    state.inHeaders = false;\n    stream.skipToEnd();\n    return null;\n  }\n\n  if (state.inSeparator) {\n    if (stream.match(email)) return \"link\";\n    if (stream.match(untilEmail)) return \"atom\";\n    stream.skipToEnd();\n    return \"atom\";\n  }\n\n  if (state.inHeader) {\n    var style = styleForHeader(state.header);\n\n    if (state.emailPermitted) {\n      if (stream.match(bracketedEmail)) return style + \" link\";\n      if (stream.match(untilBracketedEmail)) return style;\n    }\n    stream.skipToEnd();\n    return style;\n  }\n\n  stream.skipToEnd();\n  return null;\n};\n\nexport const mbox = {\n  name: \"mbox\",\n  startState: function() {\n    return {\n      // Is in a mbox separator\n      inSeparator: false,\n      // Is in a mail header\n      inHeader: false,\n      // If bracketed email is permitted. Only applicable when inHeader\n      emailPermitted: false,\n      // Name of current header\n      header: null,\n      // Is in a region of mail headers\n      inHeaders: false\n    };\n  },\n  token: readToken,\n  blankLine: function(state) {\n    state.inHeaders = state.inSeparator = state.inHeader = false;\n  },\n  languageData: {\n    autocomplete: rfc2822.concat(rfc2822NoEmail)\n  }\n}\n\n"], "mappings": ";;;AAAA,IAAI,UAAU;AAAA,EACZ;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAY;AAAA,EAAM;AAAA,EAAM;AAAA,EAAO;AAAA,EACjD;AAAA,EAAe;AAAA,EAAc;AAAA,EAAe;AAAA,EAAiB;AAAA,EAC7D;AAAA,EAAa;AAAA,EAAc;AAAA,EAAqB;AAAA,EAAe;AACjE;AACA,IAAI,iBAAiB;AAAA,EACnB;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAY;AAAA,EAAY;AAC7C;AAEA,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,gBAAgB,IAAI,OAAO,OAAO,QAAQ,KAAK,GAAG,IAAI,KAAK;AAC/D,IAAI,uBAAuB,IAAI,OAAO,OAAO,eAAe,KAAK,GAAG,IAAI,KAAK;AAC7E,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,IAAI,sBAAsB;AAE1B,SAAS,eAAeA,SAAQ;AAC9B,MAAIA,YAAW,UAAW,QAAO;AACjC,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,OAAO,IAAI,GAAG;AAEhB,UAAM,cAAc;AACpB,QAAI,MAAM,YAAY,OAAO,MAAM,UAAU,GAAG;AAE9C,aAAO;AAAA,IACT,OAAO;AACL,YAAM,WAAW;AACjB,YAAM,SAAS;AAAA,IACjB;AAEA,QAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,YAAM,YAAY;AAClB,YAAM,cAAc;AACpB,aAAO;AAAA,IACT;AAEA,QAAI;AACJ,QAAI,iBAAiB;AACrB,SAAK,QAAQ,OAAO,MAAM,oBAAoB,OACzC,iBAAiB,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI;AACpE,YAAM,YAAY;AAClB,YAAM,WAAW;AACjB,YAAM,iBAAiB;AACvB,YAAM,SAAS,MAAM,CAAC;AACtB,aAAO;AAAA,IACT;AAIA,QAAI,MAAM,cAAc,QAAQ,OAAO,MAAM,MAAM,IAAI;AACrD,YAAM,WAAW;AACjB,YAAM,iBAAiB;AACvB,YAAM,SAAS,MAAM,CAAC;AACtB,aAAO;AAAA,IACT;AAEA,UAAM,YAAY;AAClB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,aAAa;AACrB,QAAI,OAAO,MAAM,KAAK,EAAG,QAAO;AAChC,QAAI,OAAO,MAAM,UAAU,EAAG,QAAO;AACrC,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,UAAU;AAClB,QAAI,QAAQ,eAAe,MAAM,MAAM;AAEvC,QAAI,MAAM,gBAAgB;AACxB,UAAI,OAAO,MAAM,cAAc,EAAG,QAAO,QAAQ;AACjD,UAAI,OAAO,MAAM,mBAAmB,EAAG,QAAO;AAAA,IAChD;AACA,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,SAAO,UAAU;AACjB,SAAO;AACT;AAEO,IAAM,OAAO;AAAA,EAClB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA;AAAA,MAEL,aAAa;AAAA;AAAA,MAEb,UAAU;AAAA;AAAA,MAEV,gBAAgB;AAAA;AAAA,MAEhB,QAAQ;AAAA;AAAA,MAER,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EACA,OAAO;AAAA,EACP,WAAW,SAAS,OAAO;AACzB,UAAM,YAAY,MAAM,cAAc,MAAM,WAAW;AAAA,EACzD;AAAA,EACA,cAAc;AAAA,IACZ,cAAc,QAAQ,OAAO,cAAc;AAAA,EAC7C;AACF;", "names": ["header"]}