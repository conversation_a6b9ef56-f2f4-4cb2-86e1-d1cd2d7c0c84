{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/stylus.js"], "sourcesContent": ["// developer.mozilla.org/en-US/docs/Web/HTML/Element\nvar tagKeywords_ = [\"a\",\"abbr\",\"address\",\"area\",\"article\",\"aside\",\"audio\", \"b\", \"base\",\"bdi\", \"bdo\",\"bgsound\",\"blockquote\",\"body\",\"br\",\"button\",\"canvas\",\"caption\",\"cite\", \"code\",\"col\",\"colgroup\",\"data\",\"datalist\",\"dd\",\"del\",\"details\",\"dfn\",\"div\", \"dl\",\"dt\",\"em\",\"embed\",\"fieldset\",\"figcaption\",\"figure\",\"footer\",\"form\",\"h1\", \"h2\",\"h3\",\"h4\",\"h5\",\"h6\",\"head\",\"header\",\"hgroup\",\"hr\",\"html\",\"i\",\"iframe\", \"img\",\"input\",\"ins\",\"kbd\",\"keygen\",\"label\",\"legend\",\"li\",\"link\",\"main\",\"map\", \"mark\",\"marquee\",\"menu\",\"menuitem\",\"meta\",\"meter\",\"nav\",\"nobr\",\"noframes\", \"noscript\",\"object\",\"ol\",\"optgroup\",\"option\",\"output\",\"p\",\"param\",\"pre\", \"progress\",\"q\",\"rp\",\"rt\",\"ruby\",\"s\",\"samp\",\"script\",\"section\",\"select\", \"small\",\"source\",\"span\",\"strong\",\"style\",\"sub\",\"summary\",\"sup\",\"table\",\"tbody\",\"td\",\"textarea\",\"tfoot\",\"th\",\"thead\",\"time\",\"tr\",\"track\", \"u\",\"ul\",\"var\",\"video\"];\n\n// github.com/codemirror/CodeMirror/blob/master/mode/css/css.js\n// Note, \"url-prefix\" should precede \"url\" in order to match correctly in documentTypesRegexp\nvar documentTypes_ = [\"domain\", \"regexp\", \"url-prefix\", \"url\"];\nvar mediaTypes_ = [\"all\",\"aural\",\"braille\",\"handheld\",\"print\",\"projection\",\"screen\",\"tty\",\"tv\",\"embossed\"];\nvar mediaFeatures_ = [\"width\",\"min-width\",\"max-width\",\"height\",\"min-height\",\"max-height\",\"device-width\",\"min-device-width\",\"max-device-width\",\"device-height\",\"min-device-height\",\"max-device-height\",\"aspect-ratio\",\"min-aspect-ratio\",\"max-aspect-ratio\",\"device-aspect-ratio\",\"min-device-aspect-ratio\",\"max-device-aspect-ratio\",\"color\",\"min-color\",\"max-color\",\"color-index\",\"min-color-index\",\"max-color-index\",\"monochrome\",\"min-monochrome\",\"max-monochrome\",\"resolution\",\"min-resolution\",\"max-resolution\",\"scan\",\"grid\",\"dynamic-range\",\"video-dynamic-range\"];\nvar propertyKeywords_ = [\"align-content\",\"align-items\",\"align-self\",\"alignment-adjust\",\"alignment-baseline\",\"anchor-point\",\"animation\",\"animation-delay\",\"animation-direction\",\"animation-duration\",\"animation-fill-mode\",\"animation-iteration-count\",\"animation-name\",\"animation-play-state\",\"animation-timing-function\",\"appearance\",\"azimuth\",\"backface-visibility\",\"background\",\"background-attachment\",\"background-clip\",\"background-color\",\"background-image\",\"background-origin\",\"background-position\",\"background-repeat\",\"background-size\",\"baseline-shift\",\"binding\",\"bleed\",\"bookmark-label\",\"bookmark-level\",\"bookmark-state\",\"bookmark-target\",\"border\",\"border-bottom\",\"border-bottom-color\",\"border-bottom-left-radius\",\"border-bottom-right-radius\",\"border-bottom-style\",\"border-bottom-width\",\"border-collapse\",\"border-color\",\"border-image\",\"border-image-outset\",\"border-image-repeat\",\"border-image-slice\",\"border-image-source\",\"border-image-width\",\"border-left\",\"border-left-color\",\"border-left-style\",\"border-left-width\",\"border-radius\",\"border-right\",\"border-right-color\",\"border-right-style\",\"border-right-width\",\"border-spacing\",\"border-style\",\"border-top\",\"border-top-color\",\"border-top-left-radius\",\"border-top-right-radius\",\"border-top-style\",\"border-top-width\",\"border-width\",\"bottom\",\"box-decoration-break\",\"box-shadow\",\"box-sizing\",\"break-after\",\"break-before\",\"break-inside\",\"caption-side\",\"clear\",\"clip\",\"color\",\"color-profile\",\"column-count\",\"column-fill\",\"column-gap\",\"column-rule\",\"column-rule-color\",\"column-rule-style\",\"column-rule-width\",\"column-span\",\"column-width\",\"columns\",\"content\",\"counter-increment\",\"counter-reset\",\"crop\",\"cue\",\"cue-after\",\"cue-before\",\"cursor\",\"direction\",\"display\",\"dominant-baseline\",\"drop-initial-after-adjust\",\"drop-initial-after-align\",\"drop-initial-before-adjust\",\"drop-initial-before-align\",\"drop-initial-size\",\"drop-initial-value\",\"elevation\",\"empty-cells\",\"fit\",\"fit-position\",\"flex\",\"flex-basis\",\"flex-direction\",\"flex-flow\",\"flex-grow\",\"flex-shrink\",\"flex-wrap\",\"float\",\"float-offset\",\"flow-from\",\"flow-into\",\"font\",\"font-feature-settings\",\"font-family\",\"font-kerning\",\"font-language-override\",\"font-size\",\"font-size-adjust\",\"font-stretch\",\"font-style\",\"font-synthesis\",\"font-variant\",\"font-variant-alternates\",\"font-variant-caps\",\"font-variant-east-asian\",\"font-variant-ligatures\",\"font-variant-numeric\",\"font-variant-position\",\"font-weight\",\"grid\",\"grid-area\",\"grid-auto-columns\",\"grid-auto-flow\",\"grid-auto-position\",\"grid-auto-rows\",\"grid-column\",\"grid-column-end\",\"grid-column-start\",\"grid-row\",\"grid-row-end\",\"grid-row-start\",\"grid-template\",\"grid-template-areas\",\"grid-template-columns\",\"grid-template-rows\",\"hanging-punctuation\",\"height\",\"hyphens\",\"icon\",\"image-orientation\",\"image-rendering\",\"image-resolution\",\"inline-box-align\",\"justify-content\",\"left\",\"letter-spacing\",\"line-break\",\"line-height\",\"line-stacking\",\"line-stacking-ruby\",\"line-stacking-shift\",\"line-stacking-strategy\",\"list-style\",\"list-style-image\",\"list-style-position\",\"list-style-type\",\"margin\",\"margin-bottom\",\"margin-left\",\"margin-right\",\"margin-top\",\"marker-offset\",\"marks\",\"marquee-direction\",\"marquee-loop\",\"marquee-play-count\",\"marquee-speed\",\"marquee-style\",\"max-height\",\"max-width\",\"min-height\",\"min-width\",\"move-to\",\"nav-down\",\"nav-index\",\"nav-left\",\"nav-right\",\"nav-up\",\"object-fit\",\"object-position\",\"opacity\",\"order\",\"orphans\",\"outline\",\"outline-color\",\"outline-offset\",\"outline-style\",\"outline-width\",\"overflow\",\"overflow-style\",\"overflow-wrap\",\"overflow-x\",\"overflow-y\",\"padding\",\"padding-bottom\",\"padding-left\",\"padding-right\",\"padding-top\",\"page\",\"page-break-after\",\"page-break-before\",\"page-break-inside\",\"page-policy\",\"pause\",\"pause-after\",\"pause-before\",\"perspective\",\"perspective-origin\",\"pitch\",\"pitch-range\",\"play-during\",\"position\",\"presentation-level\",\"punctuation-trim\",\"quotes\",\"region-break-after\",\"region-break-before\",\"region-break-inside\",\"region-fragment\",\"rendering-intent\",\"resize\",\"rest\",\"rest-after\",\"rest-before\",\"richness\",\"right\",\"rotation\",\"rotation-point\",\"ruby-align\",\"ruby-overhang\",\"ruby-position\",\"ruby-span\",\"shape-image-threshold\",\"shape-inside\",\"shape-margin\",\"shape-outside\",\"size\",\"speak\",\"speak-as\",\"speak-header\",\"speak-numeral\",\"speak-punctuation\",\"speech-rate\",\"stress\",\"string-set\",\"tab-size\",\"table-layout\",\"target\",\"target-name\",\"target-new\",\"target-position\",\"text-align\",\"text-align-last\",\"text-decoration\",\"text-decoration-color\",\"text-decoration-line\",\"text-decoration-skip\",\"text-decoration-style\",\"text-emphasis\",\"text-emphasis-color\",\"text-emphasis-position\",\"text-emphasis-style\",\"text-height\",\"text-indent\",\"text-justify\",\"text-outline\",\"text-overflow\",\"text-shadow\",\"text-size-adjust\",\"text-space-collapse\",\"text-transform\",\"text-underline-position\",\"text-wrap\",\"top\",\"transform\",\"transform-origin\",\"transform-style\",\"transition\",\"transition-delay\",\"transition-duration\",\"transition-property\",\"transition-timing-function\",\"unicode-bidi\",\"vertical-align\",\"visibility\",\"voice-balance\",\"voice-duration\",\"voice-family\",\"voice-pitch\",\"voice-range\",\"voice-rate\",\"voice-stress\",\"voice-volume\",\"volume\",\"white-space\",\"widows\",\"width\",\"will-change\",\"word-break\",\"word-spacing\",\"word-wrap\",\"z-index\",\"clip-path\",\"clip-rule\",\"mask\",\"enable-background\",\"filter\",\"flood-color\",\"flood-opacity\",\"lighting-color\",\"stop-color\",\"stop-opacity\",\"pointer-events\",\"color-interpolation\",\"color-interpolation-filters\",\"color-rendering\",\"fill\",\"fill-opacity\",\"fill-rule\",\"image-rendering\",\"marker\",\"marker-end\",\"marker-mid\",\"marker-start\",\"shape-rendering\",\"stroke\",\"stroke-dasharray\",\"stroke-dashoffset\",\"stroke-linecap\",\"stroke-linejoin\",\"stroke-miterlimit\",\"stroke-opacity\",\"stroke-width\",\"text-rendering\",\"baseline-shift\",\"dominant-baseline\",\"glyph-orientation-horizontal\",\"glyph-orientation-vertical\",\"text-anchor\",\"writing-mode\",\"font-smoothing\",\"osx-font-smoothing\"];\nvar nonStandardPropertyKeywords_ = [\"scrollbar-arrow-color\",\"scrollbar-base-color\",\"scrollbar-dark-shadow-color\",\"scrollbar-face-color\",\"scrollbar-highlight-color\",\"scrollbar-shadow-color\",\"scrollbar-3d-light-color\",\"scrollbar-track-color\",\"shape-inside\",\"searchfield-cancel-button\",\"searchfield-decoration\",\"searchfield-results-button\",\"searchfield-results-decoration\",\"zoom\"];\nvar fontProperties_ = [\"font-family\",\"src\",\"unicode-range\",\"font-variant\",\"font-feature-settings\",\"font-stretch\",\"font-weight\",\"font-style\"];\nvar colorKeywords_ = [\"aliceblue\",\"antiquewhite\",\"aqua\",\"aquamarine\",\"azure\",\"beige\",\"bisque\",\"black\",\"blanchedalmond\",\"blue\",\"blueviolet\",\"brown\",\"burlywood\",\"cadetblue\",\"chartreuse\",\"chocolate\",\"coral\",\"cornflowerblue\",\"cornsilk\",\"crimson\",\"cyan\",\"darkblue\",\"darkcyan\",\"darkgoldenrod\",\"darkgray\",\"darkgreen\",\"darkkhaki\",\"darkmagenta\",\"darkolivegreen\",\"darkorange\",\"darkorchid\",\"darkred\",\"darksalmon\",\"darkseagreen\",\"darkslateblue\",\"darkslategray\",\"darkturquoise\",\"darkviolet\",\"deeppink\",\"deepskyblue\",\"dimgray\",\"dodgerblue\",\"firebrick\",\"floralwhite\",\"forestgreen\",\"fuchsia\",\"gainsboro\",\"ghostwhite\",\"gold\",\"goldenrod\",\"gray\",\"grey\",\"green\",\"greenyellow\",\"honeydew\",\"hotpink\",\"indianred\",\"indigo\",\"ivory\",\"khaki\",\"lavender\",\"lavenderblush\",\"lawngreen\",\"lemonchiffon\",\"lightblue\",\"lightcoral\",\"lightcyan\",\"lightgoldenrodyellow\",\"lightgray\",\"lightgreen\",\"lightpink\",\"lightsalmon\",\"lightseagreen\",\"lightskyblue\",\"lightslategray\",\"lightsteelblue\",\"lightyellow\",\"lime\",\"limegreen\",\"linen\",\"magenta\",\"maroon\",\"mediumaquamarine\",\"mediumblue\",\"mediumorchid\",\"mediumpurple\",\"mediumseagreen\",\"mediumslateblue\",\"mediumspringgreen\",\"mediumturquoise\",\"mediumvioletred\",\"midnightblue\",\"mintcream\",\"mistyrose\",\"moccasin\",\"navajowhite\",\"navy\",\"oldlace\",\"olive\",\"olivedrab\",\"orange\",\"orangered\",\"orchid\",\"palegoldenrod\",\"palegreen\",\"paleturquoise\",\"palevioletred\",\"papayawhip\",\"peachpuff\",\"peru\",\"pink\",\"plum\",\"powderblue\",\"purple\",\"rebeccapurple\",\"red\",\"rosybrown\",\"royalblue\",\"saddlebrown\",\"salmon\",\"sandybrown\",\"seagreen\",\"seashell\",\"sienna\",\"silver\",\"skyblue\",\"slateblue\",\"slategray\",\"snow\",\"springgreen\",\"steelblue\",\"tan\",\"teal\",\"thistle\",\"tomato\",\"turquoise\",\"violet\",\"wheat\",\"white\",\"whitesmoke\",\"yellow\",\"yellowgreen\"];\nvar valueKeywords_ = [\"above\",\"absolute\",\"activeborder\",\"additive\",\"activecaption\",\"afar\",\"after-white-space\",\"ahead\",\"alias\",\"all\",\"all-scroll\",\"alphabetic\",\"alternate\",\"always\",\"amharic\",\"amharic-abegede\",\"antialiased\",\"appworkspace\",\"arabic-indic\",\"armenian\",\"asterisks\",\"attr\",\"auto\",\"avoid\",\"avoid-column\",\"avoid-page\",\"avoid-region\",\"background\",\"backwards\",\"baseline\",\"below\",\"bidi-override\",\"binary\",\"bengali\",\"blink\",\"block\",\"block-axis\",\"bold\",\"bolder\",\"border\",\"border-box\",\"both\",\"bottom\",\"break\",\"break-all\",\"break-word\",\"bullets\",\"button\",\"buttonface\",\"buttonhighlight\",\"buttonshadow\",\"buttontext\",\"calc\",\"cambodian\",\"capitalize\",\"caps-lock-indicator\",\"caption\",\"captiontext\",\"caret\",\"cell\",\"center\",\"checkbox\",\"circle\",\"cjk-decimal\",\"cjk-earthly-branch\",\"cjk-heavenly-stem\",\"cjk-ideographic\",\"clear\",\"clip\",\"close-quote\",\"col-resize\",\"collapse\",\"column\",\"compact\",\"condensed\",\"conic-gradient\",\"contain\",\"content\",\"contents\",\"content-box\",\"context-menu\",\"continuous\",\"copy\",\"counter\",\"counters\",\"cover\",\"crop\",\"cross\",\"crosshair\",\"currentcolor\",\"cursive\",\"cyclic\",\"dashed\",\"decimal\",\"decimal-leading-zero\",\"default\",\"default-button\",\"destination-atop\",\"destination-in\",\"destination-out\",\"destination-over\",\"devanagari\",\"disc\",\"discard\",\"disclosure-closed\",\"disclosure-open\",\"document\",\"dot-dash\",\"dot-dot-dash\",\"dotted\",\"double\",\"down\",\"e-resize\",\"ease\",\"ease-in\",\"ease-in-out\",\"ease-out\",\"element\",\"ellipse\",\"ellipsis\",\"embed\",\"end\",\"ethiopic\",\"ethiopic-abegede\",\"ethiopic-abegede-am-et\",\"ethiopic-abegede-gez\",\"ethiopic-abegede-ti-er\",\"ethiopic-abegede-ti-et\",\"ethiopic-halehame-aa-er\",\"ethiopic-halehame-aa-et\",\"ethiopic-halehame-am-et\",\"ethiopic-halehame-gez\",\"ethiopic-halehame-om-et\",\"ethiopic-halehame-sid-et\",\"ethiopic-halehame-so-et\",\"ethiopic-halehame-ti-er\",\"ethiopic-halehame-ti-et\",\"ethiopic-halehame-tig\",\"ethiopic-numeric\",\"ew-resize\",\"expanded\",\"extends\",\"extra-condensed\",\"extra-expanded\",\"fantasy\",\"fast\",\"fill\",\"fixed\",\"flat\",\"flex\",\"footnotes\",\"forwards\",\"from\",\"geometricPrecision\",\"georgian\",\"graytext\",\"groove\",\"gujarati\",\"gurmukhi\",\"hand\",\"hangul\",\"hangul-consonant\",\"hebrew\",\"help\",\"hidden\",\"hide\",\"high\",\"higher\",\"highlight\",\"highlighttext\",\"hiragana\",\"hiragana-iroha\",\"horizontal\",\"hsl\",\"hsla\",\"icon\",\"ignore\",\"inactiveborder\",\"inactivecaption\",\"inactivecaptiontext\",\"infinite\",\"infobackground\",\"infotext\",\"inherit\",\"initial\",\"inline\",\"inline-axis\",\"inline-block\",\"inline-flex\",\"inline-table\",\"inset\",\"inside\",\"intrinsic\",\"invert\",\"italic\",\"japanese-formal\",\"japanese-informal\",\"justify\",\"kannada\",\"katakana\",\"katakana-iroha\",\"keep-all\",\"khmer\",\"korean-hangul-formal\",\"korean-hanja-formal\",\"korean-hanja-informal\",\"landscape\",\"lao\",\"large\",\"larger\",\"left\",\"level\",\"lighter\",\"line-through\",\"linear\",\"linear-gradient\",\"lines\",\"list-item\",\"listbox\",\"listitem\",\"local\",\"logical\",\"loud\",\"lower\",\"lower-alpha\",\"lower-armenian\",\"lower-greek\",\"lower-hexadecimal\",\"lower-latin\",\"lower-norwegian\",\"lower-roman\",\"lowercase\",\"ltr\",\"malayalam\",\"match\",\"matrix\",\"matrix3d\",\"media-play-button\",\"media-slider\",\"media-sliderthumb\",\"media-volume-slider\",\"media-volume-sliderthumb\",\"medium\",\"menu\",\"menulist\",\"menulist-button\",\"menutext\",\"message-box\",\"middle\",\"min-intrinsic\",\"mix\",\"mongolian\",\"monospace\",\"move\",\"multiple\",\"myanmar\",\"n-resize\",\"narrower\",\"ne-resize\",\"nesw-resize\",\"no-close-quote\",\"no-drop\",\"no-open-quote\",\"no-repeat\",\"none\",\"normal\",\"not-allowed\",\"nowrap\",\"ns-resize\",\"numbers\",\"numeric\",\"nw-resize\",\"nwse-resize\",\"oblique\",\"octal\",\"open-quote\",\"optimizeLegibility\",\"optimizeSpeed\",\"oriya\",\"oromo\",\"outset\",\"outside\",\"outside-shape\",\"overlay\",\"overline\",\"padding\",\"padding-box\",\"painted\",\"page\",\"paused\",\"persian\",\"perspective\",\"plus-darker\",\"plus-lighter\",\"pointer\",\"polygon\",\"portrait\",\"pre\",\"pre-line\",\"pre-wrap\",\"preserve-3d\",\"progress\",\"push-button\",\"radial-gradient\",\"radio\",\"read-only\",\"read-write\",\"read-write-plaintext-only\",\"rectangle\",\"region\",\"relative\",\"repeat\",\"repeating-linear-gradient\",\"repeating-radial-gradient\",\"repeating-conic-gradient\",\"repeat-x\",\"repeat-y\",\"reset\",\"reverse\",\"rgb\",\"rgba\",\"ridge\",\"right\",\"rotate\",\"rotate3d\",\"rotateX\",\"rotateY\",\"rotateZ\",\"round\",\"row-resize\",\"rtl\",\"run-in\",\"running\",\"s-resize\",\"sans-serif\",\"scale\",\"scale3d\",\"scaleX\",\"scaleY\",\"scaleZ\",\"scroll\",\"scrollbar\",\"scroll-position\",\"se-resize\",\"searchfield\",\"searchfield-cancel-button\",\"searchfield-decoration\",\"searchfield-results-button\",\"searchfield-results-decoration\",\"semi-condensed\",\"semi-expanded\",\"separate\",\"serif\",\"show\",\"sidama\",\"simp-chinese-formal\",\"simp-chinese-informal\",\"single\",\"skew\",\"skewX\",\"skewY\",\"skip-white-space\",\"slide\",\"slider-horizontal\",\"slider-vertical\",\"sliderthumb-horizontal\",\"sliderthumb-vertical\",\"slow\",\"small\",\"small-caps\",\"small-caption\",\"smaller\",\"solid\",\"somali\",\"source-atop\",\"source-in\",\"source-out\",\"source-over\",\"space\",\"spell-out\",\"square\",\"square-button\",\"standard\",\"start\",\"static\",\"status-bar\",\"stretch\",\"stroke\",\"sub\",\"subpixel-antialiased\",\"super\",\"sw-resize\",\"symbolic\",\"symbols\",\"table\",\"table-caption\",\"table-cell\",\"table-column\",\"table-column-group\",\"table-footer-group\",\"table-header-group\",\"table-row\",\"table-row-group\",\"tamil\",\"telugu\",\"text\",\"text-bottom\",\"text-top\",\"textarea\",\"textfield\",\"thai\",\"thick\",\"thin\",\"threeddarkshadow\",\"threedface\",\"threedhighlight\",\"threedlightshadow\",\"threedshadow\",\"tibetan\",\"tigre\",\"tigrinya-er\",\"tigrinya-er-abegede\",\"tigrinya-et\",\"tigrinya-et-abegede\",\"to\",\"top\",\"trad-chinese-formal\",\"trad-chinese-informal\",\"translate\",\"translate3d\",\"translateX\",\"translateY\",\"translateZ\",\"transparent\",\"ultra-condensed\",\"ultra-expanded\",\"underline\",\"up\",\"upper-alpha\",\"upper-armenian\",\"upper-greek\",\"upper-hexadecimal\",\"upper-latin\",\"upper-norwegian\",\"upper-roman\",\"uppercase\",\"urdu\",\"url\",\"var\",\"vertical\",\"vertical-text\",\"visible\",\"visibleFill\",\"visiblePainted\",\"visibleStroke\",\"visual\",\"w-resize\",\"wait\",\"wave\",\"wider\",\"window\",\"windowframe\",\"windowtext\",\"words\",\"x-large\",\"x-small\",\"xor\",\"xx-large\",\"xx-small\",\"bicubic\",\"optimizespeed\",\"grayscale\",\"row\",\"row-reverse\",\"wrap\",\"wrap-reverse\",\"column-reverse\",\"flex-start\",\"flex-end\",\"space-between\",\"space-around\", \"unset\"];\n\nvar wordOperatorKeywords_ = [\"in\",\"and\",\"or\",\"not\",\"is not\",\"is a\",\"is\",\"isnt\",\"defined\",\"if unless\"],\n    blockKeywords_ = [\"for\",\"if\",\"else\",\"unless\", \"from\", \"to\"],\n    commonAtoms_ = [\"null\",\"true\",\"false\",\"href\",\"title\",\"type\",\"not-allowed\",\"readonly\",\"disabled\"],\n    commonDef_ = [\"@font-face\", \"@keyframes\", \"@media\", \"@viewport\", \"@page\", \"@host\", \"@supports\", \"@block\", \"@css\"];\n\nvar hintWords = tagKeywords_.concat(documentTypes_,mediaTypes_,mediaFeatures_,\n                                    propertyKeywords_,nonStandardPropertyKeywords_,\n                                    colorKeywords_,valueKeywords_,fontProperties_,\n                                    wordOperatorKeywords_,blockKeywords_,\n                                    commonAtoms_,commonDef_);\n\nfunction wordRegexp(words) {\n  words = words.sort(function(a,b){return b > a;});\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nfunction keySet(array) {\n  var keys = {};\n  for (var i = 0; i < array.length; ++i) keys[array[i]] = true;\n  return keys;\n}\n\nfunction escapeRegExp(text) {\n  return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, \"\\\\$&\");\n}\n\nvar tagKeywords = keySet(tagKeywords_),\n    tagVariablesRegexp = /^(a|b|i|s|col|em)$/i,\n    propertyKeywords = keySet(propertyKeywords_),\n    nonStandardPropertyKeywords = keySet(nonStandardPropertyKeywords_),\n    valueKeywords = keySet(valueKeywords_),\n    colorKeywords = keySet(colorKeywords_),\n    documentTypes = keySet(documentTypes_),\n    documentTypesRegexp = wordRegexp(documentTypes_),\n    mediaFeatures = keySet(mediaFeatures_),\n    mediaTypes = keySet(mediaTypes_),\n    fontProperties = keySet(fontProperties_),\n    operatorsRegexp = /^\\s*([.]{2,3}|&&|\\|\\||\\*\\*|[?!=:]?=|[-+*\\/%<>]=?|\\?:|\\~)/,\n    wordOperatorKeywordsRegexp = wordRegexp(wordOperatorKeywords_),\n    blockKeywords = keySet(blockKeywords_),\n    vendorPrefixesRegexp = new RegExp(/^\\-(moz|ms|o|webkit)-/i),\n    commonAtoms = keySet(commonAtoms_),\n    firstWordMatch = \"\",\n    states = {},\n    ch,\n    style,\n    type,\n    override;\n\n/**\n * Tokenizers\n */\nfunction tokenBase(stream, state) {\n  firstWordMatch = stream.string.match(/(^[\\w-]+\\s*=\\s*$)|(^\\s*[\\w-]+\\s*=\\s*[\\w-])|(^\\s*(\\.|#|@|\\$|\\&|\\[|\\d|\\+|::?|\\{|\\>|~|\\/)?\\s*[\\w-]*([a-z0-9-]|\\*|\\/\\*)(\\(|,)?)/);\n  state.context.line.firstWord = firstWordMatch ? firstWordMatch[0].replace(/^\\s*/, \"\") : \"\";\n  state.context.line.indent = stream.indentation();\n  ch = stream.peek();\n\n  // Line comment\n  if (stream.match(\"//\")) {\n    stream.skipToEnd();\n    return [\"comment\", \"comment\"];\n  }\n  // Block comment\n  if (stream.match(\"/*\")) {\n    state.tokenize = tokenCComment;\n    return tokenCComment(stream, state);\n  }\n  // String\n  if (ch == \"\\\"\" || ch == \"'\") {\n    stream.next();\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  // Def\n  if (ch == \"@\") {\n    stream.next();\n    stream.eatWhile(/[\\w\\\\-]/);\n    return [\"def\", stream.current()];\n  }\n  // ID selector or Hex color\n  if (ch == \"#\") {\n    stream.next();\n    // Hex color\n    if (stream.match(/^[0-9a-f]{3}([0-9a-f]([0-9a-f]{2}){0,2})?\\b(?!-)/i)) {\n      return [\"atom\", \"atom\"];\n    }\n    // ID selector\n    if (stream.match(/^[a-z][\\w-]*/i)) {\n      return [\"builtin\", \"hash\"];\n    }\n  }\n  // Vendor prefixes\n  if (stream.match(vendorPrefixesRegexp)) {\n    return [\"meta\", \"vendor-prefixes\"];\n  }\n  // Numbers\n  if (stream.match(/^-?[0-9]?\\.?[0-9]/)) {\n    stream.eatWhile(/[a-z%]/i);\n    return [\"number\", \"unit\"];\n  }\n  // !important|optional\n  if (ch == \"!\") {\n    stream.next();\n    return [stream.match(/^(important|optional)/i) ? \"keyword\": \"operator\", \"important\"];\n  }\n  // Class\n  if (ch == \".\" && stream.match(/^\\.[a-z][\\w-]*/i)) {\n    return [\"qualifier\", \"qualifier\"];\n  }\n  // url url-prefix domain regexp\n  if (stream.match(documentTypesRegexp)) {\n    if (stream.peek() == \"(\") state.tokenize = tokenParenthesized;\n    return [\"property\", \"word\"];\n  }\n  // Mixins / Functions\n  if (stream.match(/^[a-z][\\w-]*\\(/i)) {\n    stream.backUp(1);\n    return [\"keyword\", \"mixin\"];\n  }\n  // Block mixins\n  if (stream.match(/^(\\+|-)[a-z][\\w-]*\\(/i)) {\n    stream.backUp(1);\n    return [\"keyword\", \"block-mixin\"];\n  }\n  // Parent Reference BEM naming\n  if (stream.string.match(/^\\s*&/) && stream.match(/^[-_]+[a-z][\\w-]*/)) {\n    return [\"qualifier\", \"qualifier\"];\n  }\n  // / Root Reference & Parent Reference\n  if (stream.match(/^(\\/|&)(-|_|:|\\.|#|[a-z])/)) {\n    stream.backUp(1);\n    return [\"variableName.special\", \"reference\"];\n  }\n  if (stream.match(/^&{1}\\s*$/)) {\n    return [\"variableName.special\", \"reference\"];\n  }\n  // Word operator\n  if (stream.match(wordOperatorKeywordsRegexp)) {\n    return [\"operator\", \"operator\"];\n  }\n  // Word\n  if (stream.match(/^\\$?[-_]*[a-z0-9]+[\\w-]*/i)) {\n    // Variable\n    if (stream.match(/^(\\.|\\[)[\\w-\\'\\\"\\]]+/i, false)) {\n      if (!wordIsTag(stream.current())) {\n        stream.match('.');\n        return [\"variable\", \"variable-name\"];\n      }\n    }\n    return [\"variable\", \"word\"];\n  }\n  // Operators\n  if (stream.match(operatorsRegexp)) {\n    return [\"operator\", stream.current()];\n  }\n  // Delimiters\n  if (/[:;,{}\\[\\]\\(\\)]/.test(ch)) {\n    stream.next();\n    return [null, ch];\n  }\n  // Non-detected items\n  stream.next();\n  return [null, null];\n}\n\n/**\n * Token comment\n */\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return [\"comment\", \"comment\"];\n}\n\n/**\n * Token string\n */\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        if (quote == \")\") stream.backUp(1);\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    if (ch == quote || !escaped && quote != \")\") state.tokenize = null;\n    return [\"string\", \"string\"];\n  };\n}\n\n/**\n * Token parenthesized\n */\nfunction tokenParenthesized(stream, state) {\n  stream.next(); // Must be \"(\"\n  if (!stream.match(/\\s*[\\\"\\')]/, false))\n    state.tokenize = tokenString(\")\");\n  else\n    state.tokenize = null;\n  return [null, \"(\"];\n}\n\n/**\n * Context management\n */\nfunction Context(type, indent, prev, line) {\n  this.type = type;\n  this.indent = indent;\n  this.prev = prev;\n  this.line = line || {firstWord: \"\", indent: 0};\n}\n\nfunction pushContext(state, stream, type, indent) {\n  indent = indent >= 0 ? indent : stream.indentUnit;\n  state.context = new Context(type, stream.indentation() + indent, state.context);\n  return type;\n}\n\nfunction popContext(state, stream, currentIndent) {\n  var contextIndent = state.context.indent - stream.indentUnit;\n  currentIndent = currentIndent || false;\n  state.context = state.context.prev;\n  if (currentIndent) state.context.indent = contextIndent;\n  return state.context.type;\n}\n\nfunction pass(type, stream, state) {\n  return states[state.context.type](type, stream, state);\n}\n\nfunction popAndPass(type, stream, state, n) {\n  for (var i = n || 1; i > 0; i--)\n    state.context = state.context.prev;\n  return pass(type, stream, state);\n}\n\n\n/**\n * Parser\n */\nfunction wordIsTag(word) {\n  return word.toLowerCase() in tagKeywords;\n}\n\nfunction wordIsProperty(word) {\n  word = word.toLowerCase();\n  return word in propertyKeywords || word in fontProperties;\n}\n\nfunction wordIsBlock(word) {\n  return word.toLowerCase() in blockKeywords;\n}\n\nfunction wordIsVendorPrefix(word) {\n  return word.toLowerCase().match(vendorPrefixesRegexp);\n}\n\nfunction wordAsValue(word) {\n  var wordLC = word.toLowerCase();\n  var override = \"variable\";\n  if (wordIsTag(word)) override = \"tag\";\n  else if (wordIsBlock(word)) override = \"block-keyword\";\n  else if (wordIsProperty(word)) override = \"property\";\n  else if (wordLC in valueKeywords || wordLC in commonAtoms) override = \"atom\";\n  else if (wordLC == \"return\" || wordLC in colorKeywords) override = \"keyword\";\n\n  // Font family\n  else if (word.match(/^[A-Z]/)) override = \"string\";\n  return override;\n}\n\nfunction typeIsBlock(type, stream) {\n  return ((endOfLine(stream) && (type == \"{\" || type == \"]\" || type == \"hash\" || type == \"qualifier\")) || type == \"block-mixin\");\n}\n\nfunction typeIsInterpolation(type, stream) {\n  return type == \"{\" && stream.match(/^\\s*\\$?[\\w-]+/i, false);\n}\n\nfunction typeIsPseudo(type, stream) {\n  return type == \":\" && stream.match(/^[a-z-]+/, false);\n}\n\nfunction startOfLine(stream) {\n  return stream.sol() || stream.string.match(new RegExp(\"^\\\\s*\" + escapeRegExp(stream.current())));\n}\n\nfunction endOfLine(stream) {\n  return stream.eol() || stream.match(/^\\s*$/, false);\n}\n\nfunction firstWordOfLine(line) {\n  var re = /^\\s*[-_]*[a-z0-9]+[\\w-]*/i;\n  var result = typeof line == \"string\" ? line.match(re) : line.string.match(re);\n  return result ? result[0].replace(/^\\s*/, \"\") : \"\";\n}\n\n\n/**\n * Block\n */\nstates.block = function(type, stream, state) {\n  if ((type == \"comment\" && startOfLine(stream)) ||\n      (type == \",\" && endOfLine(stream)) ||\n      type == \"mixin\") {\n    return pushContext(state, stream, \"block\", 0);\n  }\n  if (typeIsInterpolation(type, stream)) {\n    return pushContext(state, stream, \"interpolation\");\n  }\n  if (endOfLine(stream) && type == \"]\") {\n    if (!/^\\s*(\\.|#|:|\\[|\\*|&)/.test(stream.string) && !wordIsTag(firstWordOfLine(stream))) {\n      return pushContext(state, stream, \"block\", 0);\n    }\n  }\n  if (typeIsBlock(type, stream)) {\n    return pushContext(state, stream, \"block\");\n  }\n  if (type == \"}\" && endOfLine(stream)) {\n    return pushContext(state, stream, \"block\", 0);\n  }\n  if (type == \"variable-name\") {\n    if (stream.string.match(/^\\s?\\$[\\w-\\.\\[\\]\\'\\\"]+$/) || wordIsBlock(firstWordOfLine(stream))) {\n      return pushContext(state, stream, \"variableName\");\n    }\n    else {\n      return pushContext(state, stream, \"variableName\", 0);\n    }\n  }\n  if (type == \"=\") {\n    if (!endOfLine(stream) && !wordIsBlock(firstWordOfLine(stream))) {\n      return pushContext(state, stream, \"block\", 0);\n    }\n    return pushContext(state, stream, \"block\");\n  }\n  if (type == \"*\") {\n    if (endOfLine(stream) || stream.match(/\\s*(,|\\.|#|\\[|:|{)/,false)) {\n      override = \"tag\";\n      return pushContext(state, stream, \"block\");\n    }\n  }\n  if (typeIsPseudo(type, stream)) {\n    return pushContext(state, stream, \"pseudo\");\n  }\n  if (/@(font-face|media|supports|(-moz-)?document)/.test(type)) {\n    return pushContext(state, stream, endOfLine(stream) ? \"block\" : \"atBlock\");\n  }\n  if (/@(-(moz|ms|o|webkit)-)?keyframes$/.test(type)) {\n    return pushContext(state, stream, \"keyframes\");\n  }\n  if (/@extends?/.test(type)) {\n    return pushContext(state, stream, \"extend\", 0);\n  }\n  if (type && type.charAt(0) == \"@\") {\n\n    // Property Lookup\n    if (stream.indentation() > 0 && wordIsProperty(stream.current().slice(1))) {\n      override = \"variable\";\n      return \"block\";\n    }\n    if (/(@import|@require|@charset)/.test(type)) {\n      return pushContext(state, stream, \"block\", 0);\n    }\n    return pushContext(state, stream, \"block\");\n  }\n  if (type == \"reference\" && endOfLine(stream)) {\n    return pushContext(state, stream, \"block\");\n  }\n  if (type == \"(\") {\n    return pushContext(state, stream, \"parens\");\n  }\n\n  if (type == \"vendor-prefixes\") {\n    return pushContext(state, stream, \"vendorPrefixes\");\n  }\n  if (type == \"word\") {\n    var word = stream.current();\n    override = wordAsValue(word);\n\n    if (override == \"property\") {\n      if (startOfLine(stream)) {\n        return pushContext(state, stream, \"block\", 0);\n      } else {\n        override = \"atom\";\n        return \"block\";\n      }\n    }\n\n    if (override == \"tag\") {\n\n      // tag is a css value\n      if (/embed|menu|pre|progress|sub|table/.test(word)) {\n        if (wordIsProperty(firstWordOfLine(stream))) {\n          override = \"atom\";\n          return \"block\";\n        }\n      }\n\n      // tag is an attribute\n      if (stream.string.match(new RegExp(\"\\\\[\\\\s*\" + word + \"|\" + word +\"\\\\s*\\\\]\"))) {\n        override = \"atom\";\n        return \"block\";\n      }\n\n      // tag is a variable\n      if (tagVariablesRegexp.test(word)) {\n        if ((startOfLine(stream) && stream.string.match(/=/)) ||\n            (!startOfLine(stream) &&\n             !stream.string.match(/^(\\s*\\.|#|\\&|\\[|\\/|>|\\*)/) &&\n             !wordIsTag(firstWordOfLine(stream)))) {\n          override = \"variable\";\n          if (wordIsBlock(firstWordOfLine(stream)))  return \"block\";\n          return pushContext(state, stream, \"block\", 0);\n        }\n      }\n\n      if (endOfLine(stream)) return pushContext(state, stream, \"block\");\n    }\n    if (override == \"block-keyword\") {\n      override = \"keyword\";\n\n      // Postfix conditionals\n      if (stream.current(/(if|unless)/) && !startOfLine(stream)) {\n        return \"block\";\n      }\n      return pushContext(state, stream, \"block\");\n    }\n    if (word == \"return\") return pushContext(state, stream, \"block\", 0);\n\n    // Placeholder selector\n    if (override == \"variable\" && stream.string.match(/^\\s?\\$[\\w-\\.\\[\\]\\'\\\"]+$/)) {\n      return pushContext(state, stream, \"block\");\n    }\n  }\n  return state.context.type;\n};\n\n\n/**\n * Parens\n */\nstates.parens = function(type, stream, state) {\n  if (type == \"(\") return pushContext(state, stream, \"parens\");\n  if (type == \")\") {\n    if (state.context.prev.type == \"parens\") {\n      return popContext(state, stream);\n    }\n    if ((stream.string.match(/^[a-z][\\w-]*\\(/i) && endOfLine(stream)) ||\n        wordIsBlock(firstWordOfLine(stream)) ||\n        /(\\.|#|:|\\[|\\*|&|>|~|\\+|\\/)/.test(firstWordOfLine(stream)) ||\n        (!stream.string.match(/^-?[a-z][\\w-\\.\\[\\]\\'\\\"]*\\s*=/) &&\n         wordIsTag(firstWordOfLine(stream)))) {\n      return pushContext(state, stream, \"block\");\n    }\n    if (stream.string.match(/^[\\$-]?[a-z][\\w-\\.\\[\\]\\'\\\"]*\\s*=/) ||\n        stream.string.match(/^\\s*(\\(|\\)|[0-9])/) ||\n        stream.string.match(/^\\s+[a-z][\\w-]*\\(/i) ||\n        stream.string.match(/^\\s+[\\$-]?[a-z]/i)) {\n      return pushContext(state, stream, \"block\", 0);\n    }\n    if (endOfLine(stream)) return pushContext(state, stream, \"block\");\n    else return pushContext(state, stream, \"block\", 0);\n  }\n  if (type && type.charAt(0) == \"@\" && wordIsProperty(stream.current().slice(1))) {\n    override = \"variable\";\n  }\n  if (type == \"word\") {\n    var word = stream.current();\n    override = wordAsValue(word);\n    if (override == \"tag\" && tagVariablesRegexp.test(word)) {\n      override = \"variable\";\n    }\n    if (override == \"property\" || word == \"to\") override = \"atom\";\n  }\n  if (type == \"variable-name\") {\n    return pushContext(state, stream, \"variableName\");\n  }\n  if (typeIsPseudo(type, stream)) {\n    return pushContext(state, stream, \"pseudo\");\n  }\n  return state.context.type;\n};\n\n\n/**\n * Vendor prefixes\n */\nstates.vendorPrefixes = function(type, stream, state) {\n  if (type == \"word\") {\n    override = \"property\";\n    return pushContext(state, stream, \"block\", 0);\n  }\n  return popContext(state, stream);\n};\n\n\n/**\n * Pseudo\n */\nstates.pseudo = function(type, stream, state) {\n  if (!wordIsProperty(firstWordOfLine(stream.string))) {\n    stream.match(/^[a-z-]+/);\n    override = \"variableName.special\";\n    if (endOfLine(stream)) return pushContext(state, stream, \"block\");\n    return popContext(state, stream);\n  }\n  return popAndPass(type, stream, state);\n};\n\n\n/**\n * atBlock\n */\nstates.atBlock = function(type, stream, state) {\n  if (type == \"(\") return pushContext(state, stream, \"atBlock_parens\");\n  if (typeIsBlock(type, stream)) {\n    return pushContext(state, stream, \"block\");\n  }\n  if (typeIsInterpolation(type, stream)) {\n    return pushContext(state, stream, \"interpolation\");\n  }\n  if (type == \"word\") {\n    var word = stream.current().toLowerCase();\n    if (/^(only|not|and|or)$/.test(word))\n      override = \"keyword\";\n    else if (documentTypes.hasOwnProperty(word))\n      override = \"tag\";\n    else if (mediaTypes.hasOwnProperty(word))\n      override = \"attribute\";\n    else if (mediaFeatures.hasOwnProperty(word))\n      override = \"property\";\n    else if (nonStandardPropertyKeywords.hasOwnProperty(word))\n      override = \"string.special\";\n    else override = wordAsValue(stream.current());\n    if (override == \"tag\" && endOfLine(stream)) {\n      return pushContext(state, stream, \"block\");\n    }\n  }\n  if (type == \"operator\" && /^(not|and|or)$/.test(stream.current())) {\n    override = \"keyword\";\n  }\n  return state.context.type;\n};\n\nstates.atBlock_parens = function(type, stream, state) {\n  if (type == \"{\" || type == \"}\") return state.context.type;\n  if (type == \")\") {\n    if (endOfLine(stream)) return pushContext(state, stream, \"block\");\n    else return pushContext(state, stream, \"atBlock\");\n  }\n  if (type == \"word\") {\n    var word = stream.current().toLowerCase();\n    override = wordAsValue(word);\n    if (/^(max|min)/.test(word)) override = \"property\";\n    if (override == \"tag\") {\n      tagVariablesRegexp.test(word) ? override = \"variable\" : override = \"atom\";\n    }\n    return state.context.type;\n  }\n  return states.atBlock(type, stream, state);\n};\n\n\n/**\n * Keyframes\n */\nstates.keyframes = function(type, stream, state) {\n  if (stream.indentation() == \"0\" && ((type == \"}\" && startOfLine(stream)) || type == \"]\" || type == \"hash\"\n                                      || type == \"qualifier\" || wordIsTag(stream.current()))) {\n    return popAndPass(type, stream, state);\n  }\n  if (type == \"{\") return pushContext(state, stream, \"keyframes\");\n  if (type == \"}\") {\n    if (startOfLine(stream)) return popContext(state, stream, true);\n    else return pushContext(state, stream, \"keyframes\");\n  }\n  if (type == \"unit\" && /^[0-9]+\\%$/.test(stream.current())) {\n    return pushContext(state, stream, \"keyframes\");\n  }\n  if (type == \"word\") {\n    override = wordAsValue(stream.current());\n    if (override == \"block-keyword\") {\n      override = \"keyword\";\n      return pushContext(state, stream, \"keyframes\");\n    }\n  }\n  if (/@(font-face|media|supports|(-moz-)?document)/.test(type)) {\n    return pushContext(state, stream, endOfLine(stream) ? \"block\" : \"atBlock\");\n  }\n  if (type == \"mixin\") {\n    return pushContext(state, stream, \"block\", 0);\n  }\n  return state.context.type;\n};\n\n\n/**\n * Interpolation\n */\nstates.interpolation = function(type, stream, state) {\n  if (type == \"{\") popContext(state, stream) && pushContext(state, stream, \"block\");\n  if (type == \"}\") {\n    if (stream.string.match(/^\\s*(\\.|#|:|\\[|\\*|&|>|~|\\+|\\/)/i) ||\n        (stream.string.match(/^\\s*[a-z]/i) && wordIsTag(firstWordOfLine(stream)))) {\n      return pushContext(state, stream, \"block\");\n    }\n    if (!stream.string.match(/^(\\{|\\s*\\&)/) ||\n        stream.match(/\\s*[\\w-]/,false)) {\n      return pushContext(state, stream, \"block\", 0);\n    }\n    return pushContext(state, stream, \"block\");\n  }\n  if (type == \"variable-name\") {\n    return pushContext(state, stream, \"variableName\", 0);\n  }\n  if (type == \"word\") {\n    override = wordAsValue(stream.current());\n    if (override == \"tag\") override = \"atom\";\n  }\n  return state.context.type;\n};\n\n\n/**\n * Extend/s\n */\nstates.extend = function(type, stream, state) {\n  if (type == \"[\" || type == \"=\") return \"extend\";\n  if (type == \"]\") return popContext(state, stream);\n  if (type == \"word\") {\n    override = wordAsValue(stream.current());\n    return \"extend\";\n  }\n  return popContext(state, stream);\n};\n\n\n/**\n * Variable name\n */\nstates.variableName = function(type, stream, state) {\n  if (type == \"string\" || type == \"[\" || type == \"]\" || stream.current().match(/^(\\.|\\$)/)) {\n    if (stream.current().match(/^\\.[\\w-]+/i)) override = \"variable\";\n    return \"variableName\";\n  }\n  return popAndPass(type, stream, state);\n};\n\nexport const stylus = {\n  name: \"stylus\",\n  startState: function() {\n    return {\n      tokenize: null,\n      state: \"block\",\n      context: new Context(\"block\", 0, null)\n    };\n  },\n  token: function(stream, state) {\n    if (!state.tokenize && stream.eatSpace()) return null;\n    style = (state.tokenize || tokenBase)(stream, state);\n    if (style && typeof style == \"object\") {\n      type = style[1];\n      style = style[0];\n    }\n    override = style;\n    state.state = states[state.state](type, stream, state);\n    return override;\n  },\n  indent: function(state, textAfter, iCx) {\n    var cx = state.context,\n        ch = textAfter && textAfter.charAt(0),\n        indent = cx.indent,\n        lineFirstWord = firstWordOfLine(textAfter),\n        lineIndent = cx.line.indent,\n        prevLineFirstWord = state.context.prev ? state.context.prev.line.firstWord : \"\",\n        prevLineIndent = state.context.prev ? state.context.prev.line.indent : lineIndent;\n\n    if (cx.prev &&\n        (ch == \"}\" && (cx.type == \"block\" || cx.type == \"atBlock\" || cx.type == \"keyframes\") ||\n         ch == \")\" && (cx.type == \"parens\" || cx.type == \"atBlock_parens\") ||\n         ch == \"{\" && (cx.type == \"at\"))) {\n      indent = cx.indent - iCx.unit;\n    } else if (!(/(\\})/.test(ch))) {\n      if (/@|\\$|\\d/.test(ch) ||\n          /^\\{/.test(textAfter) ||\n/^\\s*\\/(\\/|\\*)/.test(textAfter) ||\n          /^\\s*\\/\\*/.test(prevLineFirstWord) ||\n          /^\\s*[\\w-\\.\\[\\]\\'\\\"]+\\s*(\\?|:|\\+)?=/i.test(textAfter) ||\n          /^(\\+|-)?[a-z][\\w-]*\\(/i.test(textAfter) ||\n          /^return/.test(textAfter) ||\n          wordIsBlock(lineFirstWord)) {\n        indent = lineIndent;\n      } else if (/(\\.|#|:|\\[|\\*|&|>|~|\\+|\\/)/.test(ch) || wordIsTag(lineFirstWord)) {\n        if (/\\,\\s*$/.test(prevLineFirstWord)) {\n          indent = prevLineIndent;\n        } else if (/(\\.|#|:|\\[|\\*|&|>|~|\\+|\\/)/.test(prevLineFirstWord) || wordIsTag(prevLineFirstWord)) {\n          indent = lineIndent <= prevLineIndent ? prevLineIndent : prevLineIndent + iCx.unit;\n        } else {\n          indent = lineIndent;\n        }\n      } else if (!/,\\s*$/.test(textAfter) && (wordIsVendorPrefix(lineFirstWord) || wordIsProperty(lineFirstWord))) {\n        if (wordIsBlock(prevLineFirstWord)) {\n          indent = lineIndent <= prevLineIndent ? prevLineIndent : prevLineIndent + iCx.unit;\n        } else if (/^\\{/.test(prevLineFirstWord)) {\n          indent = lineIndent <= prevLineIndent ? lineIndent : prevLineIndent + iCx.unit;\n        } else if (wordIsVendorPrefix(prevLineFirstWord) || wordIsProperty(prevLineFirstWord)) {\n          indent = lineIndent >= prevLineIndent ? prevLineIndent : lineIndent;\n        } else if (/^(\\.|#|:|\\[|\\*|&|@|\\+|\\-|>|~|\\/)/.test(prevLineFirstWord) ||\n                   /=\\s*$/.test(prevLineFirstWord) ||\n                   wordIsTag(prevLineFirstWord) ||\n                   /^\\$[\\w-\\.\\[\\]\\'\\\"]/.test(prevLineFirstWord)) {\n          indent = prevLineIndent + iCx.unit;\n        } else {\n          indent = lineIndent;\n        }\n      }\n    }\n    return indent;\n  },\n  languageData: {\n    indentOnInput: /^\\s*\\}$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    autocomplete: hintWords\n  }\n};\n"], "mappings": ";;;AACA,IAAI,eAAe,CAAC,KAAI,QAAO,WAAU,QAAO,WAAU,SAAQ,SAAS,KAAK,QAAO,OAAO,OAAM,WAAU,cAAa,QAAO,MAAK,UAAS,UAAS,WAAU,QAAQ,QAAO,OAAM,YAAW,QAAO,YAAW,MAAK,OAAM,WAAU,OAAM,OAAO,MAAK,MAAK,MAAK,SAAQ,YAAW,cAAa,UAAS,UAAS,QAAO,MAAM,MAAK,MAAK,MAAK,MAAK,MAAK,QAAO,UAAS,UAAS,MAAK,QAAO,KAAI,UAAU,OAAM,SAAQ,OAAM,OAAM,UAAS,SAAQ,UAAS,MAAK,QAAO,QAAO,OAAO,QAAO,WAAU,QAAO,YAAW,QAAO,SAAQ,OAAM,QAAO,YAAY,YAAW,UAAS,MAAK,YAAW,UAAS,UAAS,KAAI,SAAQ,OAAO,YAAW,KAAI,MAAK,MAAK,QAAO,KAAI,QAAO,UAAS,WAAU,UAAU,SAAQ,UAAS,QAAO,UAAS,SAAQ,OAAM,WAAU,OAAM,SAAQ,SAAQ,MAAK,YAAW,SAAQ,MAAK,SAAQ,QAAO,MAAK,SAAS,KAAI,MAAK,OAAM,OAAO;AAI11B,IAAI,iBAAiB,CAAC,UAAU,UAAU,cAAc,KAAK;AAC7D,IAAI,cAAc,CAAC,OAAM,SAAQ,WAAU,YAAW,SAAQ,cAAa,UAAS,OAAM,MAAK,UAAU;AACzG,IAAI,iBAAiB,CAAC,SAAQ,aAAY,aAAY,UAAS,cAAa,cAAa,gBAAe,oBAAmB,oBAAmB,iBAAgB,qBAAoB,qBAAoB,gBAAe,oBAAmB,oBAAmB,uBAAsB,2BAA0B,2BAA0B,SAAQ,aAAY,aAAY,eAAc,mBAAkB,mBAAkB,cAAa,kBAAiB,kBAAiB,cAAa,kBAAiB,kBAAiB,QAAO,QAAO,iBAAgB,qBAAqB;AACxiB,IAAI,oBAAoB,CAAC,iBAAgB,eAAc,cAAa,oBAAmB,sBAAqB,gBAAe,aAAY,mBAAkB,uBAAsB,sBAAqB,uBAAsB,6BAA4B,kBAAiB,wBAAuB,6BAA4B,cAAa,WAAU,uBAAsB,cAAa,yBAAwB,mBAAkB,oBAAmB,oBAAmB,qBAAoB,uBAAsB,qBAAoB,mBAAkB,kBAAiB,WAAU,SAAQ,kBAAiB,kBAAiB,kBAAiB,mBAAkB,UAAS,iBAAgB,uBAAsB,6BAA4B,8BAA6B,uBAAsB,uBAAsB,mBAAkB,gBAAe,gBAAe,uBAAsB,uBAAsB,sBAAqB,uBAAsB,sBAAqB,eAAc,qBAAoB,qBAAoB,qBAAoB,iBAAgB,gBAAe,sBAAqB,sBAAqB,sBAAqB,kBAAiB,gBAAe,cAAa,oBAAmB,0BAAyB,2BAA0B,oBAAmB,oBAAmB,gBAAe,UAAS,wBAAuB,cAAa,cAAa,eAAc,gBAAe,gBAAe,gBAAe,SAAQ,QAAO,SAAQ,iBAAgB,gBAAe,eAAc,cAAa,eAAc,qBAAoB,qBAAoB,qBAAoB,eAAc,gBAAe,WAAU,WAAU,qBAAoB,iBAAgB,QAAO,OAAM,aAAY,cAAa,UAAS,aAAY,WAAU,qBAAoB,6BAA4B,4BAA2B,8BAA6B,6BAA4B,qBAAoB,sBAAqB,aAAY,eAAc,OAAM,gBAAe,QAAO,cAAa,kBAAiB,aAAY,aAAY,eAAc,aAAY,SAAQ,gBAAe,aAAY,aAAY,QAAO,yBAAwB,eAAc,gBAAe,0BAAyB,aAAY,oBAAmB,gBAAe,cAAa,kBAAiB,gBAAe,2BAA0B,qBAAoB,2BAA0B,0BAAyB,wBAAuB,yBAAwB,eAAc,QAAO,aAAY,qBAAoB,kBAAiB,sBAAqB,kBAAiB,eAAc,mBAAkB,qBAAoB,YAAW,gBAAe,kBAAiB,iBAAgB,uBAAsB,yBAAwB,sBAAqB,uBAAsB,UAAS,WAAU,QAAO,qBAAoB,mBAAkB,oBAAmB,oBAAmB,mBAAkB,QAAO,kBAAiB,cAAa,eAAc,iBAAgB,sBAAqB,uBAAsB,0BAAyB,cAAa,oBAAmB,uBAAsB,mBAAkB,UAAS,iBAAgB,eAAc,gBAAe,cAAa,iBAAgB,SAAQ,qBAAoB,gBAAe,sBAAqB,iBAAgB,iBAAgB,cAAa,aAAY,cAAa,aAAY,WAAU,YAAW,aAAY,YAAW,aAAY,UAAS,cAAa,mBAAkB,WAAU,SAAQ,WAAU,WAAU,iBAAgB,kBAAiB,iBAAgB,iBAAgB,YAAW,kBAAiB,iBAAgB,cAAa,cAAa,WAAU,kBAAiB,gBAAe,iBAAgB,eAAc,QAAO,oBAAmB,qBAAoB,qBAAoB,eAAc,SAAQ,eAAc,gBAAe,eAAc,sBAAqB,SAAQ,eAAc,eAAc,YAAW,sBAAqB,oBAAmB,UAAS,sBAAqB,uBAAsB,uBAAsB,mBAAkB,oBAAmB,UAAS,QAAO,cAAa,eAAc,YAAW,SAAQ,YAAW,kBAAiB,cAAa,iBAAgB,iBAAgB,aAAY,yBAAwB,gBAAe,gBAAe,iBAAgB,QAAO,SAAQ,YAAW,gBAAe,iBAAgB,qBAAoB,eAAc,UAAS,cAAa,YAAW,gBAAe,UAAS,eAAc,cAAa,mBAAkB,cAAa,mBAAkB,mBAAkB,yBAAwB,wBAAuB,wBAAuB,yBAAwB,iBAAgB,uBAAsB,0BAAyB,uBAAsB,eAAc,eAAc,gBAAe,gBAAe,iBAAgB,eAAc,oBAAmB,uBAAsB,kBAAiB,2BAA0B,aAAY,OAAM,aAAY,oBAAmB,mBAAkB,cAAa,oBAAmB,uBAAsB,uBAAsB,8BAA6B,gBAAe,kBAAiB,cAAa,iBAAgB,kBAAiB,gBAAe,eAAc,eAAc,cAAa,gBAAe,gBAAe,UAAS,eAAc,UAAS,SAAQ,eAAc,cAAa,gBAAe,aAAY,WAAU,aAAY,aAAY,QAAO,qBAAoB,UAAS,eAAc,iBAAgB,kBAAiB,cAAa,gBAAe,kBAAiB,uBAAsB,+BAA8B,mBAAkB,QAAO,gBAAe,aAAY,mBAAkB,UAAS,cAAa,cAAa,gBAAe,mBAAkB,UAAS,oBAAmB,qBAAoB,kBAAiB,mBAAkB,qBAAoB,kBAAiB,gBAAe,kBAAiB,kBAAiB,qBAAoB,gCAA+B,8BAA6B,eAAc,gBAAe,kBAAiB,oBAAoB;AAC9sL,IAAI,+BAA+B,CAAC,yBAAwB,wBAAuB,+BAA8B,wBAAuB,6BAA4B,0BAAyB,4BAA2B,yBAAwB,gBAAe,6BAA4B,0BAAyB,8BAA6B,kCAAiC,MAAM;AACxX,IAAI,kBAAkB,CAAC,eAAc,OAAM,iBAAgB,gBAAe,yBAAwB,gBAAe,eAAc,YAAY;AAC3I,IAAI,iBAAiB,CAAC,aAAY,gBAAe,QAAO,cAAa,SAAQ,SAAQ,UAAS,SAAQ,kBAAiB,QAAO,cAAa,SAAQ,aAAY,aAAY,cAAa,aAAY,SAAQ,kBAAiB,YAAW,WAAU,QAAO,YAAW,YAAW,iBAAgB,YAAW,aAAY,aAAY,eAAc,kBAAiB,cAAa,cAAa,WAAU,cAAa,gBAAe,iBAAgB,iBAAgB,iBAAgB,cAAa,YAAW,eAAc,WAAU,cAAa,aAAY,eAAc,eAAc,WAAU,aAAY,cAAa,QAAO,aAAY,QAAO,QAAO,SAAQ,eAAc,YAAW,WAAU,aAAY,UAAS,SAAQ,SAAQ,YAAW,iBAAgB,aAAY,gBAAe,aAAY,cAAa,aAAY,wBAAuB,aAAY,cAAa,aAAY,eAAc,iBAAgB,gBAAe,kBAAiB,kBAAiB,eAAc,QAAO,aAAY,SAAQ,WAAU,UAAS,oBAAmB,cAAa,gBAAe,gBAAe,kBAAiB,mBAAkB,qBAAoB,mBAAkB,mBAAkB,gBAAe,aAAY,aAAY,YAAW,eAAc,QAAO,WAAU,SAAQ,aAAY,UAAS,aAAY,UAAS,iBAAgB,aAAY,iBAAgB,iBAAgB,cAAa,aAAY,QAAO,QAAO,QAAO,cAAa,UAAS,iBAAgB,OAAM,aAAY,aAAY,eAAc,UAAS,cAAa,YAAW,YAAW,UAAS,UAAS,WAAU,aAAY,aAAY,QAAO,eAAc,aAAY,OAAM,QAAO,WAAU,UAAS,aAAY,UAAS,SAAQ,SAAQ,cAAa,UAAS,aAAa;AAC7qD,IAAI,iBAAiB,CAAC,SAAQ,YAAW,gBAAe,YAAW,iBAAgB,QAAO,qBAAoB,SAAQ,SAAQ,OAAM,cAAa,cAAa,aAAY,UAAS,WAAU,mBAAkB,eAAc,gBAAe,gBAAe,YAAW,aAAY,QAAO,QAAO,SAAQ,gBAAe,cAAa,gBAAe,cAAa,aAAY,YAAW,SAAQ,iBAAgB,UAAS,WAAU,SAAQ,SAAQ,cAAa,QAAO,UAAS,UAAS,cAAa,QAAO,UAAS,SAAQ,aAAY,cAAa,WAAU,UAAS,cAAa,mBAAkB,gBAAe,cAAa,QAAO,aAAY,cAAa,uBAAsB,WAAU,eAAc,SAAQ,QAAO,UAAS,YAAW,UAAS,eAAc,sBAAqB,qBAAoB,mBAAkB,SAAQ,QAAO,eAAc,cAAa,YAAW,UAAS,WAAU,aAAY,kBAAiB,WAAU,WAAU,YAAW,eAAc,gBAAe,cAAa,QAAO,WAAU,YAAW,SAAQ,QAAO,SAAQ,aAAY,gBAAe,WAAU,UAAS,UAAS,WAAU,wBAAuB,WAAU,kBAAiB,oBAAmB,kBAAiB,mBAAkB,oBAAmB,cAAa,QAAO,WAAU,qBAAoB,mBAAkB,YAAW,YAAW,gBAAe,UAAS,UAAS,QAAO,YAAW,QAAO,WAAU,eAAc,YAAW,WAAU,WAAU,YAAW,SAAQ,OAAM,YAAW,oBAAmB,0BAAyB,wBAAuB,0BAAyB,0BAAyB,2BAA0B,2BAA0B,2BAA0B,yBAAwB,2BAA0B,4BAA2B,2BAA0B,2BAA0B,2BAA0B,yBAAwB,oBAAmB,aAAY,YAAW,WAAU,mBAAkB,kBAAiB,WAAU,QAAO,QAAO,SAAQ,QAAO,QAAO,aAAY,YAAW,QAAO,sBAAqB,YAAW,YAAW,UAAS,YAAW,YAAW,QAAO,UAAS,oBAAmB,UAAS,QAAO,UAAS,QAAO,QAAO,UAAS,aAAY,iBAAgB,YAAW,kBAAiB,cAAa,OAAM,QAAO,QAAO,UAAS,kBAAiB,mBAAkB,uBAAsB,YAAW,kBAAiB,YAAW,WAAU,WAAU,UAAS,eAAc,gBAAe,eAAc,gBAAe,SAAQ,UAAS,aAAY,UAAS,UAAS,mBAAkB,qBAAoB,WAAU,WAAU,YAAW,kBAAiB,YAAW,SAAQ,wBAAuB,uBAAsB,yBAAwB,aAAY,OAAM,SAAQ,UAAS,QAAO,SAAQ,WAAU,gBAAe,UAAS,mBAAkB,SAAQ,aAAY,WAAU,YAAW,SAAQ,WAAU,QAAO,SAAQ,eAAc,kBAAiB,eAAc,qBAAoB,eAAc,mBAAkB,eAAc,aAAY,OAAM,aAAY,SAAQ,UAAS,YAAW,qBAAoB,gBAAe,qBAAoB,uBAAsB,4BAA2B,UAAS,QAAO,YAAW,mBAAkB,YAAW,eAAc,UAAS,iBAAgB,OAAM,aAAY,aAAY,QAAO,YAAW,WAAU,YAAW,YAAW,aAAY,eAAc,kBAAiB,WAAU,iBAAgB,aAAY,QAAO,UAAS,eAAc,UAAS,aAAY,WAAU,WAAU,aAAY,eAAc,WAAU,SAAQ,cAAa,sBAAqB,iBAAgB,SAAQ,SAAQ,UAAS,WAAU,iBAAgB,WAAU,YAAW,WAAU,eAAc,WAAU,QAAO,UAAS,WAAU,eAAc,eAAc,gBAAe,WAAU,WAAU,YAAW,OAAM,YAAW,YAAW,eAAc,YAAW,eAAc,mBAAkB,SAAQ,aAAY,cAAa,6BAA4B,aAAY,UAAS,YAAW,UAAS,6BAA4B,6BAA4B,4BAA2B,YAAW,YAAW,SAAQ,WAAU,OAAM,QAAO,SAAQ,SAAQ,UAAS,YAAW,WAAU,WAAU,WAAU,SAAQ,cAAa,OAAM,UAAS,WAAU,YAAW,cAAa,SAAQ,WAAU,UAAS,UAAS,UAAS,UAAS,aAAY,mBAAkB,aAAY,eAAc,6BAA4B,0BAAyB,8BAA6B,kCAAiC,kBAAiB,iBAAgB,YAAW,SAAQ,QAAO,UAAS,uBAAsB,yBAAwB,UAAS,QAAO,SAAQ,SAAQ,oBAAmB,SAAQ,qBAAoB,mBAAkB,0BAAyB,wBAAuB,QAAO,SAAQ,cAAa,iBAAgB,WAAU,SAAQ,UAAS,eAAc,aAAY,cAAa,eAAc,SAAQ,aAAY,UAAS,iBAAgB,YAAW,SAAQ,UAAS,cAAa,WAAU,UAAS,OAAM,wBAAuB,SAAQ,aAAY,YAAW,WAAU,SAAQ,iBAAgB,cAAa,gBAAe,sBAAqB,sBAAqB,sBAAqB,aAAY,mBAAkB,SAAQ,UAAS,QAAO,eAAc,YAAW,YAAW,aAAY,QAAO,SAAQ,QAAO,oBAAmB,cAAa,mBAAkB,qBAAoB,gBAAe,WAAU,SAAQ,eAAc,uBAAsB,eAAc,uBAAsB,MAAK,OAAM,uBAAsB,yBAAwB,aAAY,eAAc,cAAa,cAAa,cAAa,eAAc,mBAAkB,kBAAiB,aAAY,MAAK,eAAc,kBAAiB,eAAc,qBAAoB,eAAc,mBAAkB,eAAc,aAAY,QAAO,OAAM,OAAM,YAAW,iBAAgB,WAAU,eAAc,kBAAiB,iBAAgB,UAAS,YAAW,QAAO,QAAO,SAAQ,UAAS,eAAc,cAAa,SAAQ,WAAU,WAAU,OAAM,YAAW,YAAW,WAAU,iBAAgB,aAAY,OAAM,eAAc,QAAO,gBAAe,kBAAiB,cAAa,YAAW,iBAAgB,gBAAgB,OAAO;AAE/8L,IAAI,wBAAwB,CAAC,MAAK,OAAM,MAAK,OAAM,UAAS,QAAO,MAAK,QAAO,WAAU,WAAW;AAApG,IACI,iBAAiB,CAAC,OAAM,MAAK,QAAO,UAAU,QAAQ,IAAI;AAD9D,IAEI,eAAe,CAAC,QAAO,QAAO,SAAQ,QAAO,SAAQ,QAAO,eAAc,YAAW,UAAU;AAFnG,IAGI,aAAa,CAAC,cAAc,cAAc,UAAU,aAAa,SAAS,SAAS,aAAa,UAAU,MAAM;AAEpH,IAAI,YAAY,aAAa;AAAA,EAAO;AAAA,EAAe;AAAA,EAAY;AAAA,EAC3B;AAAA,EAAkB;AAAA,EAClB;AAAA,EAAe;AAAA,EAAe;AAAA,EAC9B;AAAA,EAAsB;AAAA,EACtB;AAAA,EAAa;AAAU;AAE3D,SAAS,WAAW,OAAO;AACzB,UAAQ,MAAM,KAAK,SAAS,GAAE,GAAE;AAAC,WAAO,IAAI;AAAA,EAAE,CAAC;AAC/C,SAAO,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,IAAI,OAAO;AACvD;AAEA,SAAS,OAAO,OAAO;AACrB,MAAI,OAAO,CAAC;AACZ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,EAAG,MAAK,MAAM,CAAC,CAAC,IAAI;AACxD,SAAO;AACT;AAEA,SAAS,aAAa,MAAM;AAC1B,SAAO,KAAK,QAAQ,4BAA4B,MAAM;AACxD;AAEA,IAAI,cAAc,OAAO,YAAY;AAArC,IACI,qBAAqB;AADzB,IAEI,mBAAmB,OAAO,iBAAiB;AAF/C,IAGI,8BAA8B,OAAO,4BAA4B;AAHrE,IAII,gBAAgB,OAAO,cAAc;AAJzC,IAKI,gBAAgB,OAAO,cAAc;AALzC,IAMI,gBAAgB,OAAO,cAAc;AANzC,IAOI,sBAAsB,WAAW,cAAc;AAPnD,IAQI,gBAAgB,OAAO,cAAc;AARzC,IASI,aAAa,OAAO,WAAW;AATnC,IAUI,iBAAiB,OAAO,eAAe;AAV3C,IAWI,kBAAkB;AAXtB,IAYI,6BAA6B,WAAW,qBAAqB;AAZjE,IAaI,gBAAgB,OAAO,cAAc;AAbzC,IAcI,uBAAuB,IAAI,OAAO,wBAAwB;AAd9D,IAeI,cAAc,OAAO,YAAY;AAfrC,IAgBI,iBAAiB;AAhBrB,IAiBI,SAAS,CAAC;AAjBd,IAkBI;AAlBJ,IAmBI;AAnBJ,IAoBI;AApBJ,IAqBI;AAKJ,SAAS,UAAU,QAAQ,OAAO;AAChC,mBAAiB,OAAO,OAAO,MAAM,6HAA6H;AAClK,QAAM,QAAQ,KAAK,YAAY,iBAAiB,eAAe,CAAC,EAAE,QAAQ,QAAQ,EAAE,IAAI;AACxF,QAAM,QAAQ,KAAK,SAAS,OAAO,YAAY;AAC/C,OAAK,OAAO,KAAK;AAGjB,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,WAAO,UAAU;AACjB,WAAO,CAAC,WAAW,SAAS;AAAA,EAC9B;AAEA,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,UAAM,WAAW;AACjB,WAAO,cAAc,QAAQ,KAAK;AAAA,EACpC;AAEA,MAAI,MAAM,OAAQ,MAAM,KAAK;AAC3B,WAAO,KAAK;AACZ,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAEA,MAAI,MAAM,KAAK;AACb,WAAO,KAAK;AACZ,WAAO,SAAS,SAAS;AACzB,WAAO,CAAC,OAAO,OAAO,QAAQ,CAAC;AAAA,EACjC;AAEA,MAAI,MAAM,KAAK;AACb,WAAO,KAAK;AAEZ,QAAI,OAAO,MAAM,mDAAmD,GAAG;AACrE,aAAO,CAAC,QAAQ,MAAM;AAAA,IACxB;AAEA,QAAI,OAAO,MAAM,eAAe,GAAG;AACjC,aAAO,CAAC,WAAW,MAAM;AAAA,IAC3B;AAAA,EACF;AAEA,MAAI,OAAO,MAAM,oBAAoB,GAAG;AACtC,WAAO,CAAC,QAAQ,iBAAiB;AAAA,EACnC;AAEA,MAAI,OAAO,MAAM,mBAAmB,GAAG;AACrC,WAAO,SAAS,SAAS;AACzB,WAAO,CAAC,UAAU,MAAM;AAAA,EAC1B;AAEA,MAAI,MAAM,KAAK;AACb,WAAO,KAAK;AACZ,WAAO,CAAC,OAAO,MAAM,wBAAwB,IAAI,YAAW,YAAY,WAAW;AAAA,EACrF;AAEA,MAAI,MAAM,OAAO,OAAO,MAAM,iBAAiB,GAAG;AAChD,WAAO,CAAC,aAAa,WAAW;AAAA,EAClC;AAEA,MAAI,OAAO,MAAM,mBAAmB,GAAG;AACrC,QAAI,OAAO,KAAK,KAAK,IAAK,OAAM,WAAW;AAC3C,WAAO,CAAC,YAAY,MAAM;AAAA,EAC5B;AAEA,MAAI,OAAO,MAAM,iBAAiB,GAAG;AACnC,WAAO,OAAO,CAAC;AACf,WAAO,CAAC,WAAW,OAAO;AAAA,EAC5B;AAEA,MAAI,OAAO,MAAM,uBAAuB,GAAG;AACzC,WAAO,OAAO,CAAC;AACf,WAAO,CAAC,WAAW,aAAa;AAAA,EAClC;AAEA,MAAI,OAAO,OAAO,MAAM,OAAO,KAAK,OAAO,MAAM,mBAAmB,GAAG;AACrE,WAAO,CAAC,aAAa,WAAW;AAAA,EAClC;AAEA,MAAI,OAAO,MAAM,2BAA2B,GAAG;AAC7C,WAAO,OAAO,CAAC;AACf,WAAO,CAAC,wBAAwB,WAAW;AAAA,EAC7C;AACA,MAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,WAAO,CAAC,wBAAwB,WAAW;AAAA,EAC7C;AAEA,MAAI,OAAO,MAAM,0BAA0B,GAAG;AAC5C,WAAO,CAAC,YAAY,UAAU;AAAA,EAChC;AAEA,MAAI,OAAO,MAAM,2BAA2B,GAAG;AAE7C,QAAI,OAAO,MAAM,yBAAyB,KAAK,GAAG;AAChD,UAAI,CAAC,UAAU,OAAO,QAAQ,CAAC,GAAG;AAChC,eAAO,MAAM,GAAG;AAChB,eAAO,CAAC,YAAY,eAAe;AAAA,MACrC;AAAA,IACF;AACA,WAAO,CAAC,YAAY,MAAM;AAAA,EAC5B;AAEA,MAAI,OAAO,MAAM,eAAe,GAAG;AACjC,WAAO,CAAC,YAAY,OAAO,QAAQ,CAAC;AAAA,EACtC;AAEA,MAAI,kBAAkB,KAAK,EAAE,GAAG;AAC9B,WAAO,KAAK;AACZ,WAAO,CAAC,MAAM,EAAE;AAAA,EAClB;AAEA,SAAO,KAAK;AACZ,SAAO,CAAC,MAAM,IAAI;AACpB;AAKA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,WAAW,OAAOA;AACtB,UAAQA,MAAK,OAAO,KAAK,MAAM,MAAM;AACnC,QAAI,YAAYA,OAAM,KAAK;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAYA,OAAM;AAAA,EACpB;AACA,SAAO,CAAC,WAAW,SAAS;AAC9B;AAKA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAOA;AACrB,YAAQA,MAAK,OAAO,KAAK,MAAM,MAAM;AACnC,UAAIA,OAAM,SAAS,CAAC,SAAS;AAC3B,YAAI,SAAS,IAAK,QAAO,OAAO,CAAC;AACjC;AAAA,MACF;AACA,gBAAU,CAAC,WAAWA,OAAM;AAAA,IAC9B;AACA,QAAIA,OAAM,SAAS,CAAC,WAAW,SAAS,IAAK,OAAM,WAAW;AAC9D,WAAO,CAAC,UAAU,QAAQ;AAAA,EAC5B;AACF;AAKA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,SAAO,KAAK;AACZ,MAAI,CAAC,OAAO,MAAM,cAAc,KAAK;AACnC,UAAM,WAAW,YAAY,GAAG;AAAA;AAEhC,UAAM,WAAW;AACnB,SAAO,CAAC,MAAM,GAAG;AACnB;AAKA,SAAS,QAAQC,OAAM,QAAQ,MAAM,MAAM;AACzC,OAAK,OAAOA;AACZ,OAAK,SAAS;AACd,OAAK,OAAO;AACZ,OAAK,OAAO,QAAQ,EAAC,WAAW,IAAI,QAAQ,EAAC;AAC/C;AAEA,SAAS,YAAY,OAAO,QAAQA,OAAM,QAAQ;AAChD,WAAS,UAAU,IAAI,SAAS,OAAO;AACvC,QAAM,UAAU,IAAI,QAAQA,OAAM,OAAO,YAAY,IAAI,QAAQ,MAAM,OAAO;AAC9E,SAAOA;AACT;AAEA,SAAS,WAAW,OAAO,QAAQ,eAAe;AAChD,MAAI,gBAAgB,MAAM,QAAQ,SAAS,OAAO;AAClD,kBAAgB,iBAAiB;AACjC,QAAM,UAAU,MAAM,QAAQ;AAC9B,MAAI,cAAe,OAAM,QAAQ,SAAS;AAC1C,SAAO,MAAM,QAAQ;AACvB;AAEA,SAAS,KAAKA,OAAM,QAAQ,OAAO;AACjC,SAAO,OAAO,MAAM,QAAQ,IAAI,EAAEA,OAAM,QAAQ,KAAK;AACvD;AAEA,SAAS,WAAWA,OAAM,QAAQ,OAAO,GAAG;AAC1C,WAAS,IAAI,KAAK,GAAG,IAAI,GAAG;AAC1B,UAAM,UAAU,MAAM,QAAQ;AAChC,SAAO,KAAKA,OAAM,QAAQ,KAAK;AACjC;AAMA,SAAS,UAAU,MAAM;AACvB,SAAO,KAAK,YAAY,KAAK;AAC/B;AAEA,SAAS,eAAe,MAAM;AAC5B,SAAO,KAAK,YAAY;AACxB,SAAO,QAAQ,oBAAoB,QAAQ;AAC7C;AAEA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,YAAY,KAAK;AAC/B;AAEA,SAAS,mBAAmB,MAAM;AAChC,SAAO,KAAK,YAAY,EAAE,MAAM,oBAAoB;AACtD;AAEA,SAAS,YAAY,MAAM;AACzB,MAAI,SAAS,KAAK,YAAY;AAC9B,MAAIC,YAAW;AACf,MAAI,UAAU,IAAI,EAAG,CAAAA,YAAW;AAAA,WACvB,YAAY,IAAI,EAAG,CAAAA,YAAW;AAAA,WAC9B,eAAe,IAAI,EAAG,CAAAA,YAAW;AAAA,WACjC,UAAU,iBAAiB,UAAU,YAAa,CAAAA,YAAW;AAAA,WAC7D,UAAU,YAAY,UAAU,cAAe,CAAAA,YAAW;AAAA,WAG1D,KAAK,MAAM,QAAQ,EAAG,CAAAA,YAAW;AAC1C,SAAOA;AACT;AAEA,SAAS,YAAYD,OAAM,QAAQ;AACjC,SAAS,UAAU,MAAM,MAAMA,SAAQ,OAAOA,SAAQ,OAAOA,SAAQ,UAAUA,SAAQ,gBAAiBA,SAAQ;AAClH;AAEA,SAAS,oBAAoBA,OAAM,QAAQ;AACzC,SAAOA,SAAQ,OAAO,OAAO,MAAM,kBAAkB,KAAK;AAC5D;AAEA,SAAS,aAAaA,OAAM,QAAQ;AAClC,SAAOA,SAAQ,OAAO,OAAO,MAAM,YAAY,KAAK;AACtD;AAEA,SAAS,YAAY,QAAQ;AAC3B,SAAO,OAAO,IAAI,KAAK,OAAO,OAAO,MAAM,IAAI,OAAO,UAAU,aAAa,OAAO,QAAQ,CAAC,CAAC,CAAC;AACjG;AAEA,SAAS,UAAU,QAAQ;AACzB,SAAO,OAAO,IAAI,KAAK,OAAO,MAAM,SAAS,KAAK;AACpD;AAEA,SAAS,gBAAgB,MAAM;AAC7B,MAAI,KAAK;AACT,MAAI,SAAS,OAAO,QAAQ,WAAW,KAAK,MAAM,EAAE,IAAI,KAAK,OAAO,MAAM,EAAE;AAC5E,SAAO,SAAS,OAAO,CAAC,EAAE,QAAQ,QAAQ,EAAE,IAAI;AAClD;AAMA,OAAO,QAAQ,SAASA,OAAM,QAAQ,OAAO;AAC3C,MAAKA,SAAQ,aAAa,YAAY,MAAM,KACvCA,SAAQ,OAAO,UAAU,MAAM,KAChCA,SAAQ,SAAS;AACnB,WAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,EAC9C;AACA,MAAI,oBAAoBA,OAAM,MAAM,GAAG;AACrC,WAAO,YAAY,OAAO,QAAQ,eAAe;AAAA,EACnD;AACA,MAAI,UAAU,MAAM,KAAKA,SAAQ,KAAK;AACpC,QAAI,CAAC,uBAAuB,KAAK,OAAO,MAAM,KAAK,CAAC,UAAU,gBAAgB,MAAM,CAAC,GAAG;AACtF,aAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,IAC9C;AAAA,EACF;AACA,MAAI,YAAYA,OAAM,MAAM,GAAG;AAC7B,WAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,EAC3C;AACA,MAAIA,SAAQ,OAAO,UAAU,MAAM,GAAG;AACpC,WAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,EAC9C;AACA,MAAIA,SAAQ,iBAAiB;AAC3B,QAAI,OAAO,OAAO,MAAM,yBAAyB,KAAK,YAAY,gBAAgB,MAAM,CAAC,GAAG;AAC1F,aAAO,YAAY,OAAO,QAAQ,cAAc;AAAA,IAClD,OACK;AACH,aAAO,YAAY,OAAO,QAAQ,gBAAgB,CAAC;AAAA,IACrD;AAAA,EACF;AACA,MAAIA,SAAQ,KAAK;AACf,QAAI,CAAC,UAAU,MAAM,KAAK,CAAC,YAAY,gBAAgB,MAAM,CAAC,GAAG;AAC/D,aAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,IAC9C;AACA,WAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,EAC3C;AACA,MAAIA,SAAQ,KAAK;AACf,QAAI,UAAU,MAAM,KAAK,OAAO,MAAM,sBAAqB,KAAK,GAAG;AACjE,iBAAW;AACX,aAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,IAC3C;AAAA,EACF;AACA,MAAI,aAAaA,OAAM,MAAM,GAAG;AAC9B,WAAO,YAAY,OAAO,QAAQ,QAAQ;AAAA,EAC5C;AACA,MAAI,+CAA+C,KAAKA,KAAI,GAAG;AAC7D,WAAO,YAAY,OAAO,QAAQ,UAAU,MAAM,IAAI,UAAU,SAAS;AAAA,EAC3E;AACA,MAAI,oCAAoC,KAAKA,KAAI,GAAG;AAClD,WAAO,YAAY,OAAO,QAAQ,WAAW;AAAA,EAC/C;AACA,MAAI,YAAY,KAAKA,KAAI,GAAG;AAC1B,WAAO,YAAY,OAAO,QAAQ,UAAU,CAAC;AAAA,EAC/C;AACA,MAAIA,SAAQA,MAAK,OAAO,CAAC,KAAK,KAAK;AAGjC,QAAI,OAAO,YAAY,IAAI,KAAK,eAAe,OAAO,QAAQ,EAAE,MAAM,CAAC,CAAC,GAAG;AACzE,iBAAW;AACX,aAAO;AAAA,IACT;AACA,QAAI,8BAA8B,KAAKA,KAAI,GAAG;AAC5C,aAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,IAC9C;AACA,WAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,EAC3C;AACA,MAAIA,SAAQ,eAAe,UAAU,MAAM,GAAG;AAC5C,WAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,EAC3C;AACA,MAAIA,SAAQ,KAAK;AACf,WAAO,YAAY,OAAO,QAAQ,QAAQ;AAAA,EAC5C;AAEA,MAAIA,SAAQ,mBAAmB;AAC7B,WAAO,YAAY,OAAO,QAAQ,gBAAgB;AAAA,EACpD;AACA,MAAIA,SAAQ,QAAQ;AAClB,QAAI,OAAO,OAAO,QAAQ;AAC1B,eAAW,YAAY,IAAI;AAE3B,QAAI,YAAY,YAAY;AAC1B,UAAI,YAAY,MAAM,GAAG;AACvB,eAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,MAC9C,OAAO;AACL,mBAAW;AACX,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,YAAY,OAAO;AAGrB,UAAI,oCAAoC,KAAK,IAAI,GAAG;AAClD,YAAI,eAAe,gBAAgB,MAAM,CAAC,GAAG;AAC3C,qBAAW;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,UAAI,OAAO,OAAO,MAAM,IAAI,OAAO,YAAY,OAAO,MAAM,OAAM,SAAS,CAAC,GAAG;AAC7E,mBAAW;AACX,eAAO;AAAA,MACT;AAGA,UAAI,mBAAmB,KAAK,IAAI,GAAG;AACjC,YAAK,YAAY,MAAM,KAAK,OAAO,OAAO,MAAM,GAAG,KAC9C,CAAC,YAAY,MAAM,KACnB,CAAC,OAAO,OAAO,MAAM,0BAA0B,KAC/C,CAAC,UAAU,gBAAgB,MAAM,CAAC,GAAI;AACzC,qBAAW;AACX,cAAI,YAAY,gBAAgB,MAAM,CAAC,EAAI,QAAO;AAClD,iBAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,QAC9C;AAAA,MACF;AAEA,UAAI,UAAU,MAAM,EAAG,QAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,IAClE;AACA,QAAI,YAAY,iBAAiB;AAC/B,iBAAW;AAGX,UAAI,OAAO,QAAQ,aAAa,KAAK,CAAC,YAAY,MAAM,GAAG;AACzD,eAAO;AAAA,MACT;AACA,aAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,IAC3C;AACA,QAAI,QAAQ,SAAU,QAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAGlE,QAAI,YAAY,cAAc,OAAO,OAAO,MAAM,yBAAyB,GAAG;AAC5E,aAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,IAC3C;AAAA,EACF;AACA,SAAO,MAAM,QAAQ;AACvB;AAMA,OAAO,SAAS,SAASA,OAAM,QAAQ,OAAO;AAC5C,MAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,QAAQ;AAC3D,MAAIA,SAAQ,KAAK;AACf,QAAI,MAAM,QAAQ,KAAK,QAAQ,UAAU;AACvC,aAAO,WAAW,OAAO,MAAM;AAAA,IACjC;AACA,QAAK,OAAO,OAAO,MAAM,iBAAiB,KAAK,UAAU,MAAM,KAC3D,YAAY,gBAAgB,MAAM,CAAC,KACnC,6BAA6B,KAAK,gBAAgB,MAAM,CAAC,KACxD,CAAC,OAAO,OAAO,MAAM,8BAA8B,KACnD,UAAU,gBAAgB,MAAM,CAAC,GAAI;AACxC,aAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,IAC3C;AACA,QAAI,OAAO,OAAO,MAAM,kCAAkC,KACtD,OAAO,OAAO,MAAM,mBAAmB,KACvC,OAAO,OAAO,MAAM,oBAAoB,KACxC,OAAO,OAAO,MAAM,kBAAkB,GAAG;AAC3C,aAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,IAC9C;AACA,QAAI,UAAU,MAAM,EAAG,QAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,QAC3D,QAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,EACnD;AACA,MAAIA,SAAQA,MAAK,OAAO,CAAC,KAAK,OAAO,eAAe,OAAO,QAAQ,EAAE,MAAM,CAAC,CAAC,GAAG;AAC9E,eAAW;AAAA,EACb;AACA,MAAIA,SAAQ,QAAQ;AAClB,QAAI,OAAO,OAAO,QAAQ;AAC1B,eAAW,YAAY,IAAI;AAC3B,QAAI,YAAY,SAAS,mBAAmB,KAAK,IAAI,GAAG;AACtD,iBAAW;AAAA,IACb;AACA,QAAI,YAAY,cAAc,QAAQ,KAAM,YAAW;AAAA,EACzD;AACA,MAAIA,SAAQ,iBAAiB;AAC3B,WAAO,YAAY,OAAO,QAAQ,cAAc;AAAA,EAClD;AACA,MAAI,aAAaA,OAAM,MAAM,GAAG;AAC9B,WAAO,YAAY,OAAO,QAAQ,QAAQ;AAAA,EAC5C;AACA,SAAO,MAAM,QAAQ;AACvB;AAMA,OAAO,iBAAiB,SAASA,OAAM,QAAQ,OAAO;AACpD,MAAIA,SAAQ,QAAQ;AAClB,eAAW;AACX,WAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,EAC9C;AACA,SAAO,WAAW,OAAO,MAAM;AACjC;AAMA,OAAO,SAAS,SAASA,OAAM,QAAQ,OAAO;AAC5C,MAAI,CAAC,eAAe,gBAAgB,OAAO,MAAM,CAAC,GAAG;AACnD,WAAO,MAAM,UAAU;AACvB,eAAW;AACX,QAAI,UAAU,MAAM,EAAG,QAAO,YAAY,OAAO,QAAQ,OAAO;AAChE,WAAO,WAAW,OAAO,MAAM;AAAA,EACjC;AACA,SAAO,WAAWA,OAAM,QAAQ,KAAK;AACvC;AAMA,OAAO,UAAU,SAASA,OAAM,QAAQ,OAAO;AAC7C,MAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,gBAAgB;AACnE,MAAI,YAAYA,OAAM,MAAM,GAAG;AAC7B,WAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,EAC3C;AACA,MAAI,oBAAoBA,OAAM,MAAM,GAAG;AACrC,WAAO,YAAY,OAAO,QAAQ,eAAe;AAAA,EACnD;AACA,MAAIA,SAAQ,QAAQ;AAClB,QAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AACxC,QAAI,sBAAsB,KAAK,IAAI;AACjC,iBAAW;AAAA,aACJ,cAAc,eAAe,IAAI;AACxC,iBAAW;AAAA,aACJ,WAAW,eAAe,IAAI;AACrC,iBAAW;AAAA,aACJ,cAAc,eAAe,IAAI;AACxC,iBAAW;AAAA,aACJ,4BAA4B,eAAe,IAAI;AACtD,iBAAW;AAAA,QACR,YAAW,YAAY,OAAO,QAAQ,CAAC;AAC5C,QAAI,YAAY,SAAS,UAAU,MAAM,GAAG;AAC1C,aAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,IAC3C;AAAA,EACF;AACA,MAAIA,SAAQ,cAAc,iBAAiB,KAAK,OAAO,QAAQ,CAAC,GAAG;AACjE,eAAW;AAAA,EACb;AACA,SAAO,MAAM,QAAQ;AACvB;AAEA,OAAO,iBAAiB,SAASA,OAAM,QAAQ,OAAO;AACpD,MAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO,MAAM,QAAQ;AACrD,MAAIA,SAAQ,KAAK;AACf,QAAI,UAAU,MAAM,EAAG,QAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,QAC3D,QAAO,YAAY,OAAO,QAAQ,SAAS;AAAA,EAClD;AACA,MAAIA,SAAQ,QAAQ;AAClB,QAAI,OAAO,OAAO,QAAQ,EAAE,YAAY;AACxC,eAAW,YAAY,IAAI;AAC3B,QAAI,aAAa,KAAK,IAAI,EAAG,YAAW;AACxC,QAAI,YAAY,OAAO;AACrB,yBAAmB,KAAK,IAAI,IAAI,WAAW,aAAa,WAAW;AAAA,IACrE;AACA,WAAO,MAAM,QAAQ;AAAA,EACvB;AACA,SAAO,OAAO,QAAQA,OAAM,QAAQ,KAAK;AAC3C;AAMA,OAAO,YAAY,SAASA,OAAM,QAAQ,OAAO;AAC/C,MAAI,OAAO,YAAY,KAAK,QAASA,SAAQ,OAAO,YAAY,MAAM,KAAMA,SAAQ,OAAOA,SAAQ,UAC5DA,SAAQ,eAAe,UAAU,OAAO,QAAQ,CAAC,IAAI;AAC1F,WAAO,WAAWA,OAAM,QAAQ,KAAK;AAAA,EACvC;AACA,MAAIA,SAAQ,IAAK,QAAO,YAAY,OAAO,QAAQ,WAAW;AAC9D,MAAIA,SAAQ,KAAK;AACf,QAAI,YAAY,MAAM,EAAG,QAAO,WAAW,OAAO,QAAQ,IAAI;AAAA,QACzD,QAAO,YAAY,OAAO,QAAQ,WAAW;AAAA,EACpD;AACA,MAAIA,SAAQ,UAAU,aAAa,KAAK,OAAO,QAAQ,CAAC,GAAG;AACzD,WAAO,YAAY,OAAO,QAAQ,WAAW;AAAA,EAC/C;AACA,MAAIA,SAAQ,QAAQ;AAClB,eAAW,YAAY,OAAO,QAAQ,CAAC;AACvC,QAAI,YAAY,iBAAiB;AAC/B,iBAAW;AACX,aAAO,YAAY,OAAO,QAAQ,WAAW;AAAA,IAC/C;AAAA,EACF;AACA,MAAI,+CAA+C,KAAKA,KAAI,GAAG;AAC7D,WAAO,YAAY,OAAO,QAAQ,UAAU,MAAM,IAAI,UAAU,SAAS;AAAA,EAC3E;AACA,MAAIA,SAAQ,SAAS;AACnB,WAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,EAC9C;AACA,SAAO,MAAM,QAAQ;AACvB;AAMA,OAAO,gBAAgB,SAASA,OAAM,QAAQ,OAAO;AACnD,MAAIA,SAAQ,IAAK,YAAW,OAAO,MAAM,KAAK,YAAY,OAAO,QAAQ,OAAO;AAChF,MAAIA,SAAQ,KAAK;AACf,QAAI,OAAO,OAAO,MAAM,iCAAiC,KACpD,OAAO,OAAO,MAAM,YAAY,KAAK,UAAU,gBAAgB,MAAM,CAAC,GAAI;AAC7E,aAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,IAC3C;AACA,QAAI,CAAC,OAAO,OAAO,MAAM,aAAa,KAClC,OAAO,MAAM,YAAW,KAAK,GAAG;AAClC,aAAO,YAAY,OAAO,QAAQ,SAAS,CAAC;AAAA,IAC9C;AACA,WAAO,YAAY,OAAO,QAAQ,OAAO;AAAA,EAC3C;AACA,MAAIA,SAAQ,iBAAiB;AAC3B,WAAO,YAAY,OAAO,QAAQ,gBAAgB,CAAC;AAAA,EACrD;AACA,MAAIA,SAAQ,QAAQ;AAClB,eAAW,YAAY,OAAO,QAAQ,CAAC;AACvC,QAAI,YAAY,MAAO,YAAW;AAAA,EACpC;AACA,SAAO,MAAM,QAAQ;AACvB;AAMA,OAAO,SAAS,SAASA,OAAM,QAAQ,OAAO;AAC5C,MAAIA,SAAQ,OAAOA,SAAQ,IAAK,QAAO;AACvC,MAAIA,SAAQ,IAAK,QAAO,WAAW,OAAO,MAAM;AAChD,MAAIA,SAAQ,QAAQ;AAClB,eAAW,YAAY,OAAO,QAAQ,CAAC;AACvC,WAAO;AAAA,EACT;AACA,SAAO,WAAW,OAAO,MAAM;AACjC;AAMA,OAAO,eAAe,SAASA,OAAM,QAAQ,OAAO;AAClD,MAAIA,SAAQ,YAAYA,SAAQ,OAAOA,SAAQ,OAAO,OAAO,QAAQ,EAAE,MAAM,UAAU,GAAG;AACxF,QAAI,OAAO,QAAQ,EAAE,MAAM,YAAY,EAAG,YAAW;AACrD,WAAO;AAAA,EACT;AACA,SAAO,WAAWA,OAAM,QAAQ,KAAK;AACvC;AAEO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,OAAO;AAAA,MACP,SAAS,IAAI,QAAQ,SAAS,GAAG,IAAI;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,CAAC,MAAM,YAAY,OAAO,SAAS,EAAG,QAAO;AACjD,aAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACnD,QAAI,SAAS,OAAO,SAAS,UAAU;AACrC,aAAO,MAAM,CAAC;AACd,cAAQ,MAAM,CAAC;AAAA,IACjB;AACA,eAAW;AACX,UAAM,QAAQ,OAAO,MAAM,KAAK,EAAE,MAAM,QAAQ,KAAK;AACrD,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS,OAAO,WAAW,KAAK;AACtC,QAAI,KAAK,MAAM,SACXD,MAAK,aAAa,UAAU,OAAO,CAAC,GACpC,SAAS,GAAG,QACZ,gBAAgB,gBAAgB,SAAS,GACzC,aAAa,GAAG,KAAK,QACrB,oBAAoB,MAAM,QAAQ,OAAO,MAAM,QAAQ,KAAK,KAAK,YAAY,IAC7E,iBAAiB,MAAM,QAAQ,OAAO,MAAM,QAAQ,KAAK,KAAK,SAAS;AAE3E,QAAI,GAAG,SACFA,OAAM,QAAQ,GAAG,QAAQ,WAAW,GAAG,QAAQ,aAAa,GAAG,QAAQ,gBACvEA,OAAM,QAAQ,GAAG,QAAQ,YAAY,GAAG,QAAQ,qBAChDA,OAAM,OAAQ,GAAG,QAAQ,OAAQ;AACpC,eAAS,GAAG,SAAS,IAAI;AAAA,IAC3B,WAAW,CAAE,OAAO,KAAKA,GAAE,GAAI;AAC7B,UAAI,UAAU,KAAKA,GAAE,KACjB,MAAM,KAAK,SAAS,KAC9B,gBAAgB,KAAK,SAAS,KACpB,WAAW,KAAK,iBAAiB,KACjC,sCAAsC,KAAK,SAAS,KACpD,yBAAyB,KAAK,SAAS,KACvC,UAAU,KAAK,SAAS,KACxB,YAAY,aAAa,GAAG;AAC9B,iBAAS;AAAA,MACX,WAAW,6BAA6B,KAAKA,GAAE,KAAK,UAAU,aAAa,GAAG;AAC5E,YAAI,SAAS,KAAK,iBAAiB,GAAG;AACpC,mBAAS;AAAA,QACX,WAAW,6BAA6B,KAAK,iBAAiB,KAAK,UAAU,iBAAiB,GAAG;AAC/F,mBAAS,cAAc,iBAAiB,iBAAiB,iBAAiB,IAAI;AAAA,QAChF,OAAO;AACL,mBAAS;AAAA,QACX;AAAA,MACF,WAAW,CAAC,QAAQ,KAAK,SAAS,MAAM,mBAAmB,aAAa,KAAK,eAAe,aAAa,IAAI;AAC3G,YAAI,YAAY,iBAAiB,GAAG;AAClC,mBAAS,cAAc,iBAAiB,iBAAiB,iBAAiB,IAAI;AAAA,QAChF,WAAW,MAAM,KAAK,iBAAiB,GAAG;AACxC,mBAAS,cAAc,iBAAiB,aAAa,iBAAiB,IAAI;AAAA,QAC5E,WAAW,mBAAmB,iBAAiB,KAAK,eAAe,iBAAiB,GAAG;AACrF,mBAAS,cAAc,iBAAiB,iBAAiB;AAAA,QAC3D,WAAW,mCAAmC,KAAK,iBAAiB,KACzD,QAAQ,KAAK,iBAAiB,KAC9B,UAAU,iBAAiB,KAC3B,qBAAqB,KAAK,iBAAiB,GAAG;AACvD,mBAAS,iBAAiB,IAAI;AAAA,QAChC,OAAO;AACL,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,IAC5D,cAAc;AAAA,EAChB;AACF;", "names": ["ch", "type", "override"]}