{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/sieve.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar keywords = words(\"if elsif else stop require\");\nvar atoms = words(\"true false not\");\n\nfunction tokenBase(stream, state) {\n\n  var ch = stream.next();\n  if (ch == \"/\" && stream.eat(\"*\")) {\n    state.tokenize = tokenCComment;\n    return tokenCComment(stream, state);\n  }\n\n  if (ch === '#') {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  if (ch == \"\\\"\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n\n  if (ch == \"(\") {\n    state._indent.push(\"(\");\n    // add virtual angel wings so that editor behaves...\n    // ...more sane incase of broken brackets\n    state._indent.push(\"{\");\n    return null;\n  }\n\n  if (ch === \"{\") {\n    state._indent.push(\"{\");\n    return null;\n  }\n\n  if (ch == \")\")  {\n    state._indent.pop();\n    state._indent.pop();\n  }\n\n  if (ch === \"}\") {\n    state._indent.pop();\n    return null;\n  }\n\n  if (ch == \",\")\n    return null;\n\n  if (ch == \";\")\n    return null;\n\n\n  if (/[{}\\(\\),;]/.test(ch))\n    return null;\n\n  // 1*DIGIT \"K\" / \"M\" / \"G\"\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\d]/);\n    stream.eat(/[KkMmGg]/);\n    return \"number\";\n  }\n\n  // \":\" (ALPHA / \"_\") *(ALPHA / DIGIT / \"_\")\n  if (ch == \":\") {\n    stream.eatWhile(/[a-zA-Z_]/);\n    stream.eatWhile(/[a-zA-Z0-9_]/);\n\n    return \"operator\";\n  }\n\n  stream.eatWhile(/\\w/);\n  var cur = stream.current();\n\n  // \"text:\" *(SP / HTAB) (hash-comment / CRLF)\n  // *(multiline-literal / multiline-dotstart)\n  // \".\" CRLF\n  if ((cur == \"text\") && stream.eat(\":\"))\n  {\n    state.tokenize = tokenMultiLineString;\n    return \"string\";\n  }\n\n  if (keywords.propertyIsEnumerable(cur))\n    return \"keyword\";\n\n  if (atoms.propertyIsEnumerable(cur))\n    return \"atom\";\n\n  return null;\n}\n\nfunction tokenMultiLineString(stream, state)\n{\n  state._multiLineString = true;\n  // the first line is special it may contain a comment\n  if (!stream.sol()) {\n    stream.eatSpace();\n\n    if (stream.peek() == \"#\") {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n\n    stream.skipToEnd();\n    return \"string\";\n  }\n\n  if ((stream.next() == \".\")  && (stream.eol()))\n  {\n    state._multiLineString = false;\n    state.tokenize = tokenBase;\n  }\n\n  return \"string\";\n}\n\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = tokenBase;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped)\n        break;\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    if (!escaped) state.tokenize = tokenBase;\n    return \"string\";\n  };\n}\n\nexport const sieve = {\n  name: \"sieve\",\n  startState: function(base) {\n    return {tokenize: tokenBase,\n            baseIndent: base || 0,\n            _indent: []};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace())\n      return null;\n\n    return (state.tokenize || tokenBase)(stream, state);\n  },\n\n  indent: function(state, _textAfter, cx) {\n    var length = state._indent.length;\n    if (_textAfter && (_textAfter[0] == \"}\"))\n      length--;\n\n    if (length <0)\n      length = 0;\n\n    return length * cx.unit;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*\\}$/\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,MAAM,KAAK;AAClB,MAAI,MAAM,CAAC,GAAGA,SAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE,EAAG,KAAIA,OAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AAEA,IAAI,WAAW,MAAM,4BAA4B;AACjD,IAAI,QAAQ,MAAM,gBAAgB;AAElC,SAAS,UAAU,QAAQ,OAAO;AAEhC,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAChC,UAAM,WAAW;AACjB,WAAO,cAAc,QAAQ,KAAK;AAAA,EACpC;AAEA,MAAI,OAAO,KAAK;AACd,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAM;AACd,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAEA,MAAI,MAAM,KAAK;AACb,UAAM,QAAQ,KAAK,GAAG;AAGtB,UAAM,QAAQ,KAAK,GAAG;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,KAAK;AACd,UAAM,QAAQ,KAAK,GAAG;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,KAAM;AACd,UAAM,QAAQ,IAAI;AAClB,UAAM,QAAQ,IAAI;AAAA,EACpB;AAEA,MAAI,OAAO,KAAK;AACd,UAAM,QAAQ,IAAI;AAClB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM;AACR,WAAO;AAET,MAAI,MAAM;AACR,WAAO;AAGT,MAAI,aAAa,KAAK,EAAE;AACtB,WAAO;AAGT,MAAI,KAAK,KAAK,EAAE,GAAG;AACjB,WAAO,SAAS,MAAM;AACtB,WAAO,IAAI,UAAU;AACrB,WAAO;AAAA,EACT;AAGA,MAAI,MAAM,KAAK;AACb,WAAO,SAAS,WAAW;AAC3B,WAAO,SAAS,cAAc;AAE9B,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,IAAI;AACpB,MAAI,MAAM,OAAO,QAAQ;AAKzB,MAAK,OAAO,UAAW,OAAO,IAAI,GAAG,GACrC;AACE,UAAM,WAAW;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,qBAAqB,GAAG;AACnC,WAAO;AAET,MAAI,MAAM,qBAAqB,GAAG;AAChC,WAAO;AAET,SAAO;AACT;AAEA,SAAS,qBAAqB,QAAQ,OACtC;AACE,QAAM,mBAAmB;AAEzB,MAAI,CAAC,OAAO,IAAI,GAAG;AACjB,WAAO,SAAS;AAEhB,QAAI,OAAO,KAAK,KAAK,KAAK;AACxB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAK,OAAO,KAAK,KAAK,OAAU,OAAO,IAAI,GAC3C;AACE,UAAM,mBAAmB;AACzB,UAAM,WAAW;AAAA,EACnB;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,QAAQ,OAAO;AACpC,MAAI,WAAW,OAAO;AACtB,UAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,QAAI,YAAY,MAAM,KAAK;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO;AACrB,YAAQ,KAAK,OAAO,KAAK,MAAM,MAAM;AACnC,UAAI,MAAM,SAAS,CAAC;AAClB;AACF,gBAAU,CAAC,WAAW,MAAM;AAAA,IAC9B;AACA,QAAI,CAAC,QAAS,OAAM,WAAW;AAC/B,WAAO;AAAA,EACT;AACF;AAEO,IAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,YAAY,SAAS,MAAM;AACzB,WAAO;AAAA,MAAC,UAAU;AAAA,MACV,YAAY,QAAQ;AAAA,MACpB,SAAS,CAAC;AAAA,IAAC;AAAA,EACrB;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS;AAClB,aAAO;AAET,YAAQ,MAAM,YAAY,WAAW,QAAQ,KAAK;AAAA,EACpD;AAAA,EAEA,QAAQ,SAAS,OAAO,YAAY,IAAI;AACtC,QAAI,SAAS,MAAM,QAAQ;AAC3B,QAAI,cAAe,WAAW,CAAC,KAAK;AAClC;AAEF,QAAI,SAAQ;AACV,eAAS;AAEX,WAAO,SAAS,GAAG;AAAA,EACrB;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,EACjB;AACF;", "names": ["words"]}