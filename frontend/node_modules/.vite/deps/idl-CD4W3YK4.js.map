{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/idl.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp('^((' + words.join(')|(') + '))\\\\b', 'i');\n};\n\nvar builtinArray = [\n  'a_correlate', 'abs', 'acos', 'adapt_hist_equal', 'alog',\n  'alog2', 'alog10', 'amoeba', 'annotate', 'app_user_dir',\n  'app_user_dir_query', 'arg_present', 'array_equal', 'array_indices',\n  'arrow', 'ascii_template', 'asin', 'assoc', 'atan',\n  'axis', 'axis', 'bandpass_filter', 'bandreject_filter', 'barplot',\n  'bar_plot', 'beseli', 'beselj', 'beselk', 'besely',\n  'beta', 'biginteger', 'bilinear', 'bin_date', 'binary_template',\n  'bindgen', 'binomial', 'bit_ffs', 'bit_population', 'blas_axpy',\n  'blk_con', 'boolarr', 'boolean', 'boxplot', 'box_cursor',\n  'breakpoint', 'broyden', 'bubbleplot', 'butterworth', 'bytarr',\n  'byte', 'byteorder', 'bytscl', 'c_correlate', 'calendar',\n  'caldat', 'call_external', 'call_function', 'call_method',\n  'call_procedure', 'canny', 'catch', 'cd', 'cdf', 'ceil',\n  'chebyshev', 'check_math', 'chisqr_cvf', 'chisqr_pdf', 'choldc',\n  'cholsol', 'cindgen', 'cir_3pnt', 'clipboard', 'close',\n  'clust_wts', 'cluster', 'cluster_tree', 'cmyk_convert', 'code_coverage',\n  'color_convert', 'color_exchange', 'color_quan', 'color_range_map',\n  'colorbar', 'colorize_sample', 'colormap_applicable',\n  'colormap_gradient', 'colormap_rotation', 'colortable',\n  'comfit', 'command_line_args', 'common', 'compile_opt', 'complex',\n  'complexarr', 'complexround', 'compute_mesh_normals', 'cond', 'congrid',\n  'conj', 'constrained_min', 'contour', 'contour', 'convert_coord',\n  'convol', 'convol_fft', 'coord2to3', 'copy_lun', 'correlate',\n  'cos', 'cosh', 'cpu', 'cramer', 'createboxplotdata',\n  'create_cursor', 'create_struct', 'create_view', 'crossp', 'crvlength',\n  'ct_luminance', 'cti_test', 'cursor', 'curvefit', 'cv_coord',\n  'cvttobm', 'cw_animate', 'cw_animate_getp', 'cw_animate_load',\n  'cw_animate_run', 'cw_arcball', 'cw_bgroup', 'cw_clr_index',\n  'cw_colorsel', 'cw_defroi', 'cw_field', 'cw_filesel', 'cw_form',\n  'cw_fslider', 'cw_light_editor', 'cw_light_editor_get',\n  'cw_light_editor_set', 'cw_orient', 'cw_palette_editor',\n  'cw_palette_editor_get', 'cw_palette_editor_set', 'cw_pdmenu',\n  'cw_rgbslider', 'cw_tmpl', 'cw_zoom', 'db_exists',\n  'dblarr', 'dcindgen', 'dcomplex', 'dcomplexarr', 'define_key',\n  'define_msgblk', 'define_msgblk_from_file', 'defroi', 'defsysv',\n  'delvar', 'dendro_plot', 'dendrogram', 'deriv', 'derivsig',\n  'determ', 'device', 'dfpmin', 'diag_matrix', 'dialog_dbconnect',\n  'dialog_message', 'dialog_pickfile', 'dialog_printersetup',\n  'dialog_printjob', 'dialog_read_image',\n  'dialog_write_image', 'dictionary', 'digital_filter', 'dilate', 'dindgen',\n  'dissolve', 'dist', 'distance_measure', 'dlm_load', 'dlm_register',\n  'doc_library', 'double', 'draw_roi', 'edge_dog', 'efont',\n  'eigenql', 'eigenvec', 'ellipse', 'elmhes', 'emboss',\n  'empty', 'enable_sysrtn', 'eof', 'eos', 'erase',\n  'erf', 'erfc', 'erfcx', 'erode', 'errorplot',\n  'errplot', 'estimator_filter', 'execute', 'exit', 'exp',\n  'expand', 'expand_path', 'expint', 'extract', 'extract_slice',\n  'f_cvf', 'f_pdf', 'factorial', 'fft', 'file_basename',\n  'file_chmod', 'file_copy', 'file_delete', 'file_dirname',\n  'file_expand_path', 'file_gunzip', 'file_gzip', 'file_info',\n  'file_lines', 'file_link', 'file_mkdir', 'file_move',\n  'file_poll_input', 'file_readlink', 'file_same',\n  'file_search', 'file_tar', 'file_test', 'file_untar', 'file_unzip',\n  'file_which', 'file_zip', 'filepath', 'findgen', 'finite',\n  'fix', 'flick', 'float', 'floor', 'flow3',\n  'fltarr', 'flush', 'format_axis_values', 'forward_function', 'free_lun',\n  'fstat', 'fulstr', 'funct', 'function', 'fv_test',\n  'fx_root', 'fz_roots', 'gamma', 'gamma_ct', 'gauss_cvf',\n  'gauss_pdf', 'gauss_smooth', 'gauss2dfit', 'gaussfit',\n  'gaussian_function', 'gaussint', 'get_drive_list', 'get_dxf_objects',\n  'get_kbrd', 'get_login_info',\n  'get_lun', 'get_screen_size', 'getenv', 'getwindows', 'greg2jul',\n  'grib', 'grid_input', 'grid_tps', 'grid3', 'griddata',\n  'gs_iter', 'h_eq_ct', 'h_eq_int', 'hanning', 'hash',\n  'hdf', 'hdf5', 'heap_free', 'heap_gc', 'heap_nosave',\n  'heap_refcount', 'heap_save', 'help', 'hilbert', 'hist_2d',\n  'hist_equal', 'histogram', 'hls', 'hough', 'hqr',\n  'hsv', 'i18n_multibytetoutf8',\n  'i18n_multibytetowidechar', 'i18n_utf8tomultibyte',\n  'i18n_widechartomultibyte',\n  'ibeta', 'icontour', 'iconvertcoord', 'idelete', 'identity',\n  'idl_base64', 'idl_container', 'idl_validname',\n  'idlexbr_assistant', 'idlitsys_createtool',\n  'idlunit', 'iellipse', 'igamma', 'igetcurrent', 'igetdata',\n  'igetid', 'igetproperty', 'iimage', 'image', 'image_cont',\n  'image_statistics', 'image_threshold', 'imaginary', 'imap', 'indgen',\n  'int_2d', 'int_3d', 'int_tabulated', 'intarr', 'interpol',\n  'interpolate', 'interval_volume', 'invert', 'ioctl', 'iopen',\n  'ir_filter', 'iplot', 'ipolygon', 'ipolyline', 'iputdata',\n  'iregister', 'ireset', 'iresolve', 'irotate', 'isa',\n  'isave', 'iscale', 'isetcurrent', 'isetproperty', 'ishft',\n  'isocontour', 'isosurface', 'isurface', 'itext', 'itranslate',\n  'ivector', 'ivolume', 'izoom', 'journal', 'json_parse',\n  'json_serialize', 'jul2greg', 'julday', 'keyword_set', 'krig2d',\n  'kurtosis', 'kw_test', 'l64indgen', 'la_choldc', 'la_cholmprove',\n  'la_cholsol', 'la_determ', 'la_eigenproblem', 'la_eigenql', 'la_eigenvec',\n  'la_elmhes', 'la_gm_linear_model', 'la_hqr', 'la_invert',\n  'la_least_square_equality', 'la_least_squares', 'la_linear_equation',\n  'la_ludc', 'la_lumprove', 'la_lusol',\n  'la_svd', 'la_tridc', 'la_trimprove', 'la_triql', 'la_trired',\n  'la_trisol', 'label_date', 'label_region', 'ladfit', 'laguerre',\n  'lambda', 'lambdap', 'lambertw', 'laplacian', 'least_squares_filter',\n  'leefilt', 'legend', 'legendre', 'linbcg', 'lindgen',\n  'linfit', 'linkimage', 'list', 'll_arc_distance', 'lmfit',\n  'lmgr', 'lngamma', 'lnp_test', 'loadct', 'locale_get',\n  'logical_and', 'logical_or', 'logical_true', 'lon64arr', 'lonarr',\n  'long', 'long64', 'lsode', 'lu_complex', 'ludc',\n  'lumprove', 'lusol', 'm_correlate', 'machar', 'make_array',\n  'make_dll', 'make_rt', 'map', 'mapcontinents', 'mapgrid',\n  'map_2points', 'map_continents', 'map_grid', 'map_image', 'map_patch',\n  'map_proj_forward', 'map_proj_image', 'map_proj_info',\n  'map_proj_init', 'map_proj_inverse',\n  'map_set', 'matrix_multiply', 'matrix_power', 'max', 'md_test',\n  'mean', 'meanabsdev', 'mean_filter', 'median', 'memory',\n  'mesh_clip', 'mesh_decimate', 'mesh_issolid',\n  'mesh_merge', 'mesh_numtriangles',\n  'mesh_obj', 'mesh_smooth', 'mesh_surfacearea',\n  'mesh_validate', 'mesh_volume',\n  'message', 'min', 'min_curve_surf', 'mk_html_help', 'modifyct',\n  'moment', 'morph_close', 'morph_distance',\n  'morph_gradient', 'morph_hitormiss',\n  'morph_open', 'morph_thin', 'morph_tophat', 'multi', 'n_elements',\n  'n_params', 'n_tags', 'ncdf', 'newton', 'noise_hurl',\n  'noise_pick', 'noise_scatter', 'noise_slur', 'norm', 'obj_class',\n  'obj_destroy', 'obj_hasmethod', 'obj_isa', 'obj_new', 'obj_valid',\n  'objarr', 'on_error', 'on_ioerror', 'online_help', 'openr',\n  'openu', 'openw', 'oplot', 'oploterr', 'orderedhash',\n  'p_correlate', 'parse_url', 'particle_trace', 'path_cache', 'path_sep',\n  'pcomp', 'plot', 'plot3d', 'plot', 'plot_3dbox',\n  'plot_field', 'ploterr', 'plots', 'polar_contour', 'polar_surface',\n  'polyfill', 'polyshade', 'pnt_line', 'point_lun', 'polarplot',\n  'poly', 'poly_2d', 'poly_area', 'poly_fit', 'polyfillv',\n  'polygon', 'polyline', 'polywarp', 'popd', 'powell',\n  'pref_commit', 'pref_get', 'pref_set', 'prewitt', 'primes',\n  'print', 'printf', 'printd', 'pro', 'product',\n  'profile', 'profiler', 'profiles', 'project_vol', 'ps_show_fonts',\n  'psafm', 'pseudo', 'ptr_free', 'ptr_new', 'ptr_valid',\n  'ptrarr', 'pushd', 'qgrid3', 'qhull', 'qromb',\n  'qromo', 'qsimp', 'query_*', 'query_ascii', 'query_bmp',\n  'query_csv', 'query_dicom', 'query_gif', 'query_image', 'query_jpeg',\n  'query_jpeg2000', 'query_mrsid', 'query_pict', 'query_png', 'query_ppm',\n  'query_srf', 'query_tiff', 'query_video', 'query_wav', 'r_correlate',\n  'r_test', 'radon', 'randomn', 'randomu', 'ranks',\n  'rdpix', 'read', 'readf', 'read_ascii', 'read_binary',\n  'read_bmp', 'read_csv', 'read_dicom', 'read_gif', 'read_image',\n  'read_interfile', 'read_jpeg', 'read_jpeg2000', 'read_mrsid', 'read_pict',\n  'read_png', 'read_ppm', 'read_spr', 'read_srf', 'read_sylk',\n  'read_tiff', 'read_video', 'read_wav', 'read_wave', 'read_x11_bitmap',\n  'read_xwd', 'reads', 'readu', 'real_part', 'rebin',\n  'recall_commands', 'recon3', 'reduce_colors', 'reform', 'region_grow',\n  'register_cursor', 'regress', 'replicate',\n  'replicate_inplace', 'resolve_all',\n  'resolve_routine', 'restore', 'retall', 'return', 'reverse',\n  'rk4', 'roberts', 'rot', 'rotate', 'round',\n  'routine_filepath', 'routine_info', 'rs_test', 's_test', 'save',\n  'savgol', 'scale3', 'scale3d', 'scatterplot', 'scatterplot3d',\n  'scope_level', 'scope_traceback', 'scope_varfetch',\n  'scope_varname', 'search2d',\n  'search3d', 'sem_create', 'sem_delete', 'sem_lock', 'sem_release',\n  'set_plot', 'set_shading', 'setenv', 'sfit', 'shade_surf',\n  'shade_surf_irr', 'shade_volume', 'shift', 'shift_diff', 'shmdebug',\n  'shmmap', 'shmunmap', 'shmvar', 'show3', 'showfont',\n  'signum', 'simplex', 'sin', 'sindgen', 'sinh',\n  'size', 'skewness', 'skip_lun', 'slicer3', 'slide_image',\n  'smooth', 'sobel', 'socket', 'sort', 'spawn',\n  'sph_4pnt', 'sph_scat', 'spher_harm', 'spl_init', 'spl_interp',\n  'spline', 'spline_p', 'sprsab', 'sprsax', 'sprsin',\n  'sprstp', 'sqrt', 'standardize', 'stddev', 'stop',\n  'strarr', 'strcmp', 'strcompress', 'streamline', 'streamline',\n  'stregex', 'stretch', 'string', 'strjoin', 'strlen',\n  'strlowcase', 'strmatch', 'strmessage', 'strmid', 'strpos',\n  'strput', 'strsplit', 'strtrim', 'struct_assign', 'struct_hide',\n  'strupcase', 'surface', 'surface', 'surfr', 'svdc',\n  'svdfit', 'svsol', 'swap_endian', 'swap_endian_inplace', 'symbol',\n  'systime', 't_cvf', 't_pdf', 't3d', 'tag_names',\n  'tan', 'tanh', 'tek_color', 'temporary', 'terminal_size',\n  'tetra_clip', 'tetra_surface', 'tetra_volume', 'text', 'thin',\n  'thread', 'threed', 'tic', 'time_test2', 'timegen',\n  'timer', 'timestamp', 'timestamptovalues', 'tm_test', 'toc',\n  'total', 'trace', 'transpose', 'tri_surf', 'triangulate',\n  'trigrid', 'triql', 'trired', 'trisol', 'truncate_lun',\n  'ts_coef', 'ts_diff', 'ts_fcast', 'ts_smooth', 'tv',\n  'tvcrs', 'tvlct', 'tvrd', 'tvscl', 'typename',\n  'uindgen', 'uint', 'uintarr', 'ul64indgen', 'ulindgen',\n  'ulon64arr', 'ulonarr', 'ulong', 'ulong64', 'uniq',\n  'unsharp_mask', 'usersym', 'value_locate', 'variance', 'vector',\n  'vector_field', 'vel', 'velovect', 'vert_t3d', 'voigt',\n  'volume', 'voronoi', 'voxel_proj', 'wait', 'warp_tri',\n  'watershed', 'wdelete', 'wf_draw', 'where', 'widget_base',\n  'widget_button', 'widget_combobox', 'widget_control',\n  'widget_displaycontextmenu', 'widget_draw',\n  'widget_droplist', 'widget_event', 'widget_info',\n  'widget_label', 'widget_list',\n  'widget_propertysheet', 'widget_slider', 'widget_tab',\n  'widget_table', 'widget_text',\n  'widget_tree', 'widget_tree_move', 'widget_window',\n  'wiener_filter', 'window',\n  'window', 'write_bmp', 'write_csv', 'write_gif', 'write_image',\n  'write_jpeg', 'write_jpeg2000', 'write_nrif', 'write_pict', 'write_png',\n  'write_ppm', 'write_spr', 'write_srf', 'write_sylk', 'write_tiff',\n  'write_video', 'write_wav', 'write_wave', 'writeu', 'wset',\n  'wshow', 'wtn', 'wv_applet', 'wv_cwt', 'wv_cw_wavelet',\n  'wv_denoise', 'wv_dwt', 'wv_fn_coiflet',\n  'wv_fn_daubechies', 'wv_fn_gaussian',\n  'wv_fn_haar', 'wv_fn_morlet', 'wv_fn_paul',\n  'wv_fn_symlet', 'wv_import_data',\n  'wv_import_wavelet', 'wv_plot3d_wps', 'wv_plot_multires',\n  'wv_pwt', 'wv_tool_denoise',\n  'xbm_edit', 'xdisplayfile', 'xdxf', 'xfont', 'xinteranimate',\n  'xloadct', 'xmanager', 'xmng_tmpl', 'xmtool', 'xobjview',\n  'xobjview_rotate', 'xobjview_write_image',\n  'xpalette', 'xpcolor', 'xplot3d',\n  'xregistered', 'xroi', 'xsq_test', 'xsurface', 'xvaredit',\n  'xvolume', 'xvolume_rotate', 'xvolume_write_image',\n  'xyouts', 'zlib_compress', 'zlib_uncompress', 'zoom', 'zoom_24'\n];\nvar builtins = wordRegexp(builtinArray);\n\nvar keywordArray = [\n  'begin', 'end', 'endcase', 'endfor',\n  'endwhile', 'endif', 'endrep', 'endforeach',\n  'break', 'case', 'continue', 'for',\n  'foreach', 'goto', 'if', 'then', 'else',\n  'repeat', 'until', 'switch', 'while',\n  'do', 'pro', 'function'\n];\nvar keywords = wordRegexp(keywordArray);\n\nvar identifiers = new RegExp('^[_a-z\\xa1-\\uffff][_a-z0-9\\xa1-\\uffff]*', 'i');\n\nvar singleOperators = /[+\\-*&=<>\\/@#~$]/;\nvar boolOperators = new RegExp('(and|or|eq|lt|le|gt|ge|ne|not)', 'i');\n\nfunction tokenBase(stream) {\n  // whitespaces\n  if (stream.eatSpace()) return null;\n\n  // Handle one line Comments\n  if (stream.match(';')) {\n    stream.skipToEnd();\n    return 'comment';\n  }\n\n  // Handle Number Literals\n  if (stream.match(/^[0-9\\.+-]/, false)) {\n    if (stream.match(/^[+-]?0x[0-9a-fA-F]+/))\n      return 'number';\n    if (stream.match(/^[+-]?\\d*\\.\\d+([EeDd][+-]?\\d+)?/))\n      return 'number';\n    if (stream.match(/^[+-]?\\d+([EeDd][+-]?\\d+)?/))\n      return 'number';\n  }\n\n  // Handle Strings\n  if (stream.match(/^\"([^\"]|(\"\"))*\"/)) { return 'string'; }\n  if (stream.match(/^'([^']|(''))*'/)) { return 'string'; }\n\n  // Handle words\n  if (stream.match(keywords)) { return 'keyword'; }\n  if (stream.match(builtins)) { return 'builtin'; }\n  if (stream.match(identifiers)) { return 'variable'; }\n\n  if (stream.match(singleOperators) || stream.match(boolOperators)) {\n    return 'operator'; }\n\n  // Handle non-detected items\n  stream.next();\n  return null;\n};\n\nexport const idl = {\n  name: \"idl\",\n  token: function(stream) {\n    return tokenBase(stream);\n  },\n  languageData: {\n    autocomplete: builtinArray.concat(keywordArray)\n  }\n}\n"], "mappings": ";;;AAAA,SAAS,WAAW,OAAO;AACzB,SAAO,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,IAAI,SAAS,GAAG;AAC5D;AAEA,IAAI,eAAe;AAAA,EACjB;AAAA,EAAe;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAoB;AAAA,EAClD;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAY;AAAA,EACzC;AAAA,EAAsB;AAAA,EAAe;AAAA,EAAe;AAAA,EACpD;AAAA,EAAS;AAAA,EAAkB;AAAA,EAAQ;AAAA,EAAS;AAAA,EAC5C;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAmB;AAAA,EAAqB;AAAA,EACxD;AAAA,EAAY;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAC1C;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAY;AAAA,EAAY;AAAA,EAC9C;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAkB;AAAA,EACpD;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAC5C;AAAA,EAAc;AAAA,EAAW;AAAA,EAAc;AAAA,EAAe;AAAA,EACtD;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAU;AAAA,EAAe;AAAA,EAC9C;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAC5C;AAAA,EAAkB;AAAA,EAAS;AAAA,EAAS;AAAA,EAAM;AAAA,EAAO;AAAA,EACjD;AAAA,EAAa;AAAA,EAAc;AAAA,EAAc;AAAA,EAAc;AAAA,EACvD;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAa;AAAA,EAC/C;AAAA,EAAa;AAAA,EAAW;AAAA,EAAgB;AAAA,EAAgB;AAAA,EACxD;AAAA,EAAiB;AAAA,EAAkB;AAAA,EAAc;AAAA,EACjD;AAAA,EAAY;AAAA,EAAmB;AAAA,EAC/B;AAAA,EAAqB;AAAA,EAAqB;AAAA,EAC1C;AAAA,EAAU;AAAA,EAAqB;AAAA,EAAU;AAAA,EAAe;AAAA,EACxD;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAwB;AAAA,EAAQ;AAAA,EAC9D;AAAA,EAAQ;AAAA,EAAmB;AAAA,EAAW;AAAA,EAAW;AAAA,EACjD;AAAA,EAAU;AAAA,EAAc;AAAA,EAAa;AAAA,EAAY;AAAA,EACjD;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAU;AAAA,EAChC;AAAA,EAAiB;AAAA,EAAiB;AAAA,EAAe;AAAA,EAAU;AAAA,EAC3D;AAAA,EAAgB;AAAA,EAAY;AAAA,EAAU;AAAA,EAAY;AAAA,EAClD;AAAA,EAAW;AAAA,EAAc;AAAA,EAAmB;AAAA,EAC5C;AAAA,EAAkB;AAAA,EAAc;AAAA,EAAa;AAAA,EAC7C;AAAA,EAAe;AAAA,EAAa;AAAA,EAAY;AAAA,EAAc;AAAA,EACtD;AAAA,EAAc;AAAA,EAAmB;AAAA,EACjC;AAAA,EAAuB;AAAA,EAAa;AAAA,EACpC;AAAA,EAAyB;AAAA,EAAyB;AAAA,EAClD;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAW;AAAA,EACtC;AAAA,EAAU;AAAA,EAAY;AAAA,EAAY;AAAA,EAAe;AAAA,EACjD;AAAA,EAAiB;AAAA,EAA2B;AAAA,EAAU;AAAA,EACtD;AAAA,EAAU;AAAA,EAAe;AAAA,EAAc;AAAA,EAAS;AAAA,EAChD;AAAA,EAAU;AAAA,EAAU;AAAA,EAAU;AAAA,EAAe;AAAA,EAC7C;AAAA,EAAkB;AAAA,EAAmB;AAAA,EACrC;AAAA,EAAmB;AAAA,EACnB;AAAA,EAAsB;AAAA,EAAc;AAAA,EAAkB;AAAA,EAAU;AAAA,EAChE;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAoB;AAAA,EAAY;AAAA,EACpD;AAAA,EAAe;AAAA,EAAU;AAAA,EAAY;AAAA,EAAY;AAAA,EACjD;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAAU;AAAA,EAC5C;AAAA,EAAS;AAAA,EAAiB;AAAA,EAAO;AAAA,EAAO;AAAA,EACxC;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAS;AAAA,EACjC;AAAA,EAAW;AAAA,EAAoB;AAAA,EAAW;AAAA,EAAQ;AAAA,EAClD;AAAA,EAAU;AAAA,EAAe;AAAA,EAAU;AAAA,EAAW;AAAA,EAC9C;AAAA,EAAS;AAAA,EAAS;AAAA,EAAa;AAAA,EAAO;AAAA,EACtC;AAAA,EAAc;AAAA,EAAa;AAAA,EAAe;AAAA,EAC1C;AAAA,EAAoB;AAAA,EAAe;AAAA,EAAa;AAAA,EAChD;AAAA,EAAc;AAAA,EAAa;AAAA,EAAc;AAAA,EACzC;AAAA,EAAmB;AAAA,EAAiB;AAAA,EACpC;AAAA,EAAe;AAAA,EAAY;AAAA,EAAa;AAAA,EAAc;AAAA,EACtD;AAAA,EAAc;AAAA,EAAY;AAAA,EAAY;AAAA,EAAW;AAAA,EACjD;AAAA,EAAO;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAClC;AAAA,EAAU;AAAA,EAAS;AAAA,EAAsB;AAAA,EAAoB;AAAA,EAC7D;AAAA,EAAS;AAAA,EAAU;AAAA,EAAS;AAAA,EAAY;AAAA,EACxC;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EAAY;AAAA,EAC5C;AAAA,EAAa;AAAA,EAAgB;AAAA,EAAc;AAAA,EAC3C;AAAA,EAAqB;AAAA,EAAY;AAAA,EAAkB;AAAA,EACnD;AAAA,EAAY;AAAA,EACZ;AAAA,EAAW;AAAA,EAAmB;AAAA,EAAU;AAAA,EAAc;AAAA,EACtD;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAY;AAAA,EAAS;AAAA,EAC3C;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAW;AAAA,EAC7C;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAW;AAAA,EACvC;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAW;AAAA,EACjD;AAAA,EAAc;AAAA,EAAa;AAAA,EAAO;AAAA,EAAS;AAAA,EAC3C;AAAA,EAAO;AAAA,EACP;AAAA,EAA4B;AAAA,EAC5B;AAAA,EACA;AAAA,EAAS;AAAA,EAAY;AAAA,EAAiB;AAAA,EAAW;AAAA,EACjD;AAAA,EAAc;AAAA,EAAiB;AAAA,EAC/B;AAAA,EAAqB;AAAA,EACrB;AAAA,EAAW;AAAA,EAAY;AAAA,EAAU;AAAA,EAAe;AAAA,EAChD;AAAA,EAAU;AAAA,EAAgB;AAAA,EAAU;AAAA,EAAS;AAAA,EAC7C;AAAA,EAAoB;AAAA,EAAmB;AAAA,EAAa;AAAA,EAAQ;AAAA,EAC5D;AAAA,EAAU;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAU;AAAA,EAC/C;AAAA,EAAe;AAAA,EAAmB;AAAA,EAAU;AAAA,EAAS;AAAA,EACrD;AAAA,EAAa;AAAA,EAAS;AAAA,EAAY;AAAA,EAAa;AAAA,EAC/C;AAAA,EAAa;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAC9C;AAAA,EAAS;AAAA,EAAU;AAAA,EAAe;AAAA,EAAgB;AAAA,EAClD;AAAA,EAAc;AAAA,EAAc;AAAA,EAAY;AAAA,EAAS;AAAA,EACjD;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAAW;AAAA,EAC1C;AAAA,EAAkB;AAAA,EAAY;AAAA,EAAU;AAAA,EAAe;AAAA,EACvD;AAAA,EAAY;AAAA,EAAW;AAAA,EAAa;AAAA,EAAa;AAAA,EACjD;AAAA,EAAc;AAAA,EAAa;AAAA,EAAmB;AAAA,EAAc;AAAA,EAC5D;AAAA,EAAa;AAAA,EAAsB;AAAA,EAAU;AAAA,EAC7C;AAAA,EAA4B;AAAA,EAAoB;AAAA,EAChD;AAAA,EAAW;AAAA,EAAe;AAAA,EAC1B;AAAA,EAAU;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAY;AAAA,EAClD;AAAA,EAAa;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAU;AAAA,EACrD;AAAA,EAAU;AAAA,EAAW;AAAA,EAAY;AAAA,EAAa;AAAA,EAC9C;AAAA,EAAW;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAC3C;AAAA,EAAU;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAmB;AAAA,EAClD;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAY;AAAA,EAAU;AAAA,EACzC;AAAA,EAAe;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAY;AAAA,EACzD;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAc;AAAA,EACzC;AAAA,EAAY;AAAA,EAAS;AAAA,EAAe;AAAA,EAAU;AAAA,EAC9C;AAAA,EAAY;AAAA,EAAW;AAAA,EAAO;AAAA,EAAiB;AAAA,EAC/C;AAAA,EAAe;AAAA,EAAkB;AAAA,EAAY;AAAA,EAAa;AAAA,EAC1D;AAAA,EAAoB;AAAA,EAAkB;AAAA,EACtC;AAAA,EAAiB;AAAA,EACjB;AAAA,EAAW;AAAA,EAAmB;AAAA,EAAgB;AAAA,EAAO;AAAA,EACrD;AAAA,EAAQ;AAAA,EAAc;AAAA,EAAe;AAAA,EAAU;AAAA,EAC/C;AAAA,EAAa;AAAA,EAAiB;AAAA,EAC9B;AAAA,EAAc;AAAA,EACd;AAAA,EAAY;AAAA,EAAe;AAAA,EAC3B;AAAA,EAAiB;AAAA,EACjB;AAAA,EAAW;AAAA,EAAO;AAAA,EAAkB;AAAA,EAAgB;AAAA,EACpD;AAAA,EAAU;AAAA,EAAe;AAAA,EACzB;AAAA,EAAkB;AAAA,EAClB;AAAA,EAAc;AAAA,EAAc;AAAA,EAAgB;AAAA,EAAS;AAAA,EACrD;AAAA,EAAY;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAU;AAAA,EACxC;AAAA,EAAc;AAAA,EAAiB;AAAA,EAAc;AAAA,EAAQ;AAAA,EACrD;AAAA,EAAe;AAAA,EAAiB;AAAA,EAAW;AAAA,EAAW;AAAA,EACtD;AAAA,EAAU;AAAA,EAAY;AAAA,EAAc;AAAA,EAAe;AAAA,EACnD;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAY;AAAA,EACvC;AAAA,EAAe;AAAA,EAAa;AAAA,EAAkB;AAAA,EAAc;AAAA,EAC5D;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAQ;AAAA,EACnC;AAAA,EAAc;AAAA,EAAW;AAAA,EAAS;AAAA,EAAiB;AAAA,EACnD;AAAA,EAAY;AAAA,EAAa;AAAA,EAAY;AAAA,EAAa;AAAA,EAClD;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAa;AAAA,EAAY;AAAA,EAC5C;AAAA,EAAW;AAAA,EAAY;AAAA,EAAY;AAAA,EAAQ;AAAA,EAC3C;AAAA,EAAe;AAAA,EAAY;AAAA,EAAY;AAAA,EAAW;AAAA,EAClD;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAO;AAAA,EACpC;AAAA,EAAW;AAAA,EAAY;AAAA,EAAY;AAAA,EAAe;AAAA,EAClD;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAC1C;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAS;AAAA,EACtC;AAAA,EAAS;AAAA,EAAS;AAAA,EAAW;AAAA,EAAe;AAAA,EAC5C;AAAA,EAAa;AAAA,EAAe;AAAA,EAAa;AAAA,EAAe;AAAA,EACxD;AAAA,EAAkB;AAAA,EAAe;AAAA,EAAc;AAAA,EAAa;AAAA,EAC5D;AAAA,EAAa;AAAA,EAAc;AAAA,EAAe;AAAA,EAAa;AAAA,EACvD;AAAA,EAAU;AAAA,EAAS;AAAA,EAAW;AAAA,EAAW;AAAA,EACzC;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAc;AAAA,EACxC;AAAA,EAAY;AAAA,EAAY;AAAA,EAAc;AAAA,EAAY;AAAA,EAClD;AAAA,EAAkB;AAAA,EAAa;AAAA,EAAiB;AAAA,EAAc;AAAA,EAC9D;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAAY;AAAA,EAChD;AAAA,EAAa;AAAA,EAAc;AAAA,EAAY;AAAA,EAAa;AAAA,EACpD;AAAA,EAAY;AAAA,EAAS;AAAA,EAAS;AAAA,EAAa;AAAA,EAC3C;AAAA,EAAmB;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAU;AAAA,EACxD;AAAA,EAAmB;AAAA,EAAW;AAAA,EAC9B;AAAA,EAAqB;AAAA,EACrB;AAAA,EAAmB;AAAA,EAAW;AAAA,EAAU;AAAA,EAAU;AAAA,EAClD;AAAA,EAAO;AAAA,EAAW;AAAA,EAAO;AAAA,EAAU;AAAA,EACnC;AAAA,EAAoB;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAU;AAAA,EACzD;AAAA,EAAU;AAAA,EAAU;AAAA,EAAW;AAAA,EAAe;AAAA,EAC9C;AAAA,EAAe;AAAA,EAAmB;AAAA,EAClC;AAAA,EAAiB;AAAA,EACjB;AAAA,EAAY;AAAA,EAAc;AAAA,EAAc;AAAA,EAAY;AAAA,EACpD;AAAA,EAAY;AAAA,EAAe;AAAA,EAAU;AAAA,EAAQ;AAAA,EAC7C;AAAA,EAAkB;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAc;AAAA,EACzD;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAAS;AAAA,EACzC;AAAA,EAAU;AAAA,EAAW;AAAA,EAAO;AAAA,EAAW;AAAA,EACvC;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAY;AAAA,EAAW;AAAA,EAC3C;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAAQ;AAAA,EACrC;AAAA,EAAY;AAAA,EAAY;AAAA,EAAc;AAAA,EAAY;AAAA,EAClD;AAAA,EAAU;AAAA,EAAY;AAAA,EAAU;AAAA,EAAU;AAAA,EAC1C;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAe;AAAA,EAAU;AAAA,EAC3C;AAAA,EAAU;AAAA,EAAU;AAAA,EAAe;AAAA,EAAc;AAAA,EACjD;AAAA,EAAW;AAAA,EAAW;AAAA,EAAU;AAAA,EAAW;AAAA,EAC3C;AAAA,EAAc;AAAA,EAAY;AAAA,EAAc;AAAA,EAAU;AAAA,EAClD;AAAA,EAAU;AAAA,EAAY;AAAA,EAAW;AAAA,EAAiB;AAAA,EAClD;AAAA,EAAa;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAC5C;AAAA,EAAU;AAAA,EAAS;AAAA,EAAe;AAAA,EAAuB;AAAA,EACzD;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAAO;AAAA,EACpC;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAa;AAAA,EACzC;AAAA,EAAc;AAAA,EAAiB;AAAA,EAAgB;AAAA,EAAQ;AAAA,EACvD;AAAA,EAAU;AAAA,EAAU;AAAA,EAAO;AAAA,EAAc;AAAA,EACzC;AAAA,EAAS;AAAA,EAAa;AAAA,EAAqB;AAAA,EAAW;AAAA,EACtD;AAAA,EAAS;AAAA,EAAS;AAAA,EAAa;AAAA,EAAY;AAAA,EAC3C;AAAA,EAAW;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EACxC;AAAA,EAAW;AAAA,EAAW;AAAA,EAAY;AAAA,EAAa;AAAA,EAC/C;AAAA,EAAS;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EACnC;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAW;AAAA,EAAc;AAAA,EAC5C;AAAA,EAAa;AAAA,EAAW;AAAA,EAAS;AAAA,EAAW;AAAA,EAC5C;AAAA,EAAgB;AAAA,EAAW;AAAA,EAAgB;AAAA,EAAY;AAAA,EACvD;AAAA,EAAgB;AAAA,EAAO;AAAA,EAAY;AAAA,EAAY;AAAA,EAC/C;AAAA,EAAU;AAAA,EAAW;AAAA,EAAc;AAAA,EAAQ;AAAA,EAC3C;AAAA,EAAa;AAAA,EAAW;AAAA,EAAW;AAAA,EAAS;AAAA,EAC5C;AAAA,EAAiB;AAAA,EAAmB;AAAA,EACpC;AAAA,EAA6B;AAAA,EAC7B;AAAA,EAAmB;AAAA,EAAgB;AAAA,EACnC;AAAA,EAAgB;AAAA,EAChB;AAAA,EAAwB;AAAA,EAAiB;AAAA,EACzC;AAAA,EAAgB;AAAA,EAChB;AAAA,EAAe;AAAA,EAAoB;AAAA,EACnC;AAAA,EAAiB;AAAA,EACjB;AAAA,EAAU;AAAA,EAAa;AAAA,EAAa;AAAA,EAAa;AAAA,EACjD;AAAA,EAAc;AAAA,EAAkB;AAAA,EAAc;AAAA,EAAc;AAAA,EAC5D;AAAA,EAAa;AAAA,EAAa;AAAA,EAAa;AAAA,EAAc;AAAA,EACrD;AAAA,EAAe;AAAA,EAAa;AAAA,EAAc;AAAA,EAAU;AAAA,EACpD;AAAA,EAAS;AAAA,EAAO;AAAA,EAAa;AAAA,EAAU;AAAA,EACvC;AAAA,EAAc;AAAA,EAAU;AAAA,EACxB;AAAA,EAAoB;AAAA,EACpB;AAAA,EAAc;AAAA,EAAgB;AAAA,EAC9B;AAAA,EAAgB;AAAA,EAChB;AAAA,EAAqB;AAAA,EAAiB;AAAA,EACtC;AAAA,EAAU;AAAA,EACV;AAAA,EAAY;AAAA,EAAgB;AAAA,EAAQ;AAAA,EAAS;AAAA,EAC7C;AAAA,EAAW;AAAA,EAAY;AAAA,EAAa;AAAA,EAAU;AAAA,EAC9C;AAAA,EAAmB;AAAA,EACnB;AAAA,EAAY;AAAA,EAAW;AAAA,EACvB;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAY;AAAA,EAC/C;AAAA,EAAW;AAAA,EAAkB;AAAA,EAC7B;AAAA,EAAU;AAAA,EAAiB;AAAA,EAAmB;AAAA,EAAQ;AACxD;AACA,IAAI,WAAW,WAAW,YAAY;AAEtC,IAAI,eAAe;AAAA,EACjB;AAAA,EAAS;AAAA,EAAO;AAAA,EAAW;AAAA,EAC3B;AAAA,EAAY;AAAA,EAAS;AAAA,EAAU;AAAA,EAC/B;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAY;AAAA,EAC7B;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAM;AAAA,EAAQ;AAAA,EACjC;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAC7B;AAAA,EAAM;AAAA,EAAO;AACf;AACA,IAAI,WAAW,WAAW,YAAY;AAEtC,IAAI,cAAc,IAAI,OAAO,2BAA2C,GAAG;AAE3E,IAAI,kBAAkB;AACtB,IAAI,gBAAgB,IAAI,OAAO,kCAAkC,GAAG;AAEpE,SAAS,UAAU,QAAQ;AAEzB,MAAI,OAAO,SAAS,EAAG,QAAO;AAG9B,MAAI,OAAO,MAAM,GAAG,GAAG;AACrB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,cAAc,KAAK,GAAG;AACrC,QAAI,OAAO,MAAM,sBAAsB;AACrC,aAAO;AACT,QAAI,OAAO,MAAM,iCAAiC;AAChD,aAAO;AACT,QAAI,OAAO,MAAM,4BAA4B;AAC3C,aAAO;AAAA,EACX;AAGA,MAAI,OAAO,MAAM,iBAAiB,GAAG;AAAE,WAAO;AAAA,EAAU;AACxD,MAAI,OAAO,MAAM,iBAAiB,GAAG;AAAE,WAAO;AAAA,EAAU;AAGxD,MAAI,OAAO,MAAM,QAAQ,GAAG;AAAE,WAAO;AAAA,EAAW;AAChD,MAAI,OAAO,MAAM,QAAQ,GAAG;AAAE,WAAO;AAAA,EAAW;AAChD,MAAI,OAAO,MAAM,WAAW,GAAG;AAAE,WAAO;AAAA,EAAY;AAEpD,MAAI,OAAO,MAAM,eAAe,KAAK,OAAO,MAAM,aAAa,GAAG;AAChE,WAAO;AAAA,EAAY;AAGrB,SAAO,KAAK;AACZ,SAAO;AACT;AAEO,IAAM,MAAM;AAAA,EACjB,MAAM;AAAA,EACN,OAAO,SAAS,QAAQ;AACtB,WAAO,UAAU,MAAM;AAAA,EACzB;AAAA,EACA,cAAc;AAAA,IACZ,cAAc,aAAa,OAAO,YAAY;AAAA,EAChD;AACF;", "names": []}