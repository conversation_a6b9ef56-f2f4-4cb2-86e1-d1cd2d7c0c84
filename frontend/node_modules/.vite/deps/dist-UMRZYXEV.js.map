{"version": 3, "sources": ["../../@lezer/sass/dist/index.js", "../../@codemirror/lang-sass/dist/index.js"], "sourcesContent": ["import { ExternalTokenizer, ContextTracker, LRParser } from '@lezer/lr';\nimport { styleTags, tags } from '@lezer/highlight';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst indent = 168,\n  dedent = 169,\n  descendantOp = 170,\n  InterpolationEnd = 1,\n  InterpolationContinue = 2,\n  Unit = 3,\n  callee = 171,\n  identifier = 172,\n  VariableName = 4,\n  queryIdentifier = 173,\n  InterpolationStart = 5,\n  newline = 174,\n  blankLineStart = 175,\n  eof = 176,\n  whitespace = 177,\n  LineComment = 6,\n  Comment = 7,\n  IndentedMixin = 8,\n  IndentedInclude = 9,\n  Dialect_indented = 0;\n\n/* Hand-written tokenizers for CSS tokens that can't be\n   expressed by <PERSON><PERSON>'s built-in tokenizer. */\n\nconst space = [9, 10, 11, 12, 13, 32, 133, 160, 5760, 8192, 8193, 8194, 8195, 8196, 8197,\n               8198, 8199, 8200, 8201, 8202, 8232, 8233, 8239, 8287, 12288];\nconst colon = 58, parenL = 40, underscore = 95, bracketL = 91, dash = 45, period = 46,\n      hash = 35, percent = 37, braceL = 123, braceR = 125, slash = 47, asterisk = 42,\n      newlineChar = 10, equals = 61, plus = 43, and = 38;\n\nfunction isAlpha(ch) { return ch >= 65 && ch <= 90 || ch >= 97 && ch <= 122 || ch >= 161 }\n\nfunction isDigit(ch) { return ch >= 48 && ch <= 57 }\n\nfunction startOfComment(input) {\n  let next;\n  return input.next == slash && ((next = input.peek(1)) == slash || next == asterisk)\n}\n\nconst spaces = new ExternalTokenizer((input, stack) => {\n  if (stack.dialectEnabled(Dialect_indented)) {\n    let prev;\n    if (input.next < 0 && stack.canShift(eof)) {\n      input.acceptToken(eof);\n    } else if (((prev = input.peek(-1)) == newlineChar || prev < 0) && stack.canShift(blankLineStart)) {\n      let spaces = 0;\n      while (input.next != newlineChar && space.includes(input.next)) { input.advance(); spaces++; }\n      if (input.next == newlineChar || startOfComment(input))\n        input.acceptToken(blankLineStart, -spaces);\n      else if (spaces)\n        input.acceptToken(whitespace);\n    } else if (input.next == newlineChar) {\n      input.acceptToken(newline, 1);\n    } else if (space.includes(input.next)) {\n      input.advance();\n      while (input.next != newlineChar && space.includes(input.next)) input.advance();\n      input.acceptToken(whitespace);\n    }\n  } else {\n    let length = 0;\n    while (space.includes(input.next)) {\n      input.advance();\n      length++;\n    }\n    if (length) input.acceptToken(whitespace);\n  }\n}, {contextual: true});\n\nconst comments = new ExternalTokenizer((input, stack) => {\n  if (!startOfComment(input)) return\n  input.advance();\n  if (stack.dialectEnabled(Dialect_indented)) {\n    let indentedComment = -1;\n    for (let off = 1;; off++) {\n      let prev = input.peek(-off - 1);\n      if (prev == newlineChar || prev < 0) {\n        indentedComment = off + 1;\n        break\n      } else if (!space.includes(prev)) {\n        break\n      }\n    }\n    if (indentedComment > -1) { // Weird indented-style comment\n      let block = input.next == asterisk, end = 0;\n      input.advance();\n      while (input.next >= 0) {\n        if (input.next == newlineChar) {\n          input.advance();\n          let indented = 0;\n          while (input.next != newlineChar && space.includes(input.next)) {\n            indented++;\n            input.advance();\n          }\n          if (indented < indentedComment) {\n            end = -indented - 1;\n            break\n          }\n        } else if (block && input.next == asterisk && input.peek(1) == slash) {\n          end = 2;\n          break\n        } else {\n          input.advance();\n        }\n      }\n      input.acceptToken(block ? Comment : LineComment, end);\n      return\n    }\n  }\n  if (input.next == slash) {\n    while (input.next != newlineChar && input.next >= 0) input.advance();\n    input.acceptToken(LineComment);\n  } else {\n    input.advance();\n    while (input.next >= 0) {\n      let {next} = input;\n      input.advance();\n      if (next == asterisk && input.next == slash) {\n        input.advance();\n        break\n      }\n    }\n    input.acceptToken(Comment);\n  }\n});\n\nconst indentedMixins = new ExternalTokenizer((input, stack) => {\n  if ((input.next == plus || input.next == equals) && stack.dialectEnabled(Dialect_indented))\n    input.acceptToken(input.next == equals ? IndentedMixin : IndentedInclude, 1);\n});\n\nconst indentation = new ExternalTokenizer((input, stack) => {\n  if (!stack.dialectEnabled(Dialect_indented)) return\n  let cDepth = stack.context.depth;\n  if (input.next < 0 && cDepth) {\n    input.acceptToken(dedent);\n    return\n  }\n  let prev = input.peek(-1);\n  if (prev == newlineChar) {\n    let depth = 0;\n    while (input.next != newlineChar && space.includes(input.next)) {\n      input.advance();\n      depth++;\n    }\n    if (depth != cDepth &&\n        input.next != newlineChar && !startOfComment(input)) {\n      if (depth < cDepth) input.acceptToken(dedent, -depth);\n      else input.acceptToken(indent);\n    }\n  }\n});\n\nconst identifiers = new ExternalTokenizer((input, stack) => {\n  for (let inside = false, dashes = 0, i = 0;; i++) {\n    let {next} = input;\n    if (isAlpha(next) || next == dash || next == underscore || (inside && isDigit(next))) {\n      if (!inside && (next != dash || i > 0)) inside = true;\n      if (dashes === i && next == dash) dashes++;\n      input.advance();\n    } else if (next == hash && input.peek(1) == braceL) {\n      input.acceptToken(InterpolationStart, 2);\n      break\n    } else {\n      if (inside) input.acceptToken(\n        dashes == 2 && stack.canShift(VariableName) ? VariableName\n          : stack.canShift(queryIdentifier) ? queryIdentifier\n          : next == parenL ? callee\n          : identifier);\n      break\n    }\n  }\n});\n\nconst interpolationEnd = new ExternalTokenizer(input => {\n  if (input.next == braceR) {\n    input.advance();\n    while (isAlpha(input.next) || input.next == dash || input.next == underscore || isDigit(input.next))\n      input.advance();\n    if (input.next == hash && input.peek(1) == braceL)\n      input.acceptToken(InterpolationContinue, 2);\n    else\n      input.acceptToken(InterpolationEnd);\n  }\n});\n\nconst descendant = new ExternalTokenizer(input => {\n  if (space.includes(input.peek(-1))) {\n    let {next} = input;\n    if (isAlpha(next) || next == underscore || next == hash || next == period ||\n        next == bracketL || next == colon && isAlpha(input.peek(1)) || next == dash || next == and || next == asterisk)\n      input.acceptToken(descendantOp);\n  }\n});\n\nconst unitToken = new ExternalTokenizer(input => {\n  if (!space.includes(input.peek(-1))) {\n    let {next} = input;\n    if (next == percent) { input.advance(); input.acceptToken(Unit); }\n    if (isAlpha(next)) {\n      do { input.advance(); } while (isAlpha(input.next) || isDigit(input.next))\n      input.acceptToken(Unit);\n    }\n  }\n});\n\nfunction IndentLevel(parent, depth) {\n  this.parent = parent;\n  this.depth = depth;\n  this.hash = (parent ? parent.hash + parent.hash << 8 : 0) + depth + (depth << 4);\n}\n\nconst topIndent = new IndentLevel(null, 0);\n\nconst trackIndent = new ContextTracker({\n  start: topIndent,\n  shift(context, term, stack, input) {\n    if (term == indent) return new IndentLevel(context, stack.pos - input.pos)\n    if (term == dedent) return context.parent\n    return context\n  },\n  hash(context) { return context.hash }\n});\n\nconst cssHighlighting = styleTags({\n  \"AtKeyword import charset namespace keyframes media supports include mixin use forward extend at-root\": tags.definitionKeyword,\n  \"Keyword selector\": tags.keyword,\n  \"ControlKeyword\": tags.controlKeyword,\n  NamespaceName: tags.namespace,\n  KeyframeName: tags.labelName,\n  KeyframeRangeName: tags.operatorKeyword,\n  TagName: tags.tagName,\n  \"ClassName Suffix\": tags.className,\n  PseudoClassName: tags.constant(tags.className),\n  IdName: tags.labelName,\n  \"FeatureName PropertyName\": tags.propertyName,\n  AttributeName: tags.attributeName,\n  NumberLiteral: tags.number,\n  KeywordQuery: tags.keyword,\n  UnaryQueryOp: tags.operatorKeyword,\n  \"CallTag ValueName\": tags.atom,\n  VariableName: tags.variableName,\n  SassVariableName: tags.special(tags.variableName),\n  Callee: tags.operatorKeyword,\n  Unit: tags.unit,\n  \"UniversalSelector NestingSelector IndentedMixin IndentedInclude\": tags.definitionOperator,\n  MatchOp: tags.compareOperator,\n  \"ChildOp SiblingOp, LogicOp\": tags.logicOperator,\n  BinOp: tags.arithmeticOperator,\n  \"Important Global Default\": tags.modifier,\n  Comment: tags.blockComment,\n  LineComment: tags.lineComment,\n  ColorLiteral: tags.color,\n  \"ParenthesizedContent StringLiteral\": tags.string,\n  \"InterpolationStart InterpolationContinue InterpolationEnd\": tags.meta,\n  \": \\\"...\\\"\": tags.punctuation,\n  \"PseudoOp #\": tags.derefOperator,\n  \"; ,\": tags.separator,\n  \"( )\": tags.paren,\n  \"[ ]\": tags.squareBracket,\n  \"{ }\": tags.brace\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_identifier = {__proto__:null,not:62, using:197, as:207, with:211, without:211, hide:225, show:225, if:263, from:269, to:271, through:273, in:279};\nconst spec_callee = {__proto__:null,url:82, \"url-prefix\":82, domain:82, regexp:82, lang:104, \"nth-child\":104, \"nth-last-child\":104, \"nth-of-type\":104, \"nth-last-of-type\":104, dir:104, \"host-context\":104};\nconst spec_AtKeyword = {__proto__:null,\"@import\":162, \"@include\":194, \"@mixin\":200, \"@function\":200, \"@use\":204, \"@extend\":214, \"@at-root\":218, \"@forward\":222, \"@media\":228, \"@charset\":232, \"@namespace\":236, \"@keyframes\":242, \"@supports\":254, \"@if\":258, \"@else\":260, \"@for\":266, \"@each\":276, \"@while\":282, \"@debug\":286, \"@warn\":286, \"@error\":286, \"@return\":286};\nconst spec_queryIdentifier = {__proto__:null,layer:166, not:184, only:184, selector:190};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"!$WQ`Q+tOOO#fQ+tOOP#mOpOOOOQ#U'#Ch'#ChO#rQ(pO'#CjOOQ#U'#Ci'#CiO%_Q)QO'#GXO%rQ.jO'#CnO&mQ#dO'#D]O'dQ(pO'#CgO'kQ)OO'#D_O'vQ#dO'#DfO'{Q#dO'#DiO(QQ#dO'#DqOOQ#U'#GX'#GXO(VQ(pO'#GXO(^Q(nO'#DuO%rQ.jO'#D}O%rQ.jO'#E`O%rQ.jO'#EcO%rQ.jO'#EeO(cQ)OO'#EjO)TQ)OO'#ElO%rQ.jO'#EnO)bQ)OO'#EqO%rQ.jO'#EsO)|Q)OO'#EuO*XQ)OO'#ExO*aQ)OO'#FOO*uQ)OO'#FbOOQ&Z'#GW'#GWOOQ&Y'#Fe'#FeO+PQ(nO'#FeQ`Q+tOOO%rQ.jO'#FQO+[Q(nO'#FUO+aQ)OO'#FZO%rQ.jO'#F^O%rQ.jO'#F`OOQ&Z'#Fm'#FmO+iQ+uO'#GaO+vQ(oO'#GaQOQ#SOOP,XO#SO'#GVPOOO)CAz)CAzOOQ#U'#Cm'#CmOOQ#U,59W,59WOOQ#i'#Cp'#CpO%rQ.jO'#CsO,xQ.wO'#CuO/dQ.^O,59YO%rQ.jO'#CzOOQ#S'#DP'#DPO/uQ(nO'#DUO/zQ)OO'#DZOOQ#i'#GZ'#GZO0SQ(nO'#DOOOQ#U'#D^'#D^OOQ#U,59w,59wO&mQ#dO,59wO0XQ)OO,59yO'vQ#dO,5:QO'{Q#dO,5:TO(cQ)OO,5:WO(cQ)OO,5:YO(cQ)OO,5:ZO(cQ)OO'#FlO0dQ(nO,59RO0oQ+tO'#DsO0vQ#TO'#DsOOQ&Z,59R,59ROOQ#U'#Da'#DaOOQ#S'#Dd'#DdOOQ#U,59y,59yO0{Q(nO,59yO1QQ(nO,59yOOQ#U'#Dh'#DhOOQ#U,5:Q,5:QOOQ#S'#Dj'#DjO1VQ9`O,5:TOOQ#U'#Dr'#DrOOQ#U,5:],5:]O2YQ.jO,5:aO2dQ.jO,5:iO3`Q.jO,5:zO3mQ.YO,5:}O4OQ.jO,5;POOQ#U'#Cj'#CjO4wQ(pO,5;UO5UQ(pO,5;WOOQ&Z,5;W,5;WO5]Q)OO,5;WO5bQ.jO,5;YOOQ#S'#ET'#ETO6TQ.jO'#E]O6kQ(nO'#GcO*aQ)OO'#EZO7PQ(nO'#E^OOQ#S'#Gd'#GdO0gQ(nO,5;]O4UQ.YO,5;_OOQ#d'#Ew'#EwO+PQ(nO,5;aO7UQ)OO,5;aOOQ#S'#Ez'#EzO7^Q(nO,5;dO7cQ(nO,5;jO7nQ(nO,5;|OOQ&Z'#Gf'#GfOOQ&Y,5<P,5<POOQ&Y-E9c-E9cO3mQ.YO,5;lO7|Q)OO,5;pO8RQ)OO'#GhO8ZQ)OO,5;uO3mQ.YO,5;xO4UQ.YO,5;zOOQ&Z-E9k-E9kO8`Q(oO,5<{OOQ&Z'#Gb'#GbO8qQ+uO'#FpO8`Q(oO,5<{POO#S'#Fd'#FdP9UO#SO,5<qPOOO,5<q,5<qO9dQ.YO,59_OOQ#i,59a,59aO%rQ.jO,59cO%rQ.jO,59hO%rQ.jO'#FiO9rQ#WO1G.tOOQ#k1G.t1G.tO9zQ.oO,59fO<pQ! lO,59pOOQ#d'#D['#D[OOQ#d'#Fh'#FhO<{Q)OO,59uOOQ#i,59u,59uO={Q.jO'#DQOOQ#i,59j,59jOOQ#U1G/c1G/cOOQ#U1G/e1G/eO0{Q(nO1G/eO1QQ(nO1G/eOOQ#U1G/l1G/lO>VQ9`O1G/oO>pQ(pO1G/rO?dQ(pO1G/tO@WQ(pO1G/uO@zQ(pO,5<WOOQ#S-E9j-E9jOOQ&Z1G.m1G.mOAXQ(nO,5:_OA^Q+uO,5:_OAeQ)OO'#DeOAlQ.jO'#DcOOQ#U1G/o1G/oO%rQ.jO1G/oOBkQ.jO'#DwOBuQ.kO1G/{OOQ#T1G/{1G/{OCrQ)OO'#EQO+PQ(nO1G0TO2pQ)OO1G0TODaQ+uO'#GfOOQ&Z1G0f1G0fO0SQ(nO1G0fOOQ&Z1G0i1G0iOOQ&Z1G0k1G0kO0SQ(nO1G0kOFyQ)OO1G0kOOQ&Z1G0p1G0pOOQ&Z1G0r1G0rOGRQ)OO1G0rOGWQ(nO1G0rOG]Q)OO1G0tOOQ&Z1G0t1G0tOGkQ.jO'#FsOG{Q#dO1G0tOHQQ!N^O'#CuOH]Q!NUO'#ETOHkQ!NUO,5:pOHsQ(nO,5:wOOQ#S'#Ge'#GeOHnQ!NUO,5:sO*aQ)OO,5:rOH{Q)OO'#FrOI`Q(nO,5<}OIqQ(nO,5:uO(cQ)OO,5:xOOQ&Z1G0w1G0wOOQ&Z1G0y1G0yOOQ&Z1G0{1G0{O+PQ(nO1G0{OJYQ)OO'#E{OOQ&Z1G1O1G1OOOQ&Z1G1U1G1UOOQ&Z1G1h1G1hOJeQ+uO1G1WO%rQ.jO1G1[OL}Q)OO'#FxOMYQ)OO,5=SO%rQ.jO1G1aOOQ&Z1G1d1G1dOOQ&Z1G1f1G1fOMbQ(oO1G2gOMsQ+uO,5<[OOQ#T,5<[,5<[OOQ#T-E9n-E9nPOO#S-E9b-E9bPOOO1G2]1G2]OOQ#i1G.y1G.yONWQ.oO1G.}OOQ#i1G/S1G/SO!!|Q.^O,5<TOOQ#W-E9g-E9gOOQ#k7+$`7+$`OOQ#i1G/[1G/[O!#_Q(nO1G/[OOQ#d-E9f-E9fOOQ#i1G/a1G/aO!#dQ.jO'#FfO!$qQ.jO'#G]O!&]Q.jO'#GZO!&dQ(nO,59lOOQ#U7+%P7+%POOQ#U7+%Z7+%ZO%rQ.jO7+%ZOOQ&Z1G/y1G/yO!&iQ#TO1G/yO!&nQ(pO'#G_O!&xQ(nO,5:PO!&}Q.jO'#G^O!'XQ(nO,59}O!'^Q.YO7+%ZO!'lQ.YO'#GZO!'}Q(nO,5:cOOQ#T,5:c,5:cO!(VQ.kO'#FoO%rQ.jO'#FoO!)yQ.kO7+%gOOQ#T7+%g7+%gO!*mQ#dO,5:lOOQ&Z7+%o7+%oO+PQ(nO7+%oO7nQ(nO7+&QO+PQ(nO7+&VOOQ#d'#Eh'#EhO!*rQ)OO7+&VO!+QQ(nO7+&^O*aQ)OO7+&^OOQ#d-E9q-E9qOOQ&Z7+&`7+&`O!+VQ.jO'#GgOOQ#d,5<_,5<_OF|Q(nO7+&`O%rQ.jO1G0[O!+qQ.jO1G0_OOQ#S1G0c1G0cOOQ#S1G0^1G0^O!+xQ(nO,5<^OOQ#S-E9p-E9pO!,^Q(pO1G0dOOQ&Z7+&g7+&gO,gQ(vO'#CuOOQ#S'#E}'#E}O!,eQ(nO'#E|OOQ#S'#E|'#E|O!,sQ(nO'#FuO!-OQ)OO,5;gOOQ&Z,5;g,5;gO!-ZQ+uO7+&rO!/sQ)OO7+&rO!0OQ.jO7+&vOOQ#d,5<d,5<dOOQ#d-E9v-E9vO3mQ.YO7+&{OOQ#T1G1v1G1vOOQ#i7+$v7+$vOOQ#d-E9d-E9dO!0aQ.jO'#FgO!0nQ(nO,5<wO!0nQ(nO,5<wO%rQ.jO,5<wOOQ#i1G/W1G/WO!0vQ.YO<<HuOOQ&Z7+%e7+%eO!1UQ)OO'#FkO!1`Q(nO,5<yOOQ#U1G/k1G/kO!1hQ.jO'#FjO!1rQ(nO,5<xOOQ#U1G/i1G/iOOQ#U<<Hu<<HuO1_Q.jO,5<YO!1zQ(nO'#FnOOQ#S-E9l-E9lOOQ#T1G/}1G/}O!2PQ.kO,5<ZOOQ#e-E9m-E9mOOQ#T<<IR<<IROOQ#S'#ES'#ESO!3sQ(nO1G0WOOQ&Z<<IZ<<IZOOQ&Z<<Il<<IlOOQ&Z<<Iq<<IqO0SQ(nO<<IqO*aQ)OO<<IxO!3{Q(nO<<IxO!4TQ.jO'#FtO!4hQ)OO,5=ROG]Q)OO<<IzO!4yQ.jO7+%vOOQ#S'#EV'#EVO!5QQ!NUO7+%yOOQ#S7+&O7+&OOOQ#S,5;h,5;hOJ]Q)OO'#FvO!,sQ(nO,5<aOOQ#d,5<a,5<aOOQ#d-E9s-E9sOOQ&Z1G1R1G1ROOQ&Z-E9u-E9uO!/sQ)OO<<J^O%rQ.jO,5<cOOQ&Z<<J^<<J^O%rQ.jO<<JbOOQ&Z<<Jg<<JgO!5YQ.jO,5<RO!5gQ.jO,5<ROOQ#S-E9e-E9eO!5nQ(nO1G2cO!5vQ.jO1G2cOOQ#UAN>aAN>aO!6QQ(pO,5<VOOQ#S-E9i-E9iO!6[Q.jO,5<UOOQ#S-E9h-E9hO!6fQ.YO1G1tO!6oQ(nO1G1tO!*mQ#dO'#FqO!6zQ(nO7+%rOOQ#d7+%r7+%rO+PQ(nOAN?]O!7SQ(nOAN?dO0gQ(nOAN?dO!7[Q.jO,5<`OOQ#d-E9r-E9rOG]Q)OOAN?fOOQ&ZAN?fAN?fOOQ#S<<Ib<<IbOOQ#S<<Ie<<IeO!7vQ.jO<<IeOOQ#S,5<b,5<bOOQ#S-E9t-E9tOOQ#d1G1{1G1{P!8_Q)OO'#FwOOQ&ZAN?xAN?xO3mQ.YO1G1}O3mQ.YOAN?|OOQ#S1G1m1G1mO%rQ.jO1G1mO!8dQ(nO7+'}OOQ#S7+'`7+'`OOQ#S,5<],5<]OOQ#S-E9o-E9oOOQ#d<<I^<<I^OOQ&ZG24wG24wO0gQ(nOG25OOOQ&ZG25OG25OOOQ&ZG25QG25QO!8lQ(nOAN?POOQ&Z7+'i7+'iOOQ&ZG25hG25hO!8qQ.jO7+'XOOQ&ZLD*jLD*jOOQ#SG24kG24k\",\n  stateData: \"!9R~O$wOSVOSUOS$uQQ~OS`OTVOWcOXbO_UOc`OqWOuYO|[O!SYO!ZZO!rmO!saO#TbO#WcO#YdO#_eO#afO#cgO#fhO#hiO#jjO#mkO#slO#urO#ysO$OtO$RuO$TvO$rSO$|RO%S]O~O$m%TP~P`O$u{O~Oq^Xu^Xu!jXw^X|^X!S^X!Z^X!a^X!d^X!h^X$p^X$t^X~Oq${Xu${Xw${X|${X!S${X!Z${X!a${X!d${X!h${X$p${X$t${X~O$r}O!o${X$v${Xf${Xe${X~P$jOS!XOTVO_!XOc!XOf!QOh!XOj!XOo!TOy!VO|!WO$q!UO$r!PO%O!RO~O$r!ZO~Oq!]Ou!^O|!`O!S!^O!Z!_O!a!aO!d!cO!h!fO$p!bO$t!gO~Ow!dO~P&rO!U!mO$q!jO$r!iO~O$r!nO~O$r!pO~O$r!rO~Ou!tO~P$jOu!tO~OTVO_UOqWOuYO|[O!SYO!ZZO$r!yO$|RO%S]O~Of!}O!h!fO$t!gO~P(cOTVOc#UOf#QO#O#SO#R#TO$s#PO!h%VP$t%VP~Oj#YOy!VO$r#XO~Oj#[O$r#[O~OTVOc#UOf#QO#O#SO#R#TO$s#PO~O!o%VP$v%VP~P)bO!o#`O$t#`O$v#`O~Oc#dO~Oc#eO$P%[P~O$m%TX!p%TX$o%TX~P`O!o#kO$t#kO$m%TX!p%TX$o%TX~OU#nOV#nO$t#pO$w#nO~OR#rO$tiX!hiXeiXwiX~OPiXQiXliXmiXqiXTiXciXfiX!oiX!uiX#OiX#RiX$siX$viX#UiX#ZiX#]iX#diXSiX_iXhiXjiXoiXyiX|iX!liX!miX!niX$qiX$riX%OiX$miXviX{iX#{iX#|iX!piX$oiX~P,gOP#wOQ#uOl#sOm#sOq#tO~Of#yO~O{#}O$r#zO~Of$OO~O!U$TO$q!jO$r!iO~Ow!dO!h!fO$t!gO~O!p%TP~P`O$n$_O~Of$`O~Of$aO~O{$bO!_$cO~OS!XOTVO_!XOc!XOf$dOh!XOj!XOo!TOy!VO|!WO$q!UO$r!PO%O!RO~O!h!fO$t!gO~P1_Ol#sOm#sOq#tO!u$gO!o%VP$t%VP$v%VP~P*aOl#sOm#sOq#tO!o#`O$v#`O~O!h!fO#U$lO$t$jO~P2}Ol#sOm#sOq#tO!h!fO$t!gO~O#Z$pO#]$oO$t#`O~P2}Oq!]Ou!^O|!`O!S!^O!Z!_O!a!aO!d!cO$p!bO~O!o#`O$t#`O$v#`O~P4]Of$sO~P&rO#]$tO~O#Z$xO#d$wO$t#`O~P2}OS$}Oh$}Oj$}Oy!VO$q!UO%O$yO~OTVOc#UOf#QO#O#SO#R#TO$s$zO~P5oOm%POw%QO!h%VX$t%VX!o%VX$v%VX~Of%TO~Oj%XOy!VO~O!h%YO~Om%PO!h!fO$t!gO~O!h!fO!o#`O$t$jO$v#`O~O#z%_O~Ow%`O$P%[X~O$P%bO~O!o#kO$t#kO$m%Ta!p%Ta$o%Ta~O!o$dX$m$dX$t$dX!p$dX$o$dX~P`OU#nOV#nO$t%jO$w#nO~Oe%kOl#sOm#sOq#tO~OP%pOQ#uO~Ol#sOm#sOq#tOPnaQnaTnacnafna!ona!una#Ona#Rna$sna$tna$vna!hna#Una#Zna#]na#dnaenaSna_nahnajnaonawnayna|na!lna!mna!nna$qna$rna%Ona$mnavna{na#{na#|na!pna$ona~Oe%qOj%rOz%rO~O{%tO$r#zO~OS!XOTVO_!XOf!QOh!XOj!XOo!TOy!VO|!WO$q!UO$r!PO%O!RO~Oc%wOe%PP~P=TO{%zO!_%{O~Oq!]Ou!^O|!`O!S!^O!Z!_O~Ow!`i!a!`i!d!`i!h!`i$p!`i$t!`i!o!`i$v!`if!`ie!`i~P>_Ow!bi!a!bi!d!bi!h!bi$p!bi$t!bi!o!bi$v!bif!bie!bi~P>_Ow!ci!a!ci!d!ci!h!ci$p!ci$t!ci!o!ci$v!cif!cie!ci~P>_Ow$`a!h$`a$t$`a~P4]O!p%|O~O$o%TP~P`Oe%RP~P(cOe%QP~P%rOS!XOTVO_!XOc!XOf!QOh!XOo!TOy!VO|!WO$q!UO$r!PO%O!RO~Oe&VOj&TO~PAsOl#sOm#sOq#tOw&XO!l&ZO!m&ZO!n&ZO!o!ii$t!ii$v!ii$m!ii!p!ii$o!ii~P%rOf&[OT!tXc!tX!o!tX#O!tX#R!tX$s!tX$t!tX$v!tX~O$n$_OS%YXT%YXW%YXX%YX_%YXc%YXq%YXu%YX|%YX!S%YX!Z%YX!r%YX!s%YX#T%YX#W%YX#Y%YX#_%YX#a%YX#c%YX#f%YX#h%YX#j%YX#m%YX#s%YX#u%YX#y%YX$O%YX$R%YX$T%YX$m%YX$r%YX$|%YX%S%YX!p%YX!o%YX$t%YX$o%YX~O$r!PO$|&aO~O#]&cO~Ou&dO~O!o#`O#d$wO$t#`O$v#`O~O!o%ZP#d%ZP$t%ZP$v%ZP~P%rO$r!PO~OR#rO!|iXeiX~Oe!wXm!wXu!yX!|!yX~Ou&jO!|&kO~Oe&lOm%PO~Ow$fX!h$fX$t$fX!o$fX$v$fX~P*aOw%QO!h%Va$t%Va!o%Va$v%Va~Om%POw!}a!h!}a$t!}a!o!}a$v!}ae!}a~O!p&xO$r&sO%O&rO~O#v&zOS#tiT#tiW#tiX#ti_#tic#tiq#tiu#ti|#ti!S#ti!Z#ti!r#ti!s#ti#T#ti#W#ti#Y#ti#_#ti#a#ti#c#ti#f#ti#h#ti#j#ti#m#ti#s#ti#u#ti#y#ti$O#ti$R#ti$T#ti$m#ti$r#ti$|#ti%S#ti!p#ti!o#ti$t#ti$o#ti~Oc&|Ow$lX$P$lX~Ow%`O$P%[a~O!o#kO$t#kO$m%Ti!p%Ti$o%Ti~O!o$da$m$da$t$da!p$da$o$da~P`Oq#tOPkiQkilkimkiTkickifki!oki!uki#Oki#Rki$ski$tki$vki!hki#Uki#Zki#]ki#dkiekiSki_kihkijkiokiwkiyki|ki!lki!mki!nki$qki$rki%Oki$mkivki{ki#{ki#|ki!pki$oki~Ol#sOm#sOq#tOP$]aQ$]a~Oe'QO~Ol#sOm#sOq#tOS$YXT$YX_$YXc$YXe$YXf$YXh$YXj$YXo$YXv$YXw$YXy$YX|$YX$q$YX$r$YX%O$YX~Ov'UOw'SOe%PX~P%rOS$}XT$}X_$}Xc$}Xe$}Xf$}Xh$}Xj$}Xl$}Xm$}Xo$}Xq$}Xv$}Xw$}Xy$}X|$}X$q$}X$r$}X%O$}X~Ou'VO~P!%OOe'WO~O$o'YO~Ow'ZOe%RX~P4]Oe']O~Ow'^Oe%QX~P%rOe'`O~Ol#sOm#sOq#tO{'aO~Ou'bOe$}Xl$}Xm$}Xq$}X~Oe'eOj'cO~Ol#sOm#sOq#tOS$cXT$cX_$cXc$cXf$cXh$cXj$cXo$cXw$cXy$cX|$cX!l$cX!m$cX!n$cX!o$cX$q$cX$r$cX$t$cX$v$cX%O$cX$m$cX!p$cX$o$cX~Ow&XO!l'hO!m'hO!n'hO!o!iq$t!iq$v!iq$m!iq!p!iq$o!iq~P%rO$r'iO~O!o#`O#]'nO$t#`O$v#`O~Ou'oO~Ol#sOm#sOq#tOw'qO!o%ZX#d%ZX$t%ZX$v%ZX~O$s'uO~P5oOm%POw$fa!h$fa$t$fa!o$fa$v$fa~Oe'wO~P4]O%O&rOw#pX!h#pX$t#pX~Ow'yO!h!fO$t!gO~O!p'}O$r&sO%O&rO~O#v(POS#tqT#tqW#tqX#tq_#tqc#tqq#tqu#tq|#tq!S#tq!Z#tq!r#tq!s#tq#T#tq#W#tq#Y#tq#_#tq#a#tq#c#tq#f#tq#h#tq#j#tq#m#tq#s#tq#u#tq#y#tq$O#tq$R#tq$T#tq$m#tq$r#tq$|#tq%S#tq!p#tq!o#tq$t#tq$o#tq~O!h!fO#w(QO$t!gO~Ol#sOm#sOq#tO#{(SO#|(SO~Oc(VOe$ZXw$ZX~P=TOw'SOe%Pa~Ol#sOm#sOq#tO{(ZO~Oe$_Xw$_X~P(cOw'ZOe%Ra~Oe$^Xw$^X~P%rOw'^Oe%Qa~Ou'bO~Ol#sOm#sOq#tOS$caT$ca_$cac$caf$cah$caj$cao$caw$cay$ca|$ca!l$ca!m$ca!n$ca!o$ca$q$ca$r$ca$t$ca$v$ca%O$ca$m$ca!p$ca$o$ca~Oe(dOq(bO~Oe(gOm%PO~Ow$hX!o$hX#d$hX$t$hX$v$hX~P%rOw'qO!o%Za#d%Za$t%Za$v%Za~Oe(lO~P%rOe(mO!|(nO~Ov(vOe$Zaw$Za~P%rOu(wO~P!%OOw'SOe%Pi~Ow'SOe%Pi~P%rOe$_aw$_a~P4]Oe$^aw$^a~P%rOl#sOm#sOq#tOw(yOe$bij$bi~Oe(|Oq(bO~Oe)OOm%PO~Ol#sOm#sOq#tOw$ha!o$ha#d$ha$t$ha$v$ha~OS$}Oh$}Oj$}Oy!VO$q!UO$s'uO%O&rO~O#w(QO~Ow'SOe%Pq~Oe)WO~Oe$Zqw$Zq~P%rO%Oql!dl~\",\n  goto: \"=Y%]PPPPPPPPPPP%^%h%h%{P%h&`&cP(UPP)ZP*YP)ZPP)ZP)ZP+f,j-lPPP-xPPPP)Z/S%h/W%hP/^P/d/j/p%hP/v%h/|P%hP%h%hP%h0S0VP1k1}2XPPPPP%^PP2_P2b'w'w2h'w'wP'wP'w'wP%^PP%^P%^PP2qP%^P%^P%^PP%^P%^P%^P2w%^P2z2}3Q3X%^P%^PPP%^PPPP%^PP%^P%^P%^P3^3d3j4Y4h4n4t4z5Q5W5d5j5p5z6Q6W6b6h6n6t6zPPPPPPPPPPPP7Q7T7aP8WP:_:b:eP:h:q:w;T;p;y=S=VanOPqx!f#l$_%fs^OPefqx!a!b!c!d!f#l$_$`%T%f'ZsTOPefqx!a!b!c!d!f#l$_$`%T%f'ZR!OUb^ef!a!b!c!d$`%T'Z`_OPqx!f#l$_%f!x!XVabcdgiruv!Q!T!t#s#t#u$O$a$c$d$e$w%_%b%v%{&Q&X&Y&j'S'V'^'b'q't(Q(S(U(Y(^(w)Ug#Uhlm!u#Q#S$i%P%Q&d'o!x!XVabcdgiruv!Q!T!t#s#t#u$O$a$c$d$e$w%_%b%v%{&Q&X&Y&j'S'V'^'b'q't(Q(S(U(Y(^(w)UQ&b$pR&i$x!y!XVabcdgiruv!Q!T!t#s#t#u$O$a$c$d$e$w%_%b%v%{&Q&X&Y&j'S'V'^'b'q't(Q(S(U(Y(^(w)U!x!XVabcdgiruv!Q!T!t#s#t#u$O$a$c$d$e$w%_%b%v%{&Q&X&Y&j'S'V'^'b'q't(Q(S(U(Y(^(w)UU$}#Q&k(nU&u%Y&w'yR'x&t!x!XVabcdgiruv!Q!T!t#s#t#u$O$a$c$d$e$w%_%b%v%{&Q&X&Y&j'S'V'^'b'q't(Q(S(U(Y(^(w)UV$}#Q&k(n#P!YVabcdgiruv!Q!T!t#Q#s#t#u$O$a$c$d$e$w%_%b%v%{&Q&X&Y&j&k'S'V'^'b'q't(Q(S(U(Y(^(n(w)UQ$P!YQ&_$lQ&`$oR(e'n!x!XVabcdgiruv!Q!T!t#s#t#u$O$a$c$d$e$w%_%b%v%{&Q&X&Y&j'S'V'^'b'q't(Q(S(U(Y(^(w)UQ#YjU$}#Q&k(nR%X#ZT#{!W#|Q![WR$Q!]Q!kYR$R!^Q$R!mR%y$TQ!lYR$S!^Q$R!lR%y$SQ!oZR$U!_Q!q[R$V!`R!s]Q!hXQ!|fQ$]!eQ$f!tQ$k!vQ$m!wQ$r!{Q%U#VQ%[#^Q%]#_Q%^#cQ%c#gQ'l&_Q'{&vQ(R&zQ(T'OQ(q'zQ(s(PQ)P(gQ)S(tQ)T(uR)V)OSpOqUyP!f$_Q#jxQ%g#lR'P%fa`OPqx!f#l$_%fQ$f!tR(a'bR$i!uQ'j&[R(z(bQ${#QQ'v&kR)R(nQ&b$pR's&iR#ZjR#]kR%Z#]S&v%Y&wR(o'yV&t%Y&w'yQ#o{R%i#oQqOR#bqQ%v$OQ&Q$a^'R%v&Q't(U(Y(^)UQ't&jQ(U'SQ(Y'VQ(^'^R)U(wQ'T%vU(W'T(X(xQ(X'UR(x(YQ#|!WR%s#|Q#v!SR%o#vQ'_&QR(_'_Q'[&OR(]'[Q!eXR$[!eUxP!f$_S#ix%fR%f#lQ&U$dR'd&UQ&Y$eR'g&YQ#myQ%e#jT%h#m%eQ(c'jR({(cQ%R#RR&o%RQ$u#OS&e$u(jR(j'sQ'r&gR(i'rQ&w%YR'|&wQ'z&vR(p'zQ&y%^R(O&yQ%a#eR&}%aR|QSoOq]wPx!f#l$_%f`XOPqx!f#l$_%fQ!zeQ!{fQ$W!aQ$X!bQ$Y!cQ$Z!dQ&O$`Q&p%TR(['ZQ!SVQ!uaQ!vbQ!wcQ!xdQ#OgQ#WiQ#crQ#guQ#hvS#q!Q$dQ#x!TQ$e!tQ%l#sQ%m#tQ%n#ul%u$O$a%v&Q&j'S'V'^'t(U(Y(^(w)UQ&S$cS&W$e&YQ&g$wQ&{%_Q'O%bQ'X%{Q'f&XQ(`'bQ(h'qQ(t(QR(u(SR%x$OR&R$aR&P$`QzPQ$^!fR%}$_X#ly#j#m%eQ#VhQ#_mQ$h!uR&^$iW#Rhm!u$iQ#^lQ$|#QQ%S#SQ&m%PQ&n%QQ'p&dR(f'oQ%O#QQ'v&kR)R(nQ#apQ$k!vQ$n!xQ$q!zQ$v#OQ%V#WQ%W#YQ%]#_Q%d#hQ&]$hQ&f$uQ&q%XQ'k&^Q'l&_S'm&`&bQ(k'sQ(}(eR)Q(jR&h$wR#ft\",\n  nodeNames: \"⚠ InterpolationEnd InterpolationContinue Unit VariableName InterpolationStart LineComment Comment IndentedMixin IndentedInclude StyleSheet RuleSet UniversalSelector TagSelector TagName NestingSelector SuffixedSelector Suffix Interpolation SassVariableName ValueName ) ( ParenthesizedValue ColorLiteral NumberLiteral StringLiteral BinaryExpression BinOp LogicOp UnaryExpression LogicOp NamespacedValue . CallExpression Callee ArgList : ... , CallLiteral CallTag ParenthesizedContent ] [ LineNames LineName ClassSelector ClassName PseudoClassSelector :: PseudoClassName PseudoClassName ArgList PseudoClassName ArgList IdSelector # IdName AttributeSelector AttributeName MatchOp ChildSelector ChildOp DescendantSelector SiblingSelector SiblingOp PlaceholderSelector ClassName Block { Declaration PropertyName Map Important Global Default ; } ImportStatement AtKeyword import Layer layer LayerName KeywordQuery FeatureQuery FeatureName BinaryQuery ComparisonQuery CompareOp UnaryQuery LogicOp ParenthesizedQuery SelectorQuery selector IncludeStatement include Keyword MixinStatement mixin UseStatement use Keyword Star Keyword ExtendStatement extend RootStatement at-root ForwardStatement forward Keyword MediaStatement media CharsetStatement charset NamespaceStatement namespace NamespaceName KeyframesStatement keyframes KeyframeName KeyframeList KeyframeSelector KeyframeRangeName SupportsStatement supports IfStatement ControlKeyword ControlKeyword Keyword ForStatement ControlKeyword Keyword Keyword Keyword EachStatement ControlKeyword Keyword WhileStatement ControlKeyword OutputStatement ControlKeyword AtRule Styles\",\n  maxTerm: 196,\n  context: trackIndent,\n  nodeProps: [\n    [\"openedBy\", 1,\"InterpolationStart\",5,\"InterpolationEnd\",21,\"(\",43,\"[\",78,\"{\"],\n    [\"isolate\", -3,6,7,26,\"\"],\n    [\"closedBy\", 22,\")\",44,\"]\",70,\"}\"]\n  ],\n  propSources: [cssHighlighting],\n  skippedNodes: [0,6,7,146],\n  repeatNodeCount: 21,\n  tokenData: \"!$Q~RyOq#rqr$jrs0jst2^tu8{uv;hvw;{wx<^xy={yz>^z{>c{|>||}Co}!ODQ!O!PDo!P!QFY!Q![Fk![!]Gf!]!^Hb!^!_Hs!_!`Is!`!aJ^!a!b#r!b!cKa!c!}#r!}#OMn#O#P#r#P#QNP#Q#RNb#R#T#r#T#UNw#U#c#r#c#d!!Y#d#o#r#o#p!!o#p#qNb#q#r!#Q#r#s!#c#s;'S#r;'S;=`!#z<%lO#rW#uSOy$Rz;'S$R;'S;=`$d<%lO$RW$WSzWOy$Rz;'S$R;'S;=`$d<%lO$RW$gP;=`<%l$RY$m[Oy$Rz!_$R!_!`%c!`#W$R#W#X%v#X#Z$R#Z#[)Z#[#]$R#]#^,V#^;'S$R;'S;=`$d<%lO$RY%jSzWlQOy$Rz;'S$R;'S;=`$d<%lO$RY%{UzWOy$Rz#X$R#X#Y&_#Y;'S$R;'S;=`$d<%lO$RY&dUzWOy$Rz#Y$R#Y#Z&v#Z;'S$R;'S;=`$d<%lO$RY&{UzWOy$Rz#T$R#T#U'_#U;'S$R;'S;=`$d<%lO$RY'dUzWOy$Rz#i$R#i#j'v#j;'S$R;'S;=`$d<%lO$RY'{UzWOy$Rz#`$R#`#a(_#a;'S$R;'S;=`$d<%lO$RY(dUzWOy$Rz#h$R#h#i(v#i;'S$R;'S;=`$d<%lO$RY(}S!nQzWOy$Rz;'S$R;'S;=`$d<%lO$RY)`UzWOy$Rz#`$R#`#a)r#a;'S$R;'S;=`$d<%lO$RY)wUzWOy$Rz#c$R#c#d*Z#d;'S$R;'S;=`$d<%lO$RY*`UzWOy$Rz#U$R#U#V*r#V;'S$R;'S;=`$d<%lO$RY*wUzWOy$Rz#T$R#T#U+Z#U;'S$R;'S;=`$d<%lO$RY+`UzWOy$Rz#`$R#`#a+r#a;'S$R;'S;=`$d<%lO$RY+yS!mQzWOy$Rz;'S$R;'S;=`$d<%lO$RY,[UzWOy$Rz#a$R#a#b,n#b;'S$R;'S;=`$d<%lO$RY,sUzWOy$Rz#d$R#d#e-V#e;'S$R;'S;=`$d<%lO$RY-[UzWOy$Rz#c$R#c#d-n#d;'S$R;'S;=`$d<%lO$RY-sUzWOy$Rz#f$R#f#g.V#g;'S$R;'S;=`$d<%lO$RY.[UzWOy$Rz#h$R#h#i.n#i;'S$R;'S;=`$d<%lO$RY.sUzWOy$Rz#T$R#T#U/V#U;'S$R;'S;=`$d<%lO$RY/[UzWOy$Rz#b$R#b#c/n#c;'S$R;'S;=`$d<%lO$RY/sUzWOy$Rz#h$R#h#i0V#i;'S$R;'S;=`$d<%lO$RY0^S!lQzWOy$Rz;'S$R;'S;=`$d<%lO$R~0mWOY0jZr0jrs1Vs#O0j#O#P1[#P;'S0j;'S;=`2W<%lO0j~1[Oj~~1_RO;'S0j;'S;=`1h;=`O0j~1kXOY0jZr0jrs1Vs#O0j#O#P1[#P;'S0j;'S;=`2W;=`<%l0j<%lO0j~2ZP;=`<%l0jZ2cY!ZPOy$Rz!Q$R!Q![3R![!c$R!c!i3R!i#T$R#T#Z3R#Z;'S$R;'S;=`$d<%lO$RY3WYzWOy$Rz!Q$R!Q![3v![!c$R!c!i3v!i#T$R#T#Z3v#Z;'S$R;'S;=`$d<%lO$RY3{YzWOy$Rz!Q$R!Q![4k![!c$R!c!i4k!i#T$R#T#Z4k#Z;'S$R;'S;=`$d<%lO$RY4rYhQzWOy$Rz!Q$R!Q![5b![!c$R!c!i5b!i#T$R#T#Z5b#Z;'S$R;'S;=`$d<%lO$RY5iYhQzWOy$Rz!Q$R!Q![6X![!c$R!c!i6X!i#T$R#T#Z6X#Z;'S$R;'S;=`$d<%lO$RY6^YzWOy$Rz!Q$R!Q![6|![!c$R!c!i6|!i#T$R#T#Z6|#Z;'S$R;'S;=`$d<%lO$RY7TYhQzWOy$Rz!Q$R!Q![7s![!c$R!c!i7s!i#T$R#T#Z7s#Z;'S$R;'S;=`$d<%lO$RY7xYzWOy$Rz!Q$R!Q![8h![!c$R!c!i8h!i#T$R#T#Z8h#Z;'S$R;'S;=`$d<%lO$RY8oShQzWOy$Rz;'S$R;'S;=`$d<%lO$R_9O`Oy$Rz}$R}!O:Q!O!Q$R!Q![:Q![!_$R!_!`;T!`!c$R!c!}:Q!}#R$R#R#S:Q#S#T$R#T#o:Q#o;'S$R;'S;=`$d<%lO$RZ:X^zWcROy$Rz}$R}!O:Q!O!Q$R!Q![:Q![!c$R!c!}:Q!}#R$R#R#S:Q#S#T$R#T#o:Q#o;'S$R;'S;=`$d<%lO$R[;[S!_SzWOy$Rz;'S$R;'S;=`$d<%lO$RZ;oS%SPlQOy$Rz;'S$R;'S;=`$d<%lO$RZ<QS_ROy$Rz;'S$R;'S;=`$d<%lO$R~<aWOY<^Zw<^wx1Vx#O<^#O#P<y#P;'S<^;'S;=`=u<%lO<^~<|RO;'S<^;'S;=`=V;=`O<^~=YXOY<^Zw<^wx1Vx#O<^#O#P<y#P;'S<^;'S;=`=u;=`<%l<^<%lO<^~=xP;=`<%l<^Z>QSfROy$Rz;'S$R;'S;=`$d<%lO$R~>cOe~_>jU$|PlQOy$Rz!_$R!_!`;T!`;'S$R;'S;=`$d<%lO$RZ?TWlQ!dPOy$Rz!O$R!O!P?m!P!Q$R!Q![Br![;'S$R;'S;=`$d<%lO$RZ?rUzWOy$Rz!Q$R!Q![@U![;'S$R;'S;=`$d<%lO$RZ@]YzW%OROy$Rz!Q$R!Q![@U![!g$R!g!h@{!h#X$R#X#Y@{#Y;'S$R;'S;=`$d<%lO$RZAQYzWOy$Rz{$R{|Ap|}$R}!OAp!O!Q$R!Q![BX![;'S$R;'S;=`$d<%lO$RZAuUzWOy$Rz!Q$R!Q![BX![;'S$R;'S;=`$d<%lO$RZB`UzW%OROy$Rz!Q$R!Q![BX![;'S$R;'S;=`$d<%lO$RZBy[zW%OROy$Rz!O$R!O!P@U!P!Q$R!Q![Br![!g$R!g!h@{!h#X$R#X#Y@{#Y;'S$R;'S;=`$d<%lO$RZCtSwROy$Rz;'S$R;'S;=`$d<%lO$RZDVWlQOy$Rz!O$R!O!P?m!P!Q$R!Q![Br![;'S$R;'S;=`$d<%lO$RZDtWqROy$Rz!O$R!O!PE^!P!Q$R!Q![@U![;'S$R;'S;=`$d<%lO$RYEcUzWOy$Rz!O$R!O!PEu!P;'S$R;'S;=`$d<%lO$RYE|SvQzWOy$Rz;'S$R;'S;=`$d<%lO$RYF_SlQOy$Rz;'S$R;'S;=`$d<%lO$RZFp[%OROy$Rz!O$R!O!P@U!P!Q$R!Q![Br![!g$R!g!h@{!h#X$R#X#Y@{#Y;'S$R;'S;=`$d<%lO$RkGkUucOy$Rz![$R![!]G}!];'S$R;'S;=`$d<%lO$RXHUS!SPzWOy$Rz;'S$R;'S;=`$d<%lO$RZHgS!oROy$Rz;'S$R;'S;=`$d<%lO$RjHzU!|`lQOy$Rz!_$R!_!`I^!`;'S$R;'S;=`$d<%lO$RjIgS!|`zWlQOy$Rz;'S$R;'S;=`$d<%lO$RnIzU!|`!_SOy$Rz!_$R!_!`%c!`;'S$R;'S;=`$d<%lO$RkJgV!aP!|`lQOy$Rz!_$R!_!`I^!`!aJ|!a;'S$R;'S;=`$d<%lO$RXKTS!aPzWOy$Rz;'S$R;'S;=`$d<%lO$RXKdYOy$Rz}$R}!OLS!O!c$R!c!}Lq!}#T$R#T#oLq#o;'S$R;'S;=`$d<%lO$RXLXWzWOy$Rz!c$R!c!}Lq!}#T$R#T#oLq#o;'S$R;'S;=`$d<%lO$RXLx[!rPzWOy$Rz}$R}!OLq!O!Q$R!Q![Lq![!c$R!c!}Lq!}#T$R#T#oLq#o;'S$R;'S;=`$d<%lO$RZMsS|ROy$Rz;'S$R;'S;=`$d<%lO$R_NUS{VOy$Rz;'S$R;'S;=`$d<%lO$R[NeUOy$Rz!_$R!_!`;T!`;'S$R;'S;=`$d<%lO$RkNzUOy$Rz#b$R#b#c! ^#c;'S$R;'S;=`$d<%lO$Rk! cUzWOy$Rz#W$R#W#X! u#X;'S$R;'S;=`$d<%lO$Rk! |SmczWOy$Rz;'S$R;'S;=`$d<%lO$Rk!!]UOy$Rz#f$R#f#g! u#g;'S$R;'S;=`$d<%lO$RZ!!tS!hROy$Rz;'S$R;'S;=`$d<%lO$RZ!#VS!pROy$Rz;'S$R;'S;=`$d<%lO$R]!#hU!dPOy$Rz!_$R!_!`;T!`;'S$R;'S;=`$d<%lO$RW!#}P;=`<%l#r\",\n  tokenizers: [indentation, descendant, interpolationEnd, unitToken, identifiers, spaces, comments, indentedMixins, 0, 1, 2, 3, 4],\n  topRules: {\"StyleSheet\":[0,10],\"Styles\":[1,145]},\n  dialects: {indented: 0},\n  specialized: [{term: 172, get: (value) => spec_identifier[value] || -1},{term: 171, get: (value) => spec_callee[value] || -1},{term: 80, get: (value) => spec_AtKeyword[value] || -1},{term: 173, get: (value) => spec_queryIdentifier[value] || -1}],\n  tokenPrec: 3217\n});\n\nexport { parser };\n", "import { parser } from '@lezer/sass';\nimport { LRLanguage, foldNodeProp, foldInside, indentNodeProp, continuedIndent, LanguageSupport } from '@codemirror/language';\nimport { defineCSSCompletionSource } from '@codemirror/lang-css';\n\n/**\nA language provider based on the [Lezer Sass\nparser](https://github.com/lezer-parser/sass), extended with\nhighlighting and indentation information.\n*/\nconst sassLanguage = /*@__PURE__*/LRLanguage.define({\n    name: \"sass\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/foldNodeProp.add({\n                Block: foldInside,\n                Comment(node, state) {\n                    return { from: node.from + 2, to: state.sliceDoc(node.to - 2, node.to) == \"*/\" ? node.to - 2 : node.to };\n                }\n            }),\n            /*@__PURE__*/indentNodeProp.add({\n                Declaration: /*@__PURE__*/continuedIndent()\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"/*\", close: \"*/\" }, line: \"//\" },\n        indentOnInput: /^\\s*\\}$/,\n        wordChars: \"$-\"\n    }\n});\nconst indentedSassLanguage = /*@__PURE__*/sassLanguage.configure({\n    dialect: \"indented\",\n    props: [\n        /*@__PURE__*/indentNodeProp.add({\n            \"Block RuleSet\": cx => cx.baseIndent + cx.unit\n        }),\n        /*@__PURE__*/foldNodeProp.add({\n            Block: node => ({ from: node.from, to: node.to })\n        })\n    ]\n});\n/**\nProperty, variable, $-variable, and value keyword completion\nsource.\n*/\nconst sassCompletionSource = /*@__PURE__*/defineCSSCompletionSource(node => node.name == \"VariableName\" || node.name == \"SassVariableName\");\n/**\nLanguage support for CSS.\n*/\nfunction sass(config) {\n    return new LanguageSupport((config === null || config === void 0 ? void 0 : config.indented) ? indentedSassLanguage : sassLanguage, sassLanguage.data.of({ autocomplete: sassCompletionSource }));\n}\n\nexport { sass, sassCompletionSource, sassLanguage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA,IAAM,SAAS;AAAf,IACE,SAAS;AADX,IAEE,eAAe;AAFjB,IAGE,mBAAmB;AAHrB,IAIE,wBAAwB;AAJ1B,IAKE,OAAO;AALT,IAME,SAAS;AANX,IAOE,aAAa;AAPf,IAQE,eAAe;AARjB,IASE,kBAAkB;AATpB,IAUE,qBAAqB;AAVvB,IAWE,UAAU;AAXZ,IAYE,iBAAiB;AAZnB,IAaE,MAAM;AAbR,IAcE,aAAa;AAdf,IAeE,cAAc;AAfhB,IAgBE,UAAU;AAhBZ,IAiBE,gBAAgB;AAjBlB,IAkBE,kBAAkB;AAlBpB,IAmBE,mBAAmB;AAKrB,IAAM,QAAQ;AAAA,EAAC;AAAA,EAAG;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAI;AAAA,EAAK;AAAA,EAAK;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EACrE;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAA,EAAM;AAAK;AAC1E,IAAM,QAAQ;AAAd,IAAkB,SAAS;AAA3B,IAA+B,aAAa;AAA5C,IAAgD,WAAW;AAA3D,IAA+D,OAAO;AAAtE,IAA0E,SAAS;AAAnF,IACM,OAAO;AADb,IACiB,UAAU;AAD3B,IAC+B,SAAS;AADxC,IAC6C,SAAS;AADtD,IAC2D,QAAQ;AADnE,IACuE,WAAW;AADlF,IAEM,cAAc;AAFpB,IAEwB,SAAS;AAFjC,IAEqC,OAAO;AAF5C,IAEgD,MAAM;AAEtD,SAAS,QAAQ,IAAI;AAAE,SAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM;AAAI;AAEzF,SAAS,QAAQ,IAAI;AAAE,SAAO,MAAM,MAAM,MAAM;AAAG;AAEnD,SAAS,eAAe,OAAO;AAC7B,MAAI;AACJ,SAAO,MAAM,QAAQ,WAAW,OAAO,MAAM,KAAK,CAAC,MAAM,SAAS,QAAQ;AAC5E;AAEA,IAAM,SAAS,IAAI,kBAAkB,CAAC,OAAO,UAAU;AACrD,MAAI,MAAM,eAAe,gBAAgB,GAAG;AAC1C,QAAI;AACJ,QAAI,MAAM,OAAO,KAAK,MAAM,SAAS,GAAG,GAAG;AACzC,YAAM,YAAY,GAAG;AAAA,IACvB,aAAa,OAAO,MAAM,KAAK,EAAE,MAAM,eAAe,OAAO,MAAM,MAAM,SAAS,cAAc,GAAG;AACjG,UAAIA,UAAS;AACb,aAAO,MAAM,QAAQ,eAAe,MAAM,SAAS,MAAM,IAAI,GAAG;AAAE,cAAM,QAAQ;AAAG,QAAAA;AAAA,MAAU;AAC7F,UAAI,MAAM,QAAQ,eAAe,eAAe,KAAK;AACnD,cAAM,YAAY,gBAAgB,CAACA,OAAM;AAAA,eAClCA;AACP,cAAM,YAAY,UAAU;AAAA,IAChC,WAAW,MAAM,QAAQ,aAAa;AACpC,YAAM,YAAY,SAAS,CAAC;AAAA,IAC9B,WAAW,MAAM,SAAS,MAAM,IAAI,GAAG;AACrC,YAAM,QAAQ;AACd,aAAO,MAAM,QAAQ,eAAe,MAAM,SAAS,MAAM,IAAI,EAAG,OAAM,QAAQ;AAC9E,YAAM,YAAY,UAAU;AAAA,IAC9B;AAAA,EACF,OAAO;AACL,QAAI,SAAS;AACb,WAAO,MAAM,SAAS,MAAM,IAAI,GAAG;AACjC,YAAM,QAAQ;AACd;AAAA,IACF;AACA,QAAI,OAAQ,OAAM,YAAY,UAAU;AAAA,EAC1C;AACF,GAAG,EAAC,YAAY,KAAI,CAAC;AAErB,IAAM,WAAW,IAAI,kBAAkB,CAAC,OAAO,UAAU;AACvD,MAAI,CAAC,eAAe,KAAK,EAAG;AAC5B,QAAM,QAAQ;AACd,MAAI,MAAM,eAAe,gBAAgB,GAAG;AAC1C,QAAI,kBAAkB;AACtB,aAAS,MAAM,KAAI,OAAO;AACxB,UAAI,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC;AAC9B,UAAI,QAAQ,eAAe,OAAO,GAAG;AACnC,0BAAkB,MAAM;AACxB;AAAA,MACF,WAAW,CAAC,MAAM,SAAS,IAAI,GAAG;AAChC;AAAA,MACF;AAAA,IACF;AACA,QAAI,kBAAkB,IAAI;AACxB,UAAI,QAAQ,MAAM,QAAQ,UAAU,MAAM;AAC1C,YAAM,QAAQ;AACd,aAAO,MAAM,QAAQ,GAAG;AACtB,YAAI,MAAM,QAAQ,aAAa;AAC7B,gBAAM,QAAQ;AACd,cAAI,WAAW;AACf,iBAAO,MAAM,QAAQ,eAAe,MAAM,SAAS,MAAM,IAAI,GAAG;AAC9D;AACA,kBAAM,QAAQ;AAAA,UAChB;AACA,cAAI,WAAW,iBAAiB;AAC9B,kBAAM,CAAC,WAAW;AAClB;AAAA,UACF;AAAA,QACF,WAAW,SAAS,MAAM,QAAQ,YAAY,MAAM,KAAK,CAAC,KAAK,OAAO;AACpE,gBAAM;AACN;AAAA,QACF,OAAO;AACL,gBAAM,QAAQ;AAAA,QAChB;AAAA,MACF;AACA,YAAM,YAAY,QAAQ,UAAU,aAAa,GAAG;AACpD;AAAA,IACF;AAAA,EACF;AACA,MAAI,MAAM,QAAQ,OAAO;AACvB,WAAO,MAAM,QAAQ,eAAe,MAAM,QAAQ,EAAG,OAAM,QAAQ;AACnE,UAAM,YAAY,WAAW;AAAA,EAC/B,OAAO;AACL,UAAM,QAAQ;AACd,WAAO,MAAM,QAAQ,GAAG;AACtB,UAAI,EAAC,KAAI,IAAI;AACb,YAAM,QAAQ;AACd,UAAI,QAAQ,YAAY,MAAM,QAAQ,OAAO;AAC3C,cAAM,QAAQ;AACd;AAAA,MACF;AAAA,IACF;AACA,UAAM,YAAY,OAAO;AAAA,EAC3B;AACF,CAAC;AAED,IAAM,iBAAiB,IAAI,kBAAkB,CAAC,OAAO,UAAU;AAC7D,OAAK,MAAM,QAAQ,QAAQ,MAAM,QAAQ,WAAW,MAAM,eAAe,gBAAgB;AACvF,UAAM,YAAY,MAAM,QAAQ,SAAS,gBAAgB,iBAAiB,CAAC;AAC/E,CAAC;AAED,IAAM,cAAc,IAAI,kBAAkB,CAAC,OAAO,UAAU;AAC1D,MAAI,CAAC,MAAM,eAAe,gBAAgB,EAAG;AAC7C,MAAI,SAAS,MAAM,QAAQ;AAC3B,MAAI,MAAM,OAAO,KAAK,QAAQ;AAC5B,UAAM,YAAY,MAAM;AACxB;AAAA,EACF;AACA,MAAI,OAAO,MAAM,KAAK,EAAE;AACxB,MAAI,QAAQ,aAAa;AACvB,QAAI,QAAQ;AACZ,WAAO,MAAM,QAAQ,eAAe,MAAM,SAAS,MAAM,IAAI,GAAG;AAC9D,YAAM,QAAQ;AACd;AAAA,IACF;AACA,QAAI,SAAS,UACT,MAAM,QAAQ,eAAe,CAAC,eAAe,KAAK,GAAG;AACvD,UAAI,QAAQ,OAAQ,OAAM,YAAY,QAAQ,CAAC,KAAK;AAAA,UAC/C,OAAM,YAAY,MAAM;AAAA,IAC/B;AAAA,EACF;AACF,CAAC;AAED,IAAM,cAAc,IAAI,kBAAkB,CAAC,OAAO,UAAU;AAC1D,WAAS,SAAS,OAAO,SAAS,GAAG,IAAI,KAAI,KAAK;AAChD,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,IAAI,KAAK,QAAQ,QAAQ,QAAQ,cAAe,UAAU,QAAQ,IAAI,GAAI;AACpF,UAAI,CAAC,WAAW,QAAQ,QAAQ,IAAI,GAAI,UAAS;AACjD,UAAI,WAAW,KAAK,QAAQ,KAAM;AAClC,YAAM,QAAQ;AAAA,IAChB,WAAW,QAAQ,QAAQ,MAAM,KAAK,CAAC,KAAK,QAAQ;AAClD,YAAM,YAAY,oBAAoB,CAAC;AACvC;AAAA,IACF,OAAO;AACL,UAAI,OAAQ,OAAM;AAAA,QAChB,UAAU,KAAK,MAAM,SAAS,YAAY,IAAI,eAC1C,MAAM,SAAS,eAAe,IAAI,kBAClC,QAAQ,SAAS,SACjB;AAAA,MAAU;AAChB;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAM,mBAAmB,IAAI,kBAAkB,WAAS;AACtD,MAAI,MAAM,QAAQ,QAAQ;AACxB,UAAM,QAAQ;AACd,WAAO,QAAQ,MAAM,IAAI,KAAK,MAAM,QAAQ,QAAQ,MAAM,QAAQ,cAAc,QAAQ,MAAM,IAAI;AAChG,YAAM,QAAQ;AAChB,QAAI,MAAM,QAAQ,QAAQ,MAAM,KAAK,CAAC,KAAK;AACzC,YAAM,YAAY,uBAAuB,CAAC;AAAA;AAE1C,YAAM,YAAY,gBAAgB;AAAA,EACtC;AACF,CAAC;AAED,IAAM,aAAa,IAAI,kBAAkB,WAAS;AAChD,MAAI,MAAM,SAAS,MAAM,KAAK,EAAE,CAAC,GAAG;AAClC,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,IAAI,KAAK,QAAQ,cAAc,QAAQ,QAAQ,QAAQ,UAC/D,QAAQ,YAAY,QAAQ,SAAS,QAAQ,MAAM,KAAK,CAAC,CAAC,KAAK,QAAQ,QAAQ,QAAQ,OAAO,QAAQ;AACxG,YAAM,YAAY,YAAY;AAAA,EAClC;AACF,CAAC;AAED,IAAM,YAAY,IAAI,kBAAkB,WAAS;AAC/C,MAAI,CAAC,MAAM,SAAS,MAAM,KAAK,EAAE,CAAC,GAAG;AACnC,QAAI,EAAC,KAAI,IAAI;AACb,QAAI,QAAQ,SAAS;AAAE,YAAM,QAAQ;AAAG,YAAM,YAAY,IAAI;AAAA,IAAG;AACjE,QAAI,QAAQ,IAAI,GAAG;AACjB,SAAG;AAAE,cAAM,QAAQ;AAAA,MAAG,SAAS,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI;AACxE,YAAM,YAAY,IAAI;AAAA,IACxB;AAAA,EACF;AACF,CAAC;AAED,SAAS,YAAY,QAAQ,OAAO;AAClC,OAAK,SAAS;AACd,OAAK,QAAQ;AACb,OAAK,QAAQ,SAAS,OAAO,OAAO,OAAO,QAAQ,IAAI,KAAK,SAAS,SAAS;AAChF;AAEA,IAAM,YAAY,IAAI,YAAY,MAAM,CAAC;AAEzC,IAAM,cAAc,IAAI,eAAe;AAAA,EACrC,OAAO;AAAA,EACP,MAAM,SAAS,MAAM,OAAO,OAAO;AACjC,QAAI,QAAQ,OAAQ,QAAO,IAAI,YAAY,SAAS,MAAM,MAAM,MAAM,GAAG;AACzE,QAAI,QAAQ,OAAQ,QAAO,QAAQ;AACnC,WAAO;AAAA,EACT;AAAA,EACA,KAAK,SAAS;AAAE,WAAO,QAAQ;AAAA,EAAK;AACtC,CAAC;AAED,IAAM,kBAAkB,UAAU;AAAA,EAChC,wGAAwG,KAAK;AAAA,EAC7G,oBAAoB,KAAK;AAAA,EACzB,kBAAkB,KAAK;AAAA,EACvB,eAAe,KAAK;AAAA,EACpB,cAAc,KAAK;AAAA,EACnB,mBAAmB,KAAK;AAAA,EACxB,SAAS,KAAK;AAAA,EACd,oBAAoB,KAAK;AAAA,EACzB,iBAAiB,KAAK,SAAS,KAAK,SAAS;AAAA,EAC7C,QAAQ,KAAK;AAAA,EACb,4BAA4B,KAAK;AAAA,EACjC,eAAe,KAAK;AAAA,EACpB,eAAe,KAAK;AAAA,EACpB,cAAc,KAAK;AAAA,EACnB,cAAc,KAAK;AAAA,EACnB,qBAAqB,KAAK;AAAA,EAC1B,cAAc,KAAK;AAAA,EACnB,kBAAkB,KAAK,QAAQ,KAAK,YAAY;AAAA,EAChD,QAAQ,KAAK;AAAA,EACb,MAAM,KAAK;AAAA,EACX,mEAAmE,KAAK;AAAA,EACxE,SAAS,KAAK;AAAA,EACd,8BAA8B,KAAK;AAAA,EACnC,OAAO,KAAK;AAAA,EACZ,4BAA4B,KAAK;AAAA,EACjC,SAAS,KAAK;AAAA,EACd,aAAa,KAAK;AAAA,EAClB,cAAc,KAAK;AAAA,EACnB,sCAAsC,KAAK;AAAA,EAC3C,6DAA6D,KAAK;AAAA,EAClE,WAAa,KAAK;AAAA,EAClB,cAAc,KAAK;AAAA,EACnB,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AAAA,EACZ,OAAO,KAAK;AACd,CAAC;AAGD,IAAM,kBAAkB,EAAC,WAAU,MAAK,KAAI,IAAI,OAAM,KAAK,IAAG,KAAK,MAAK,KAAK,SAAQ,KAAK,MAAK,KAAK,MAAK,KAAK,IAAG,KAAK,MAAK,KAAK,IAAG,KAAK,SAAQ,KAAK,IAAG,IAAG;AAC3J,IAAM,cAAc,EAAC,WAAU,MAAK,KAAI,IAAI,cAAa,IAAI,QAAO,IAAI,QAAO,IAAI,MAAK,KAAK,aAAY,KAAK,kBAAiB,KAAK,eAAc,KAAK,oBAAmB,KAAK,KAAI,KAAK,gBAAe,IAAG;AAC1M,IAAM,iBAAiB,EAAC,WAAU,MAAK,WAAU,KAAK,YAAW,KAAK,UAAS,KAAK,aAAY,KAAK,QAAO,KAAK,WAAU,KAAK,YAAW,KAAK,YAAW,KAAK,UAAS,KAAK,YAAW,KAAK,cAAa,KAAK,cAAa,KAAK,aAAY,KAAK,OAAM,KAAK,SAAQ,KAAK,QAAO,KAAK,SAAQ,KAAK,UAAS,KAAK,UAAS,KAAK,SAAQ,KAAK,UAAS,KAAK,WAAU,IAAG;AACxW,IAAM,uBAAuB,EAAC,WAAU,MAAK,OAAM,KAAK,KAAI,KAAK,MAAK,KAAK,UAAS,IAAG;AACvF,IAAM,SAAS,SAAS,YAAY;AAAA,EAClC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,CAAC,YAAY,GAAE,sBAAqB,GAAE,oBAAmB,IAAG,KAAI,IAAG,KAAI,IAAG,GAAG;AAAA,IAC7E,CAAC,WAAW,IAAG,GAAE,GAAE,IAAG,EAAE;AAAA,IACxB,CAAC,YAAY,IAAG,KAAI,IAAG,KAAI,IAAG,GAAG;AAAA,EACnC;AAAA,EACA,aAAa,CAAC,eAAe;AAAA,EAC7B,cAAc,CAAC,GAAE,GAAE,GAAE,GAAG;AAAA,EACxB,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY,CAAC,aAAa,YAAY,kBAAkB,WAAW,aAAa,QAAQ,UAAU,gBAAgB,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAC/H,UAAU,EAAC,cAAa,CAAC,GAAE,EAAE,GAAE,UAAS,CAAC,GAAE,GAAG,EAAC;AAAA,EAC/C,UAAU,EAAC,UAAU,EAAC;AAAA,EACtB,aAAa,CAAC,EAAC,MAAM,KAAK,KAAK,CAAC,UAAU,gBAAgB,KAAK,KAAK,GAAE,GAAE,EAAC,MAAM,KAAK,KAAK,CAAC,UAAU,YAAY,KAAK,KAAK,GAAE,GAAE,EAAC,MAAM,IAAI,KAAK,CAAC,UAAU,eAAe,KAAK,KAAK,GAAE,GAAE,EAAC,MAAM,KAAK,KAAK,CAAC,UAAU,qBAAqB,KAAK,KAAK,GAAE,CAAC;AAAA,EACpP,WAAW;AACb,CAAC;;;AC5RD,IAAM,eAA4B,WAAW,OAAO;AAAA,EAChD,MAAM;AAAA,EACN,QAAqB,OAAO,UAAU;AAAA,IAClC,OAAO;AAAA,MACU,aAAa,IAAI;AAAA,QAC1B,OAAO;AAAA,QACP,QAAQ,MAAM,OAAO;AACjB,iBAAO,EAAE,MAAM,KAAK,OAAO,GAAG,IAAI,MAAM,SAAS,KAAK,KAAK,GAAG,KAAK,EAAE,KAAK,OAAO,KAAK,KAAK,IAAI,KAAK,GAAG;AAAA,QAC3G;AAAA,MACJ,CAAC;AAAA,MACY,eAAe,IAAI;AAAA,QAC5B,aAA0B,gBAAgB;AAAA,MAC9C,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAAA,EACD,cAAc;AAAA,IACV,eAAe,EAAE,OAAO,EAAE,MAAM,MAAM,OAAO,KAAK,GAAG,MAAM,KAAK;AAAA,IAChE,eAAe;AAAA,IACf,WAAW;AAAA,EACf;AACJ,CAAC;AACD,IAAM,uBAAoC,aAAa,UAAU;AAAA,EAC7D,SAAS;AAAA,EACT,OAAO;AAAA,IACU,eAAe,IAAI;AAAA,MAC5B,iBAAiB,QAAM,GAAG,aAAa,GAAG;AAAA,IAC9C,CAAC;AAAA,IACY,aAAa,IAAI;AAAA,MAC1B,OAAO,WAAS,EAAE,MAAM,KAAK,MAAM,IAAI,KAAK,GAAG;AAAA,IACnD,CAAC;AAAA,EACL;AACJ,CAAC;AAKD,IAAM,uBAAoC,0BAA0B,UAAQ,KAAK,QAAQ,kBAAkB,KAAK,QAAQ,kBAAkB;AAI1I,SAAS,KAAK,QAAQ;AAClB,SAAO,IAAI,iBAAiB,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,YAAY,uBAAuB,cAAc,aAAa,KAAK,GAAG,EAAE,cAAc,qBAAqB,CAAC,CAAC;AACpM;", "names": ["spaces"]}