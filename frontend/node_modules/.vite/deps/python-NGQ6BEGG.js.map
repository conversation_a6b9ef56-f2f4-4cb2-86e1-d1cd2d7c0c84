{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/python.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nvar wordOperators = wordRegexp([\"and\", \"or\", \"not\", \"is\"]);\nvar commonKeywords = [\"as\", \"assert\", \"break\", \"class\", \"continue\",\n                      \"def\", \"del\", \"elif\", \"else\", \"except\", \"finally\",\n                      \"for\", \"from\", \"global\", \"if\", \"import\",\n                      \"lambda\", \"pass\", \"raise\", \"return\",\n                      \"try\", \"while\", \"with\", \"yield\", \"in\", \"False\", \"True\"];\nvar commonBuiltins = [\"abs\", \"all\", \"any\", \"bin\", \"bool\", \"bytearray\", \"callable\", \"chr\",\n                      \"classmethod\", \"compile\", \"complex\", \"delattr\", \"dict\", \"dir\", \"divmod\",\n                      \"enumerate\", \"eval\", \"filter\", \"float\", \"format\", \"frozenset\",\n                      \"getattr\", \"globals\", \"hasattr\", \"hash\", \"help\", \"hex\", \"id\",\n                      \"input\", \"int\", \"isinstance\", \"issubclass\", \"iter\", \"len\",\n                      \"list\", \"locals\", \"map\", \"max\", \"memoryview\", \"min\", \"next\",\n                      \"object\", \"oct\", \"open\", \"ord\", \"pow\", \"property\", \"range\",\n                      \"repr\", \"reversed\", \"round\", \"set\", \"setattr\", \"slice\",\n                      \"sorted\", \"staticmethod\", \"str\", \"sum\", \"super\", \"tuple\",\n                      \"type\", \"vars\", \"zip\", \"__import__\", \"NotImplemented\",\n                      \"Ellipsis\", \"__debug__\"];\n\nfunction top(state) {\n  return state.scopes[state.scopes.length - 1];\n}\n\nexport function mkPython(parserConf) {\n  var ERRORCLASS = \"error\";\n\n  var delimiters = parserConf.delimiters || parserConf.singleDelimiters || /^[\\(\\)\\[\\]\\{\\}@,:`=;\\.\\\\]/;\n  //               (Backwards-compatibility with old, cumbersome config system)\n  var operators = [parserConf.singleOperators, parserConf.doubleOperators, parserConf.doubleDelimiters, parserConf.tripleDelimiters,\n                   parserConf.operators || /^([-+*/%\\/&|^]=?|[<>=]+|\\/\\/=?|\\*\\*=?|!=|[~!@]|\\.\\.\\.)/]\n  for (var i = 0; i < operators.length; i++) if (!operators[i]) operators.splice(i--, 1)\n\n  var hangingIndent = parserConf.hangingIndent;\n\n  var myKeywords = commonKeywords, myBuiltins = commonBuiltins;\n  if (parserConf.extra_keywords != undefined)\n    myKeywords = myKeywords.concat(parserConf.extra_keywords);\n\n  if (parserConf.extra_builtins != undefined)\n    myBuiltins = myBuiltins.concat(parserConf.extra_builtins);\n\n  var py3 = !(parserConf.version && Number(parserConf.version) < 3)\n  if (py3) {\n    // since http://legacy.python.org/dev/peps/pep-0465/ @ is also an operator\n    var identifiers = parserConf.identifiers|| /^[_A-Za-z\\u00A1-\\uFFFF][_A-Za-z0-9\\u00A1-\\uFFFF]*/;\n    myKeywords = myKeywords.concat([\"nonlocal\", \"None\", \"aiter\", \"anext\", \"async\", \"await\", \"breakpoint\", \"match\", \"case\"]);\n    myBuiltins = myBuiltins.concat([\"ascii\", \"bytes\", \"exec\", \"print\"]);\n    var stringPrefixes = new RegExp(\"^(([rbuf]|(br)|(rb)|(fr)|(rf))?('{3}|\\\"{3}|['\\\"]))\", \"i\");\n  } else {\n    var identifiers = parserConf.identifiers|| /^[_A-Za-z][_A-Za-z0-9]*/;\n    myKeywords = myKeywords.concat([\"exec\", \"print\"]);\n    myBuiltins = myBuiltins.concat([\"apply\", \"basestring\", \"buffer\", \"cmp\", \"coerce\", \"execfile\",\n                                    \"file\", \"intern\", \"long\", \"raw_input\", \"reduce\", \"reload\",\n                                    \"unichr\", \"unicode\", \"xrange\", \"None\"]);\n    var stringPrefixes = new RegExp(\"^(([rubf]|(ur)|(br))?('{3}|\\\"{3}|['\\\"]))\", \"i\");\n  }\n  var keywords = wordRegexp(myKeywords);\n  var builtins = wordRegexp(myBuiltins);\n\n  // tokenizers\n  function tokenBase(stream, state) {\n    var sol = stream.sol() && state.lastToken != \"\\\\\"\n    if (sol) state.indent = stream.indentation()\n    // Handle scope changes\n    if (sol && top(state).type == \"py\") {\n      var scopeOffset = top(state).offset;\n      if (stream.eatSpace()) {\n        var lineOffset = stream.indentation();\n        if (lineOffset > scopeOffset)\n          pushPyScope(stream, state);\n        else if (lineOffset < scopeOffset && dedent(stream, state) && stream.peek() != \"#\")\n          state.errorToken = true;\n        return null;\n      } else {\n        var style = tokenBaseInner(stream, state);\n        if (scopeOffset > 0 && dedent(stream, state))\n          style += \" \" + ERRORCLASS;\n        return style;\n      }\n    }\n    return tokenBaseInner(stream, state);\n  }\n\n  function tokenBaseInner(stream, state, inFormat) {\n    if (stream.eatSpace()) return null;\n\n    // Handle Comments\n    if (!inFormat && stream.match(/^#.*/)) return \"comment\";\n\n    // Handle Number Literals\n    if (stream.match(/^[0-9\\.]/, false)) {\n      var floatLiteral = false;\n      // Floats\n      if (stream.match(/^[\\d_]*\\.\\d+(e[\\+\\-]?\\d+)?/i)) { floatLiteral = true; }\n      if (stream.match(/^[\\d_]+\\.\\d*/)) { floatLiteral = true; }\n      if (stream.match(/^\\.\\d+/)) { floatLiteral = true; }\n      if (floatLiteral) {\n        // Float literals may be \"imaginary\"\n        stream.eat(/J/i);\n        return \"number\";\n      }\n      // Integers\n      var intLiteral = false;\n      // Hex\n      if (stream.match(/^0x[0-9a-f_]+/i)) intLiteral = true;\n      // Binary\n      if (stream.match(/^0b[01_]+/i)) intLiteral = true;\n      // Octal\n      if (stream.match(/^0o[0-7_]+/i)) intLiteral = true;\n      // Decimal\n      if (stream.match(/^[1-9][\\d_]*(e[\\+\\-]?[\\d_]+)?/)) {\n        // Decimal literals may be \"imaginary\"\n        stream.eat(/J/i);\n        // TODO - Can you have imaginary longs?\n        intLiteral = true;\n      }\n      // Zero by itself with no other piece of number.\n      if (stream.match(/^0(?![\\dx])/i)) intLiteral = true;\n      if (intLiteral) {\n        // Integer literals may be \"long\"\n        stream.eat(/L/i);\n        return \"number\";\n      }\n    }\n\n    // Handle Strings\n    if (stream.match(stringPrefixes)) {\n      var isFmtString = stream.current().toLowerCase().indexOf('f') !== -1;\n      if (!isFmtString) {\n        state.tokenize = tokenStringFactory(stream.current(), state.tokenize);\n        return state.tokenize(stream, state);\n      } else {\n        state.tokenize = formatStringFactory(stream.current(), state.tokenize);\n        return state.tokenize(stream, state);\n      }\n    }\n\n    for (var i = 0; i < operators.length; i++)\n      if (stream.match(operators[i])) return \"operator\"\n\n    if (stream.match(delimiters)) return \"punctuation\";\n\n    if (state.lastToken == \".\" && stream.match(identifiers))\n      return \"property\";\n\n    if (stream.match(keywords) || stream.match(wordOperators))\n      return \"keyword\";\n\n    if (stream.match(builtins))\n      return \"builtin\";\n\n    if (stream.match(/^(self|cls)\\b/))\n      return \"self\";\n\n    if (stream.match(identifiers)) {\n      if (state.lastToken == \"def\" || state.lastToken == \"class\")\n        return \"def\";\n      return \"variable\";\n    }\n\n    // Handle non-detected items\n    stream.next();\n    return inFormat ? null :ERRORCLASS;\n  }\n\n  function formatStringFactory(delimiter, tokenOuter) {\n    while (\"rubf\".indexOf(delimiter.charAt(0).toLowerCase()) >= 0)\n      delimiter = delimiter.substr(1);\n\n    var singleline = delimiter.length == 1;\n    var OUTCLASS = \"string\";\n\n    function tokenNestedExpr(depth) {\n      return function(stream, state) {\n        var inner = tokenBaseInner(stream, state, true)\n        if (inner == \"punctuation\") {\n          if (stream.current() == \"{\") {\n            state.tokenize = tokenNestedExpr(depth + 1)\n          } else if (stream.current() == \"}\") {\n            if (depth > 1) state.tokenize = tokenNestedExpr(depth - 1)\n            else state.tokenize = tokenString\n          }\n        }\n        return inner\n      }\n    }\n\n    function tokenString(stream, state) {\n      while (!stream.eol()) {\n        stream.eatWhile(/[^'\"\\{\\}\\\\]/);\n        if (stream.eat(\"\\\\\")) {\n          stream.next();\n          if (singleline && stream.eol())\n            return OUTCLASS;\n        } else if (stream.match(delimiter)) {\n          state.tokenize = tokenOuter;\n          return OUTCLASS;\n        } else if (stream.match('{{')) {\n          // ignore {{ in f-str\n          return OUTCLASS;\n        } else if (stream.match('{', false)) {\n          // switch to nested mode\n          state.tokenize = tokenNestedExpr(0)\n          if (stream.current()) return OUTCLASS;\n          else return state.tokenize(stream, state)\n        } else if (stream.match('}}')) {\n          return OUTCLASS;\n        } else if (stream.match('}')) {\n          // single } in f-string is an error\n          return ERRORCLASS;\n        } else {\n          stream.eat(/['\"]/);\n        }\n      }\n      if (singleline) {\n        if (parserConf.singleLineStringErrors)\n          return ERRORCLASS;\n        else\n          state.tokenize = tokenOuter;\n      }\n      return OUTCLASS;\n    }\n    tokenString.isString = true;\n    return tokenString;\n  }\n\n  function tokenStringFactory(delimiter, tokenOuter) {\n    while (\"rubf\".indexOf(delimiter.charAt(0).toLowerCase()) >= 0)\n      delimiter = delimiter.substr(1);\n\n    var singleline = delimiter.length == 1;\n    var OUTCLASS = \"string\";\n\n    function tokenString(stream, state) {\n      while (!stream.eol()) {\n        stream.eatWhile(/[^'\"\\\\]/);\n        if (stream.eat(\"\\\\\")) {\n          stream.next();\n          if (singleline && stream.eol())\n            return OUTCLASS;\n        } else if (stream.match(delimiter)) {\n          state.tokenize = tokenOuter;\n          return OUTCLASS;\n        } else {\n          stream.eat(/['\"]/);\n        }\n      }\n      if (singleline) {\n        if (parserConf.singleLineStringErrors)\n          return ERRORCLASS;\n        else\n          state.tokenize = tokenOuter;\n      }\n      return OUTCLASS;\n    }\n    tokenString.isString = true;\n    return tokenString;\n  }\n\n  function pushPyScope(stream, state) {\n    while (top(state).type != \"py\") state.scopes.pop()\n    state.scopes.push({offset: top(state).offset + stream.indentUnit,\n                       type: \"py\",\n                       align: null})\n  }\n\n  function pushBracketScope(stream, state, type) {\n    var align = stream.match(/^[\\s\\[\\{\\(]*(?:#|$)/, false) ? null : stream.column() + 1\n    state.scopes.push({offset: state.indent + (hangingIndent || stream.indentUnit),\n                       type: type,\n                       align: align})\n  }\n\n  function dedent(stream, state) {\n    var indented = stream.indentation();\n    while (state.scopes.length > 1 && top(state).offset > indented) {\n      if (top(state).type != \"py\") return true;\n      state.scopes.pop();\n    }\n    return top(state).offset != indented;\n  }\n\n  function tokenLexer(stream, state) {\n    if (stream.sol()) {\n      state.beginningOfLine = true;\n      state.dedent = false;\n    }\n\n    var style = state.tokenize(stream, state);\n    var current = stream.current();\n\n    // Handle decorators\n    if (state.beginningOfLine && current == \"@\")\n      return stream.match(identifiers, false) ? \"meta\" : py3 ? \"operator\" : ERRORCLASS;\n\n    if (/\\S/.test(current)) state.beginningOfLine = false;\n\n    if ((style == \"variable\" || style == \"builtin\")\n        && state.lastToken == \"meta\")\n      style = \"meta\";\n\n    // Handle scope changes.\n    if (current == \"pass\" || current == \"return\")\n      state.dedent = true;\n\n    if (current == \"lambda\") state.lambda = true;\n    if (current == \":\" && !state.lambda && top(state).type == \"py\" && stream.match(/^\\s*(?:#|$)/, false))\n      pushPyScope(stream, state);\n\n    if (current.length == 1 && !/string|comment/.test(style)) {\n      var delimiter_index = \"[({\".indexOf(current);\n      if (delimiter_index != -1)\n        pushBracketScope(stream, state, \"])}\".slice(delimiter_index, delimiter_index+1));\n\n      delimiter_index = \"])}\".indexOf(current);\n      if (delimiter_index != -1) {\n        if (top(state).type == current) state.indent = state.scopes.pop().offset - (hangingIndent || stream.indentUnit)\n        else return ERRORCLASS;\n      }\n    }\n    if (state.dedent && stream.eol() && top(state).type == \"py\" && state.scopes.length > 1)\n      state.scopes.pop();\n\n    return style;\n  }\n\n  return {\n    name: \"python\",\n\n    startState: function() {\n      return {\n        tokenize: tokenBase,\n        scopes: [{offset: 0, type: \"py\", align: null}],\n        indent: 0,\n        lastToken: null,\n        lambda: false,\n        dedent: 0\n      };\n    },\n\n    token: function(stream, state) {\n      var addErr = state.errorToken;\n      if (addErr) state.errorToken = false;\n      var style = tokenLexer(stream, state);\n\n      if (style && style != \"comment\")\n        state.lastToken = (style == \"keyword\" || style == \"punctuation\") ? stream.current() : style;\n      if (style == \"punctuation\") style = null;\n\n      if (stream.eol() && state.lambda)\n        state.lambda = false;\n      return addErr ? ERRORCLASS : style;\n    },\n\n    indent: function(state, textAfter, cx) {\n      if (state.tokenize != tokenBase)\n        return state.tokenize.isString ? null : 0;\n\n      var scope = top(state)\n      var closing = scope.type == textAfter.charAt(0) ||\n          scope.type == \"py\" && !state.dedent && /^(else:|elif |except |finally:)/.test(textAfter)\n      if (scope.align != null)\n        return scope.align - (closing ? 1 : 0)\n      else\n        return scope.offset - (closing ? hangingIndent || cx.unit : 0)\n    },\n\n    languageData: {\n      autocomplete: commonKeywords.concat(commonBuiltins).concat([\"exec\", \"print\"]),\n      indentOnInput: /^\\s*([\\}\\]\\)]|else:|elif |except |finally:)$/,\n      commentTokens: {line: \"#\"},\n      closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"'''\", '\"\"\"']}\n    }\n  };\n};\n\nvar words = function(str) { return str.split(\" \"); };\n\nexport const python = mkPython({})\n\nexport const cython = mkPython({\n  extra_keywords: words(\"by cdef cimport cpdef ctypedef enum except \"+\n                        \"extern gil include nogil property public \"+\n                        \"readonly struct union DEF IF ELIF ELSE\")\n})\n"], "mappings": ";;;AAAA,SAAS,WAAWA,QAAO;AACzB,SAAO,IAAI,OAAO,QAAQA,OAAM,KAAK,KAAK,IAAI,OAAO;AACvD;AAEA,IAAI,gBAAgB,WAAW,CAAC,OAAO,MAAM,OAAO,IAAI,CAAC;AACzD,IAAI,iBAAiB;AAAA,EAAC;AAAA,EAAM;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AAAA,EAClC;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAU;AAAA,EACxC;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAM;AAAA,EAC/B;AAAA,EAAU;AAAA,EAAQ;AAAA,EAAS;AAAA,EAC3B;AAAA,EAAO;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAM;AAAA,EAAS;AAAM;AAC5E,IAAI,iBAAiB;AAAA,EAAC;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAY;AAAA,EAC7D;AAAA,EAAe;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAO;AAAA,EAC/D;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAU;AAAA,EAClD;AAAA,EAAW;AAAA,EAAW;AAAA,EAAW;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EACxD;AAAA,EAAS;AAAA,EAAO;AAAA,EAAc;AAAA,EAAc;AAAA,EAAQ;AAAA,EACpD;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAO;AAAA,EAAO;AAAA,EAAc;AAAA,EAAO;AAAA,EACrD;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAO;AAAA,EAAY;AAAA,EACnD;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAS;AAAA,EAAO;AAAA,EAAW;AAAA,EAC/C;AAAA,EAAU;AAAA,EAAgB;AAAA,EAAO;AAAA,EAAO;AAAA,EAAS;AAAA,EACjD;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAc;AAAA,EACrC;AAAA,EAAY;AAAW;AAE7C,SAAS,IAAI,OAAO;AAClB,SAAO,MAAM,OAAO,MAAM,OAAO,SAAS,CAAC;AAC7C;AAEO,SAAS,SAAS,YAAY;AACnC,MAAI,aAAa;AAEjB,MAAI,aAAa,WAAW,cAAc,WAAW,oBAAoB;AAEzE,MAAI,YAAY;AAAA,IAAC,WAAW;AAAA,IAAiB,WAAW;AAAA,IAAiB,WAAW;AAAA,IAAkB,WAAW;AAAA,IAChG,WAAW,aAAa;AAAA,EAAwD;AACjG,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK,KAAI,CAAC,UAAU,CAAC,EAAG,WAAU,OAAO,KAAK,CAAC;AAErF,MAAI,gBAAgB,WAAW;AAE/B,MAAI,aAAa,gBAAgB,aAAa;AAC9C,MAAI,WAAW,kBAAkB;AAC/B,iBAAa,WAAW,OAAO,WAAW,cAAc;AAE1D,MAAI,WAAW,kBAAkB;AAC/B,iBAAa,WAAW,OAAO,WAAW,cAAc;AAE1D,MAAI,MAAM,EAAE,WAAW,WAAW,OAAO,WAAW,OAAO,IAAI;AAC/D,MAAI,KAAK;AAEP,QAAI,cAAc,WAAW,eAAc;AAC3C,iBAAa,WAAW,OAAO,CAAC,YAAY,QAAQ,SAAS,SAAS,SAAS,SAAS,cAAc,SAAS,MAAM,CAAC;AACtH,iBAAa,WAAW,OAAO,CAAC,SAAS,SAAS,QAAQ,OAAO,CAAC;AAClE,QAAI,iBAAiB,IAAI,OAAO,oDAAsD,GAAG;AAAA,EAC3F,OAAO;AACL,QAAI,cAAc,WAAW,eAAc;AAC3C,iBAAa,WAAW,OAAO,CAAC,QAAQ,OAAO,CAAC;AAChD,iBAAa,WAAW,OAAO;AAAA,MAAC;AAAA,MAAS;AAAA,MAAc;AAAA,MAAU;AAAA,MAAO;AAAA,MAAU;AAAA,MAClD;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAQ;AAAA,MAAa;AAAA,MAAU;AAAA,MACjD;AAAA,MAAU;AAAA,MAAW;AAAA,MAAU;AAAA,IAAM,CAAC;AACtE,QAAI,iBAAiB,IAAI,OAAO,0CAA4C,GAAG;AAAA,EACjF;AACA,MAAI,WAAW,WAAW,UAAU;AACpC,MAAI,WAAW,WAAW,UAAU;AAGpC,WAAS,UAAU,QAAQ,OAAO;AAChC,QAAI,MAAM,OAAO,IAAI,KAAK,MAAM,aAAa;AAC7C,QAAI,IAAK,OAAM,SAAS,OAAO,YAAY;AAE3C,QAAI,OAAO,IAAI,KAAK,EAAE,QAAQ,MAAM;AAClC,UAAI,cAAc,IAAI,KAAK,EAAE;AAC7B,UAAI,OAAO,SAAS,GAAG;AACrB,YAAI,aAAa,OAAO,YAAY;AACpC,YAAI,aAAa;AACf,sBAAY,QAAQ,KAAK;AAAA,iBAClB,aAAa,eAAe,OAAO,QAAQ,KAAK,KAAK,OAAO,KAAK,KAAK;AAC7E,gBAAM,aAAa;AACrB,eAAO;AAAA,MACT,OAAO;AACL,YAAI,QAAQ,eAAe,QAAQ,KAAK;AACxC,YAAI,cAAc,KAAK,OAAO,QAAQ,KAAK;AACzC,mBAAS,MAAM;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,eAAe,QAAQ,KAAK;AAAA,EACrC;AAEA,WAAS,eAAe,QAAQ,OAAO,UAAU;AAC/C,QAAI,OAAO,SAAS,EAAG,QAAO;AAG9B,QAAI,CAAC,YAAY,OAAO,MAAM,MAAM,EAAG,QAAO;AAG9C,QAAI,OAAO,MAAM,YAAY,KAAK,GAAG;AACnC,UAAI,eAAe;AAEnB,UAAI,OAAO,MAAM,6BAA6B,GAAG;AAAE,uBAAe;AAAA,MAAM;AACxE,UAAI,OAAO,MAAM,cAAc,GAAG;AAAE,uBAAe;AAAA,MAAM;AACzD,UAAI,OAAO,MAAM,QAAQ,GAAG;AAAE,uBAAe;AAAA,MAAM;AACnD,UAAI,cAAc;AAEhB,eAAO,IAAI,IAAI;AACf,eAAO;AAAA,MACT;AAEA,UAAI,aAAa;AAEjB,UAAI,OAAO,MAAM,gBAAgB,EAAG,cAAa;AAEjD,UAAI,OAAO,MAAM,YAAY,EAAG,cAAa;AAE7C,UAAI,OAAO,MAAM,aAAa,EAAG,cAAa;AAE9C,UAAI,OAAO,MAAM,+BAA+B,GAAG;AAEjD,eAAO,IAAI,IAAI;AAEf,qBAAa;AAAA,MACf;AAEA,UAAI,OAAO,MAAM,cAAc,EAAG,cAAa;AAC/C,UAAI,YAAY;AAEd,eAAO,IAAI,IAAI;AACf,eAAO;AAAA,MACT;AAAA,IACF;AAGA,QAAI,OAAO,MAAM,cAAc,GAAG;AAChC,UAAI,cAAc,OAAO,QAAQ,EAAE,YAAY,EAAE,QAAQ,GAAG,MAAM;AAClE,UAAI,CAAC,aAAa;AAChB,cAAM,WAAW,mBAAmB,OAAO,QAAQ,GAAG,MAAM,QAAQ;AACpE,eAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,MACrC,OAAO;AACL,cAAM,WAAW,oBAAoB,OAAO,QAAQ,GAAG,MAAM,QAAQ;AACrE,eAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,MACrC;AAAA,IACF;AAEA,aAASC,KAAI,GAAGA,KAAI,UAAU,QAAQA;AACpC,UAAI,OAAO,MAAM,UAAUA,EAAC,CAAC,EAAG,QAAO;AAEzC,QAAI,OAAO,MAAM,UAAU,EAAG,QAAO;AAErC,QAAI,MAAM,aAAa,OAAO,OAAO,MAAM,WAAW;AACpD,aAAO;AAET,QAAI,OAAO,MAAM,QAAQ,KAAK,OAAO,MAAM,aAAa;AACtD,aAAO;AAET,QAAI,OAAO,MAAM,QAAQ;AACvB,aAAO;AAET,QAAI,OAAO,MAAM,eAAe;AAC9B,aAAO;AAET,QAAI,OAAO,MAAM,WAAW,GAAG;AAC7B,UAAI,MAAM,aAAa,SAAS,MAAM,aAAa;AACjD,eAAO;AACT,aAAO;AAAA,IACT;AAGA,WAAO,KAAK;AACZ,WAAO,WAAW,OAAM;AAAA,EAC1B;AAEA,WAAS,oBAAoB,WAAW,YAAY;AAClD,WAAO,OAAO,QAAQ,UAAU,OAAO,CAAC,EAAE,YAAY,CAAC,KAAK;AAC1D,kBAAY,UAAU,OAAO,CAAC;AAEhC,QAAI,aAAa,UAAU,UAAU;AACrC,QAAI,WAAW;AAEf,aAAS,gBAAgB,OAAO;AAC9B,aAAO,SAAS,QAAQ,OAAO;AAC7B,YAAI,QAAQ,eAAe,QAAQ,OAAO,IAAI;AAC9C,YAAI,SAAS,eAAe;AAC1B,cAAI,OAAO,QAAQ,KAAK,KAAK;AAC3B,kBAAM,WAAW,gBAAgB,QAAQ,CAAC;AAAA,UAC5C,WAAW,OAAO,QAAQ,KAAK,KAAK;AAClC,gBAAI,QAAQ,EAAG,OAAM,WAAW,gBAAgB,QAAQ,CAAC;AAAA,gBACpD,OAAM,WAAW;AAAA,UACxB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,YAAY,QAAQ,OAAO;AAClC,aAAO,CAAC,OAAO,IAAI,GAAG;AACpB,eAAO,SAAS,aAAa;AAC7B,YAAI,OAAO,IAAI,IAAI,GAAG;AACpB,iBAAO,KAAK;AACZ,cAAI,cAAc,OAAO,IAAI;AAC3B,mBAAO;AAAA,QACX,WAAW,OAAO,MAAM,SAAS,GAAG;AAClC,gBAAM,WAAW;AACjB,iBAAO;AAAA,QACT,WAAW,OAAO,MAAM,IAAI,GAAG;AAE7B,iBAAO;AAAA,QACT,WAAW,OAAO,MAAM,KAAK,KAAK,GAAG;AAEnC,gBAAM,WAAW,gBAAgB,CAAC;AAClC,cAAI,OAAO,QAAQ,EAAG,QAAO;AAAA,cACxB,QAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,QAC1C,WAAW,OAAO,MAAM,IAAI,GAAG;AAC7B,iBAAO;AAAA,QACT,WAAW,OAAO,MAAM,GAAG,GAAG;AAE5B,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,IAAI,MAAM;AAAA,QACnB;AAAA,MACF;AACA,UAAI,YAAY;AACd,YAAI,WAAW;AACb,iBAAO;AAAA;AAEP,gBAAM,WAAW;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AACA,gBAAY,WAAW;AACvB,WAAO;AAAA,EACT;AAEA,WAAS,mBAAmB,WAAW,YAAY;AACjD,WAAO,OAAO,QAAQ,UAAU,OAAO,CAAC,EAAE,YAAY,CAAC,KAAK;AAC1D,kBAAY,UAAU,OAAO,CAAC;AAEhC,QAAI,aAAa,UAAU,UAAU;AACrC,QAAI,WAAW;AAEf,aAAS,YAAY,QAAQ,OAAO;AAClC,aAAO,CAAC,OAAO,IAAI,GAAG;AACpB,eAAO,SAAS,SAAS;AACzB,YAAI,OAAO,IAAI,IAAI,GAAG;AACpB,iBAAO,KAAK;AACZ,cAAI,cAAc,OAAO,IAAI;AAC3B,mBAAO;AAAA,QACX,WAAW,OAAO,MAAM,SAAS,GAAG;AAClC,gBAAM,WAAW;AACjB,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,IAAI,MAAM;AAAA,QACnB;AAAA,MACF;AACA,UAAI,YAAY;AACd,YAAI,WAAW;AACb,iBAAO;AAAA;AAEP,gBAAM,WAAW;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AACA,gBAAY,WAAW;AACvB,WAAO;AAAA,EACT;AAEA,WAAS,YAAY,QAAQ,OAAO;AAClC,WAAO,IAAI,KAAK,EAAE,QAAQ,KAAM,OAAM,OAAO,IAAI;AACjD,UAAM,OAAO,KAAK;AAAA,MAAC,QAAQ,IAAI,KAAK,EAAE,SAAS,OAAO;AAAA,MACnC,MAAM;AAAA,MACN,OAAO;AAAA,IAAI,CAAC;AAAA,EACjC;AAEA,WAAS,iBAAiB,QAAQ,OAAO,MAAM;AAC7C,QAAI,QAAQ,OAAO,MAAM,uBAAuB,KAAK,IAAI,OAAO,OAAO,OAAO,IAAI;AAClF,UAAM,OAAO,KAAK;AAAA,MAAC,QAAQ,MAAM,UAAU,iBAAiB,OAAO;AAAA,MAChD;AAAA,MACA;AAAA,IAAY,CAAC;AAAA,EAClC;AAEA,WAAS,OAAO,QAAQ,OAAO;AAC7B,QAAI,WAAW,OAAO,YAAY;AAClC,WAAO,MAAM,OAAO,SAAS,KAAK,IAAI,KAAK,EAAE,SAAS,UAAU;AAC9D,UAAI,IAAI,KAAK,EAAE,QAAQ,KAAM,QAAO;AACpC,YAAM,OAAO,IAAI;AAAA,IACnB;AACA,WAAO,IAAI,KAAK,EAAE,UAAU;AAAA,EAC9B;AAEA,WAAS,WAAW,QAAQ,OAAO;AACjC,QAAI,OAAO,IAAI,GAAG;AAChB,YAAM,kBAAkB;AACxB,YAAM,SAAS;AAAA,IACjB;AAEA,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,QAAI,UAAU,OAAO,QAAQ;AAG7B,QAAI,MAAM,mBAAmB,WAAW;AACtC,aAAO,OAAO,MAAM,aAAa,KAAK,IAAI,SAAS,MAAM,aAAa;AAExE,QAAI,KAAK,KAAK,OAAO,EAAG,OAAM,kBAAkB;AAEhD,SAAK,SAAS,cAAc,SAAS,cAC9B,MAAM,aAAa;AACxB,cAAQ;AAGV,QAAI,WAAW,UAAU,WAAW;AAClC,YAAM,SAAS;AAEjB,QAAI,WAAW,SAAU,OAAM,SAAS;AACxC,QAAI,WAAW,OAAO,CAAC,MAAM,UAAU,IAAI,KAAK,EAAE,QAAQ,QAAQ,OAAO,MAAM,eAAe,KAAK;AACjG,kBAAY,QAAQ,KAAK;AAE3B,QAAI,QAAQ,UAAU,KAAK,CAAC,iBAAiB,KAAK,KAAK,GAAG;AACxD,UAAI,kBAAkB,MAAM,QAAQ,OAAO;AAC3C,UAAI,mBAAmB;AACrB,yBAAiB,QAAQ,OAAO,MAAM,MAAM,iBAAiB,kBAAgB,CAAC,CAAC;AAEjF,wBAAkB,MAAM,QAAQ,OAAO;AACvC,UAAI,mBAAmB,IAAI;AACzB,YAAI,IAAI,KAAK,EAAE,QAAQ,QAAS,OAAM,SAAS,MAAM,OAAO,IAAI,EAAE,UAAU,iBAAiB,OAAO;AAAA,YAC/F,QAAO;AAAA,MACd;AAAA,IACF;AACA,QAAI,MAAM,UAAU,OAAO,IAAI,KAAK,IAAI,KAAK,EAAE,QAAQ,QAAQ,MAAM,OAAO,SAAS;AACnF,YAAM,OAAO,IAAI;AAEnB,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IAEN,YAAY,WAAW;AACrB,aAAO;AAAA,QACL,UAAU;AAAA,QACV,QAAQ,CAAC,EAAC,QAAQ,GAAG,MAAM,MAAM,OAAO,KAAI,CAAC;AAAA,QAC7C,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,IAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,UAAI,SAAS,MAAM;AACnB,UAAI,OAAQ,OAAM,aAAa;AAC/B,UAAI,QAAQ,WAAW,QAAQ,KAAK;AAEpC,UAAI,SAAS,SAAS;AACpB,cAAM,YAAa,SAAS,aAAa,SAAS,gBAAiB,OAAO,QAAQ,IAAI;AACxF,UAAI,SAAS,cAAe,SAAQ;AAEpC,UAAI,OAAO,IAAI,KAAK,MAAM;AACxB,cAAM,SAAS;AACjB,aAAO,SAAS,aAAa;AAAA,IAC/B;AAAA,IAEA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,UAAI,MAAM,YAAY;AACpB,eAAO,MAAM,SAAS,WAAW,OAAO;AAE1C,UAAI,QAAQ,IAAI,KAAK;AACrB,UAAI,UAAU,MAAM,QAAQ,UAAU,OAAO,CAAC,KAC1C,MAAM,QAAQ,QAAQ,CAAC,MAAM,UAAU,kCAAkC,KAAK,SAAS;AAC3F,UAAI,MAAM,SAAS;AACjB,eAAO,MAAM,SAAS,UAAU,IAAI;AAAA;AAEpC,eAAO,MAAM,UAAU,UAAU,iBAAiB,GAAG,OAAO;AAAA,IAChE;AAAA,IAEA,cAAc;AAAA,MACZ,cAAc,eAAe,OAAO,cAAc,EAAE,OAAO,CAAC,QAAQ,OAAO,CAAC;AAAA,MAC5E,eAAe;AAAA,MACf,eAAe,EAAC,MAAM,IAAG;AAAA,MACzB,eAAe,EAAC,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK,EAAC;AAAA,IACnE;AAAA,EACF;AACF;AAEA,IAAI,QAAQ,SAAS,KAAK;AAAE,SAAO,IAAI,MAAM,GAAG;AAAG;AAE5C,IAAM,SAAS,SAAS,CAAC,CAAC;AAE1B,IAAM,SAAS,SAAS;AAAA,EAC7B,gBAAgB,MAAM,4HAEwC;AAChE,CAAC;", "names": ["words", "i"]}