{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/commonlisp.js"], "sourcesContent": ["var specialForm = /^(block|let*|return-from|catch|load-time-value|setq|eval-when|locally|symbol-macrolet|flet|macrolet|tagbody|function|multiple-value-call|the|go|multiple-value-prog1|throw|if|progn|unwind-protect|labels|progv|let|quote)$/;\nvar assumeBody = /^with|^def|^do|^prog|case$|^cond$|bind$|when$|unless$/;\nvar numLiteral = /^(?:[+\\-]?(?:\\d+|\\d*\\.\\d+)(?:[efd][+\\-]?\\d+)?|[+\\-]?\\d+(?:\\/[+\\-]?\\d+)?|#b[+\\-]?[01]+|#o[+\\-]?[0-7]+|#x[+\\-]?[\\da-f]+)/;\nvar symbol = /[^\\s'`,@()\\[\\]\";]/;\nvar type;\n\nfunction readSym(stream) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"\\\\\") stream.next();\n    else if (!symbol.test(ch)) { stream.backUp(1); break; }\n  }\n  return stream.current();\n}\n\nfunction base(stream, state) {\n  if (stream.eatSpace()) {type = \"ws\"; return null;}\n  if (stream.match(numLiteral)) return \"number\";\n  var ch = stream.next();\n  if (ch == \"\\\\\") ch = stream.next();\n\n  if (ch == '\"') return (state.tokenize = inString)(stream, state);\n  else if (ch == \"(\") { type = \"open\"; return \"bracket\"; }\n  else if (ch == \")\") { type = \"close\"; return \"bracket\"; }\n  else if (ch == \";\") { stream.skipToEnd(); type = \"ws\"; return \"comment\"; }\n  else if (/['`,@]/.test(ch)) return null;\n  else if (ch == \"|\") {\n    if (stream.skipTo(\"|\")) { stream.next(); return \"variableName\"; }\n    else { stream.skipToEnd(); return \"error\"; }\n  } else if (ch == \"#\") {\n    var ch = stream.next();\n    if (ch == \"(\") { type = \"open\"; return \"bracket\"; }\n    else if (/[+\\-=\\.']/.test(ch)) return null;\n    else if (/\\d/.test(ch) && stream.match(/^\\d*#/)) return null;\n    else if (ch == \"|\") return (state.tokenize = inComment)(stream, state);\n    else if (ch == \":\") { readSym(stream); return \"meta\"; }\n    else if (ch == \"\\\\\") { stream.next(); readSym(stream); return \"string.special\" }\n    else return \"error\";\n  } else {\n    var name = readSym(stream);\n    if (name == \".\") return null;\n    type = \"symbol\";\n    if (name == \"nil\" || name == \"t\" || name.charAt(0) == \":\") return \"atom\";\n    if (state.lastType == \"open\" && (specialForm.test(name) || assumeBody.test(name))) return \"keyword\";\n    if (name.charAt(0) == \"&\") return \"variableName.special\";\n    return \"variableName\";\n  }\n}\n\nfunction inString(stream, state) {\n  var escaped = false, next;\n  while (next = stream.next()) {\n    if (next == '\"' && !escaped) { state.tokenize = base; break; }\n    escaped = !escaped && next == \"\\\\\";\n  }\n  return \"string\";\n}\n\nfunction inComment(stream, state) {\n  var next, last;\n  while (next = stream.next()) {\n    if (next == \"#\" && last == \"|\") { state.tokenize = base; break; }\n    last = next;\n  }\n  type = \"ws\";\n  return \"comment\";\n}\n\nexport const commonLisp = {\n  name: \"commonlisp\",\n  startState: function () {\n    return {ctx: {prev: null, start: 0, indentTo: 0}, lastType: null, tokenize: base};\n  },\n\n  token: function (stream, state) {\n    if (stream.sol() && typeof state.ctx.indentTo != \"number\")\n      state.ctx.indentTo = state.ctx.start + 1;\n\n    type = null;\n    var style = state.tokenize(stream, state);\n    if (type != \"ws\") {\n      if (state.ctx.indentTo == null) {\n        if (type == \"symbol\" && assumeBody.test(stream.current()))\n          state.ctx.indentTo = state.ctx.start + stream.indentUnit;\n        else\n          state.ctx.indentTo = \"next\";\n      } else if (state.ctx.indentTo == \"next\") {\n        state.ctx.indentTo = stream.column();\n      }\n      state.lastType = type;\n    }\n    if (type == \"open\") state.ctx = {prev: state.ctx, start: stream.column(), indentTo: null};\n    else if (type == \"close\") state.ctx = state.ctx.prev || state.ctx;\n    return style;\n  },\n\n  indent: function (state) {\n    var i = state.ctx.indentTo;\n    return typeof i == \"number\" ? i : state.ctx.start + 1;\n  },\n\n  languageData: {\n    commentTokens: {line: \";;\", block: {open: \"#|\", close: \"|#\"}},\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']}\n  }\n};\n\n"], "mappings": ";;;AAAA,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI;AAEJ,SAAS,QAAQ,QAAQ;AACvB,MAAI;AACJ,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,KAAM,QAAO,KAAK;AAAA,aACnB,CAAC,OAAO,KAAK,EAAE,GAAG;AAAE,aAAO,OAAO,CAAC;AAAG;AAAA,IAAO;AAAA,EACxD;AACA,SAAO,OAAO,QAAQ;AACxB;AAEA,SAAS,KAAK,QAAQ,OAAO;AAC3B,MAAI,OAAO,SAAS,GAAG;AAAC,WAAO;AAAM,WAAO;AAAA,EAAK;AACjD,MAAI,OAAO,MAAM,UAAU,EAAG,QAAO;AACrC,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,KAAM,MAAK,OAAO,KAAK;AAEjC,MAAI,MAAM,IAAK,SAAQ,MAAM,WAAW,UAAU,QAAQ,KAAK;AAAA,WACtD,MAAM,KAAK;AAAE,WAAO;AAAQ,WAAO;AAAA,EAAW,WAC9C,MAAM,KAAK;AAAE,WAAO;AAAS,WAAO;AAAA,EAAW,WAC/C,MAAM,KAAK;AAAE,WAAO,UAAU;AAAG,WAAO;AAAM,WAAO;AAAA,EAAW,WAChE,SAAS,KAAK,EAAE,EAAG,QAAO;AAAA,WAC1B,MAAM,KAAK;AAClB,QAAI,OAAO,OAAO,GAAG,GAAG;AAAE,aAAO,KAAK;AAAG,aAAO;AAAA,IAAgB,OAC3D;AAAE,aAAO,UAAU;AAAG,aAAO;AAAA,IAAS;AAAA,EAC7C,WAAW,MAAM,KAAK;AACpB,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,KAAK;AAAE,aAAO;AAAQ,aAAO;AAAA,IAAW,WACzC,YAAY,KAAK,EAAE,EAAG,QAAO;AAAA,aAC7B,KAAK,KAAK,EAAE,KAAK,OAAO,MAAM,OAAO,EAAG,QAAO;AAAA,aAC/C,MAAM,IAAK,SAAQ,MAAM,WAAW,WAAW,QAAQ,KAAK;AAAA,aAC5D,MAAM,KAAK;AAAE,cAAQ,MAAM;AAAG,aAAO;AAAA,IAAQ,WAC7C,MAAM,MAAM;AAAE,aAAO,KAAK;AAAG,cAAQ,MAAM;AAAG,aAAO;AAAA,IAAiB,MAC1E,QAAO;AAAA,EACd,OAAO;AACL,QAAI,OAAO,QAAQ,MAAM;AACzB,QAAI,QAAQ,IAAK,QAAO;AACxB,WAAO;AACP,QAAI,QAAQ,SAAS,QAAQ,OAAO,KAAK,OAAO,CAAC,KAAK,IAAK,QAAO;AAClE,QAAI,MAAM,YAAY,WAAW,YAAY,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,GAAI,QAAO;AAC1F,QAAI,KAAK,OAAO,CAAC,KAAK,IAAK,QAAO;AAClC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,SAAS,QAAQ,OAAO;AAC/B,MAAI,UAAU,OAAO;AACrB,SAAO,OAAO,OAAO,KAAK,GAAG;AAC3B,QAAI,QAAQ,OAAO,CAAC,SAAS;AAAE,YAAM,WAAW;AAAM;AAAA,IAAO;AAC7D,cAAU,CAAC,WAAW,QAAQ;AAAA,EAChC;AACA,SAAO;AACT;AAEA,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,MAAM;AACV,SAAO,OAAO,OAAO,KAAK,GAAG;AAC3B,QAAI,QAAQ,OAAO,QAAQ,KAAK;AAAE,YAAM,WAAW;AAAM;AAAA,IAAO;AAChE,WAAO;AAAA,EACT;AACA,SAAO;AACP,SAAO;AACT;AAEO,IAAM,aAAa;AAAA,EACxB,MAAM;AAAA,EACN,YAAY,WAAY;AACtB,WAAO,EAAC,KAAK,EAAC,MAAM,MAAM,OAAO,GAAG,UAAU,EAAC,GAAG,UAAU,MAAM,UAAU,KAAI;AAAA,EAClF;AAAA,EAEA,OAAO,SAAU,QAAQ,OAAO;AAC9B,QAAI,OAAO,IAAI,KAAK,OAAO,MAAM,IAAI,YAAY;AAC/C,YAAM,IAAI,WAAW,MAAM,IAAI,QAAQ;AAEzC,WAAO;AACP,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,QAAI,QAAQ,MAAM;AAChB,UAAI,MAAM,IAAI,YAAY,MAAM;AAC9B,YAAI,QAAQ,YAAY,WAAW,KAAK,OAAO,QAAQ,CAAC;AACtD,gBAAM,IAAI,WAAW,MAAM,IAAI,QAAQ,OAAO;AAAA;AAE9C,gBAAM,IAAI,WAAW;AAAA,MACzB,WAAW,MAAM,IAAI,YAAY,QAAQ;AACvC,cAAM,IAAI,WAAW,OAAO,OAAO;AAAA,MACrC;AACA,YAAM,WAAW;AAAA,IACnB;AACA,QAAI,QAAQ,OAAQ,OAAM,MAAM,EAAC,MAAM,MAAM,KAAK,OAAO,OAAO,OAAO,GAAG,UAAU,KAAI;AAAA,aAC/E,QAAQ,QAAS,OAAM,MAAM,MAAM,IAAI,QAAQ,MAAM;AAC9D,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAU,OAAO;AACvB,QAAI,IAAI,MAAM,IAAI;AAClB,WAAO,OAAO,KAAK,WAAW,IAAI,MAAM,IAAI,QAAQ;AAAA,EACtD;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,IAC5D,eAAe,EAAC,UAAU,CAAC,KAAK,KAAK,KAAK,GAAG,EAAC;AAAA,EAChD;AACF;", "names": []}