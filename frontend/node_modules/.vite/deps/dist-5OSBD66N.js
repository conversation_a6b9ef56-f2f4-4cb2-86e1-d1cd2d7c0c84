import {
  ContextTracker,
  ExternalTokenizer,
  LRLanguage,
  L<PERSON>arser,
  LanguageSupport,
  delimitedIndent,
  foldInside,
  foldNodeProp,
  indentNodeProp,
  parseMixed,
  styleTags,
  tags
} from "./chunk-XA6Y4WMI.js";
import "./chunk-WOOG5QLI.js";

// node_modules/@lezer/yaml/dist/index.js
var blockEnd = 63;
var eof = 64;
var DirectiveEnd = 1;
var DocEnd = 2;
var sequenceStartMark = 3;
var sequenceContinueMark = 4;
var explicitMapStartMark = 5;
var explicitMapContinueMark = 6;
var flowMapMark = 7;
var mapStartMark = 65;
var mapContinueMark = 66;
var Literal = 8;
var QuotedLiteral = 9;
var Anchor = 10;
var Alias = 11;
var Tag = 12;
var BlockLiteralContent = 13;
var BracketL = 19;
var FlowSequence = 20;
var Colon = 29;
var BraceL = 33;
var FlowMapping = 34;
var BlockLiteralHeader = 47;
var type_Top = 0;
var type_Seq = 1;
var type_Map = 2;
var type_Flow = 3;
var type_Lit = 4;
var Context = class {
  constructor(parent, depth, type) {
    this.parent = parent;
    this.depth = depth;
    this.type = type;
    this.hash = (parent ? parent.hash + parent.hash << 8 : 0) + depth + (depth << 4) + type;
  }
};
Context.top = new Context(null, -1, type_Top);
function findColumn(input, pos) {
  for (let col = 0, p = pos - input.pos - 1; ; p--, col++) {
    let ch = input.peek(p);
    if (isBreakSpace(ch) || ch == -1) return col;
  }
}
function isNonBreakSpace(ch) {
  return ch == 32 || ch == 9;
}
function isBreakSpace(ch) {
  return ch == 10 || ch == 13;
}
function isSpace(ch) {
  return isNonBreakSpace(ch) || isBreakSpace(ch);
}
function isSep(ch) {
  return ch < 0 || isSpace(ch);
}
var indentation = new ContextTracker({
  start: Context.top,
  reduce(context, term) {
    return context.type == type_Flow && (term == FlowSequence || term == FlowMapping) ? context.parent : context;
  },
  shift(context, term, stack, input) {
    if (term == sequenceStartMark)
      return new Context(context, findColumn(input, input.pos), type_Seq);
    if (term == mapStartMark || term == explicitMapStartMark)
      return new Context(context, findColumn(input, input.pos), type_Map);
    if (term == blockEnd)
      return context.parent;
    if (term == BracketL || term == BraceL)
      return new Context(context, 0, type_Flow);
    if (term == BlockLiteralContent && context.type == type_Lit)
      return context.parent;
    if (term == BlockLiteralHeader) {
      let indent = /[1-9]/.exec(input.read(input.pos, stack.pos));
      if (indent) return new Context(context, context.depth + +indent[0], type_Lit);
    }
    return context;
  },
  hash(context) {
    return context.hash;
  }
});
function three(input, ch, off = 0) {
  return input.peek(off) == ch && input.peek(off + 1) == ch && input.peek(off + 2) == ch && isSep(input.peek(off + 3));
}
var newlines = new ExternalTokenizer((input, stack) => {
  if (input.next == -1 && stack.canShift(eof))
    return input.acceptToken(eof);
  let prev = input.peek(-1);
  if ((isBreakSpace(prev) || prev < 0) && stack.context.type != type_Flow) {
    if (three(
      input,
      45
      /* '-' */
    )) {
      if (stack.canShift(blockEnd)) input.acceptToken(blockEnd);
      else return input.acceptToken(DirectiveEnd, 3);
    }
    if (three(
      input,
      46
      /* '.' */
    )) {
      if (stack.canShift(blockEnd)) input.acceptToken(blockEnd);
      else return input.acceptToken(DocEnd, 3);
    }
    let depth = 0;
    while (input.next == 32) {
      depth++;
      input.advance();
    }
    if ((depth < stack.context.depth || depth == stack.context.depth && stack.context.type == type_Seq && (input.next != 45 || !isSep(input.peek(1)))) && // Not blank
    input.next != -1 && !isBreakSpace(input.next) && input.next != 35)
      input.acceptToken(blockEnd, -depth);
  }
}, { contextual: true });
var blockMark = new ExternalTokenizer((input, stack) => {
  if (stack.context.type == type_Flow) {
    if (input.next == 63) {
      input.advance();
      if (isSep(input.next)) input.acceptToken(flowMapMark);
    }
    return;
  }
  if (input.next == 45) {
    input.advance();
    if (isSep(input.next))
      input.acceptToken(stack.context.type == type_Seq && stack.context.depth == findColumn(input, input.pos - 1) ? sequenceContinueMark : sequenceStartMark);
  } else if (input.next == 63) {
    input.advance();
    if (isSep(input.next))
      input.acceptToken(stack.context.type == type_Map && stack.context.depth == findColumn(input, input.pos - 1) ? explicitMapContinueMark : explicitMapStartMark);
  } else {
    let start = input.pos;
    for (; ; ) {
      if (isNonBreakSpace(input.next)) {
        if (input.pos == start) return;
        input.advance();
      } else if (input.next == 33) {
        readTag(input);
      } else if (input.next == 38) {
        readAnchor(input);
      } else if (input.next == 42) {
        readAnchor(input);
        break;
      } else if (input.next == 39 || input.next == 34) {
        if (readQuoted(input, true)) break;
        return;
      } else if (input.next == 91 || input.next == 123) {
        if (!scanBrackets(input)) return;
        break;
      } else {
        readPlain(input, true, false, 0);
        break;
      }
    }
    while (isNonBreakSpace(input.next)) input.advance();
    if (input.next == 58) {
      if (input.pos == start && stack.canShift(Colon)) return;
      let after = input.peek(1);
      if (isSep(after))
        input.acceptTokenTo(stack.context.type == type_Map && stack.context.depth == findColumn(input, start) ? mapContinueMark : mapStartMark, start);
    }
  }
}, { contextual: true });
function uriChar(ch) {
  return ch > 32 && ch < 127 && ch != 34 && ch != 37 && ch != 44 && ch != 60 && ch != 62 && ch != 92 && ch != 94 && ch != 96 && ch != 123 && ch != 124 && ch != 125;
}
function hexChar(ch) {
  return ch >= 48 && ch <= 57 || ch >= 97 && ch <= 102 || ch >= 65 && ch <= 70;
}
function readUriChar(input, quoted) {
  if (input.next == 37) {
    input.advance();
    if (hexChar(input.next)) input.advance();
    if (hexChar(input.next)) input.advance();
    return true;
  } else if (uriChar(input.next) || quoted && input.next == 44) {
    input.advance();
    return true;
  }
  return false;
}
function readTag(input) {
  input.advance();
  if (input.next == 60) {
    input.advance();
    for (; ; ) {
      if (!readUriChar(input, true)) {
        if (input.next == 62) input.advance();
        break;
      }
    }
  } else {
    while (readUriChar(input, false)) {
    }
  }
}
function readAnchor(input) {
  input.advance();
  while (!isSep(input.next) && charTag(input.tag) != "f") input.advance();
}
function readQuoted(input, scan) {
  let quote = input.next, lineBreak = false, start = input.pos;
  input.advance();
  for (; ; ) {
    let ch = input.next;
    if (ch < 0) break;
    input.advance();
    if (ch == quote) {
      if (ch == 39) {
        if (input.next == 39) input.advance();
        else break;
      } else {
        break;
      }
    } else if (ch == 92 && quote == 34) {
      if (input.next >= 0) input.advance();
    } else if (isBreakSpace(ch)) {
      if (scan) return false;
      lineBreak = true;
    } else if (scan && input.pos >= start + 1024) {
      return false;
    }
  }
  return !lineBreak;
}
function scanBrackets(input) {
  for (let stack = [], end = input.pos + 1024; ; ) {
    if (input.next == 91 || input.next == 123) {
      stack.push(input.next);
      input.advance();
    } else if (input.next == 39 || input.next == 34) {
      if (!readQuoted(input, true)) return false;
    } else if (input.next == 93 || input.next == 125) {
      if (stack[stack.length - 1] != input.next - 2) return false;
      stack.pop();
      input.advance();
      if (!stack.length) return true;
    } else if (input.next < 0 || input.pos > end || isBreakSpace(input.next)) {
      return false;
    } else {
      input.advance();
    }
  }
}
var charTable = "iiisiiissisfissssssssssssisssiiissssssssssssssssssssssssssfsfssissssssssssssssssssssssssssfif";
function charTag(ch) {
  if (ch < 33) return "u";
  if (ch > 125) return "s";
  return charTable[ch - 33];
}
function isSafe(ch, inFlow) {
  let tag = charTag(ch);
  return tag != "u" && !(inFlow && tag == "f");
}
function readPlain(input, scan, inFlow, indent) {
  if (charTag(input.next) == "s" || (input.next == 63 || input.next == 58 || input.next == 45) && isSafe(input.peek(1), inFlow)) {
    input.advance();
  } else {
    return false;
  }
  let start = input.pos;
  for (; ; ) {
    let next = input.next, off = 0, lineIndent = indent + 1;
    while (isSpace(next)) {
      if (isBreakSpace(next)) {
        if (scan) return false;
        lineIndent = 0;
      } else {
        lineIndent++;
      }
      next = input.peek(++off);
    }
    let safe = next >= 0 && (next == 58 ? isSafe(input.peek(off + 1), inFlow) : next == 35 ? input.peek(off - 1) != 32 : isSafe(next, inFlow));
    if (!safe || !inFlow && lineIndent <= indent || lineIndent == 0 && !inFlow && (three(input, 45, off) || three(input, 46, off)))
      break;
    if (scan && charTag(next) == "f") return false;
    for (let i = off; i >= 0; i--) input.advance();
    if (scan && input.pos > start + 1024) return false;
  }
  return true;
}
var literals = new ExternalTokenizer((input, stack) => {
  if (input.next == 33) {
    readTag(input);
    input.acceptToken(Tag);
  } else if (input.next == 38 || input.next == 42) {
    let token = input.next == 38 ? Anchor : Alias;
    readAnchor(input);
    input.acceptToken(token);
  } else if (input.next == 39 || input.next == 34) {
    readQuoted(input, false);
    input.acceptToken(QuotedLiteral);
  } else if (readPlain(input, false, stack.context.type == type_Flow, stack.context.depth)) {
    input.acceptToken(Literal);
  }
});
var blockLiteral = new ExternalTokenizer((input, stack) => {
  let indent = stack.context.type == type_Lit ? stack.context.depth : -1, upto = input.pos;
  scan: for (; ; ) {
    let depth = 0, next = input.next;
    while (next == 32) next = input.peek(++depth);
    if (!depth && (three(input, 45, depth) || three(input, 46, depth))) break;
    if (!isBreakSpace(next)) {
      if (indent < 0) indent = Math.max(stack.context.depth + 1, depth);
      if (depth < indent) break;
    }
    for (; ; ) {
      if (input.next < 0) break scan;
      let isBreak = isBreakSpace(input.next);
      input.advance();
      if (isBreak) continue scan;
      upto = input.pos;
    }
  }
  input.acceptTokenTo(BlockLiteralContent, upto);
});
var yamlHighlighting = styleTags({
  DirectiveName: tags.keyword,
  DirectiveContent: tags.attributeValue,
  "DirectiveEnd DocEnd": tags.meta,
  QuotedLiteral: tags.string,
  BlockLiteralHeader: tags.special(tags.string),
  BlockLiteralContent: tags.content,
  Literal: tags.content,
  "Key/Literal Key/QuotedLiteral": tags.definition(tags.propertyName),
  "Anchor Alias": tags.labelName,
  Tag: tags.typeName,
  Comment: tags.lineComment,
  ": , -": tags.separator,
  "?": tags.punctuation,
  "[ ]": tags.squareBracket,
  "{ }": tags.brace
});
var parser = LRParser.deserialize({
  version: 14,
  states: "5lQ!ZQgOOO#PQfO'#CpO#uQfO'#DOOOQR'#Dv'#DvO$qQgO'#DRO%gQdO'#DUO%nQgO'#DUO&ROaO'#D[OOQR'#Du'#DuO&{QgO'#D^O'rQgO'#D`OOQR'#Dt'#DtO(iOqO'#DbOOQP'#Dj'#DjO(zQaO'#CmO)YQgO'#CmOOQP'#Cm'#CmQ)jQaOOQ)uQgOOQ]QgOOO*PQdO'#CrO*nQdO'#CtOOQO'#Dw'#DwO+]Q`O'#CxO+hQdO'#CwO+rQ`O'#CwOOQO'#Cv'#CvO+wQdO'#CvOOQO'#Cq'#CqO,UQ`O,59[O,^QfO,59[OOQR,59[,59[OOQO'#Cx'#CxO,eQ`O'#DPO,pQdO'#DPOOQO'#Dx'#DxO,zQdO'#DxO-XQ`O,59jO-aQfO,59jOOQR,59j,59jOOQR'#DS'#DSO-hQcO,59mO-sQgO'#DVO.TQ`O'#DVO.YQcO,59pOOQR'#DX'#DXO#|QfO'#DWO.hQcO'#DWOOQR,59v,59vO.yOWO,59vO/OOaO,59vO/WOaO,59vO/cQgO'#D_OOQR,59x,59xO0VQgO'#DaOOQR,59z,59zOOQP,59|,59|O0yOaO,59|O1ROaO,59|O1aOqO,59|OOQP-E7h-E7hO1oQgO,59XOOQP,59X,59XO2PQaO'#DeO2_QgO'#DeO2oQgO'#DkOOQP'#Dk'#DkQ)jQaOOO3PQdO'#CsOOQO,59^,59^O3kQdO'#CuOOQO,59`,59`OOQO,59c,59cO4VQdO,59cO4aQdO'#CzO4kQ`O'#CzOOQO,59b,59bOOQU,5:Q,5:QOOQR1G.v1G.vO4pQ`O1G.vOOQU-E7d-E7dO4xQdO,59kOOQO,59k,59kO5SQdO'#DQO5^Q`O'#DQOOQO,5:d,5:dOOQU,5:R,5:ROOQR1G/U1G/UO5cQ`O1G/UOOQU-E7e-E7eO5kQgO'#DhO5xQcO1G/XOOQR1G/X1G/XOOQR,59q,59qO6TQgO,59qO6eQdO'#DiO6lQgO'#DiO7PQcO1G/[OOQR1G/[1G/[OOQR,59r,59rO#|QfO,59rOOQR1G/b1G/bO7_OWO1G/bO7dOaO1G/bOOQR,59y,59yOOQR,59{,59{OOQP1G/h1G/hO7lOaO1G/hO7tOaO1G/hO8POaO1G/hOOQP1G.s1G.sO8_QgO,5:POOQP,5:P,5:POOQP,5:V,5:VOOQP-E7i-E7iOOQO,59_,59_OOQO,59a,59aOOQO1G.}1G.}OOQO,59f,59fO8oQdO,59fOOQR7+$b7+$bP,XQ`O'#DfOOQO1G/V1G/VOOQO,59l,59lO8yQdO,59lOOQR7+$p7+$pP9TQ`O'#DgOOQR'#DT'#DTOOQR,5:S,5:SOOQR-E7f-E7fOOQR7+$s7+$sOOQR1G/]1G/]O9YQgO'#DYO9jQ`O'#DYOOQR,5:T,5:TO#|QfO'#DZO9oQcO'#DZOOQR-E7g-E7gOOQR7+$v7+$vOOQR1G/^1G/^OOQR7+$|7+$|O:QOWO7+$|OOQP7+%S7+%SO:VOaO7+%SO:_OaO7+%SOOQP1G/k1G/kOOQO1G/Q1G/QOOQO1G/W1G/WOOQR,59t,59tO:jQgO,59tOOQR,59u,59uO#|QfO,59uOOQR<<Hh<<HhOOQP<<Hn<<HnO:zOaO<<HnOOQR1G/`1G/`OOQR1G/a1G/aOOQPAN>YAN>Y",
  stateData: ";S~O!fOS!gOS^OS~OP_OQbORSOTUOWROXROYYOZZO[XOcPOqQO!PVO!V[O!cTO~O`cO~P]OVkOWROXROYeOZfO[dOcPOmhOqQO~OboO~P!bOVtOWROXROYeOZfO[dOcPOmrOqQO~OpwO~P#WORSOTUOWROXROYYOZZO[XOcPOqQO!PVO!cTO~OSvP!avP!bvP~P#|OWROXROYeOZfO[dOcPOqQO~OmzO~P%OOm!OOUzP!azP!bzP!dzP~P#|O^!SO!b!QO!f!TO!g!RO~ORSOTUOWROXROcPOqQO!PVO!cTO~OY!UOP!QXQ!QX!V!QX!`!QXS!QX!a!QX!b!QXU!QXm!QX!d!QX~P&aO[!WOP!SXQ!SX!V!SX!`!SXS!SX!a!SX!b!SXU!SXm!SX!d!SX~P&aO^!ZO!W![O!b!YO!f!]O!g!YO~OP!_O!V[OQaX!`aX~OPaXQaX!VaX!`aX~P#|OP!bOQ!cO!V[O~OP_O!V[O~P#|OWROXROY!fOcPOqQObfXmfXofXpfX~OWROXRO[!hOcPOqQObhXmhXohXphX~ObeXmlXoeX~ObkXokX~P%OOm!kO~Om!lObnPonP~P%OOb!pOo!oO~Ob!pO~P!bOm!sOosXpsX~OosXpsX~P%OOm!uOotPptP~P%OOo!xOp!yO~Op!yO~P#WOS!|O!a#OO!b#OO~OUyX!ayX!byX!dyX~P#|Om#QO~OU#SO!a#UO!b#UO!d#RO~Om#WOUzX!azX!bzX!dzX~O]#XO~O!b#XO!g#YO~O^#ZO!b#XO!g#YO~OP!RXQ!RX!V!RX!`!RXS!RX!a!RX!b!RXU!RXm!RX!d!RX~P&aOP!TXQ!TX!V!TX!`!TXS!TX!a!TX!b!TXU!TXm!TX!d!TX~P&aO!b#^O!g#^O~O^#_O!b#^O!f#`O!g#^O~O^#_O!W#aO!b#^O!g#^O~OPaaQaa!Vaa!`aa~P#|OP#cO!V[OQ!XX!`!XX~OP!XXQ!XX!V!XX!`!XX~P#|OP_O!V[OQ!_X!`!_X~P#|OWROXROcPOqQObgXmgXogXpgX~OWROXROcPOqQObiXmiXoiXpiX~Obkaoka~P%OObnXonX~P%OOm#kO~Ob#lOo!oO~Oosapsa~P%OOotXptX~P%OOm#pO~Oo!xOp#qO~OSwP!awP!bwP~P#|OS!|O!a#vO!b#vO~OUya!aya!bya!dya~P#|Om#xO~P%OOm#{OU}P!a}P!b}P!d}P~P#|OU#SO!a$OO!b$OO!d#RO~O]$QO~O!b$QO!g$RO~O!b$SO!g$SO~O^$TO!b$SO!g$SO~O^$TO!b$SO!f$UO!g$SO~OP!XaQ!Xa!V!Xa!`!Xa~P#|Obnaona~P%OOotapta~P%OOo!xO~OU|X!a|X!b|X!d|X~P#|Om$ZO~Om$]OU}X!a}X!b}X!d}X~O]$^O~O!b$_O!g$_O~O^$`O!b$_O!g$_O~OU|a!a|a!b|a!d|a~P#|O!b$cO!g$cO~O",
  goto: ",]!mPPPPPPPPPPPPPPPPP!nPP!v#v#|$`#|$c$f$j$nP%VPPP!v%Y%^%a%{&O%a&R&U&X&_&b%aP&e&{&e'O'RPP']'a'g'm's'y(XPPPPPPPP(_)e*X+c,VUaObcR#e!c!{ROPQSTUXY_bcdehknrtvz!O!U!W!_!b!c!f!h!k!l!s!u!|#Q#R#S#W#c#k#p#x#{$Z$]QmPR!qnqfPQThknrtv!k!l!s!u#R#k#pR!gdR!ieTlPnTjPnSiPnSqQvQ{TQ!mkQ!trQ!vtR#y#RR!nkTsQvR!wt!RWOSUXY_bcz!O!U!W!_!b!c!|#Q#S#W#c#x#{$Z$]RySR#t!|R|TR|UQ!PUR#|#SR#z#RR#z#SyZOSU_bcz!O!_!b!c!|#Q#S#W#c#x#{$Z$]R!VXR!XYa]O^abc!a!c!eT!da!eQnPR!rnQvQR!{vQ!}yR#u!}Q#T|R#}#TW^Obc!cS!^^!aT!aa!eQ!eaR#f!eW`Obc!cQxSS}U#SQ!`_Q#PzQ#V!OQ#b!_Q#d!bQ#s!|Q#w#QQ$P#WQ$V#cQ$Y#xQ$[#{Q$a$ZR$b$]xZOSU_bcz!O!_!b!c!|#Q#S#W#c#x#{$Z$]Q!VXQ!XYQ#[!UR#]!W!QWOSUXY_bcz!O!U!W!_!b!c!|#Q#S#W#c#x#{$Z$]pfPQThknrtv!k!l!s!u#R#k#pQ!gdQ!ieQ#g!fR#h!hSgPn^pQTkrtv#RQ!jhQ#i!kQ#j!lQ#n!sQ#o!uQ$W#kR$X#pQuQR!zv",
  nodeNames: "⚠ DirectiveEnd DocEnd - - ? ? ? Literal QuotedLiteral Anchor Alias Tag BlockLiteralContent Comment Stream BOM Document ] [ FlowSequence Item Tagged Anchored Anchored Tagged FlowMapping Pair Key : Pair , } { FlowMapping Pair Pair BlockSequence Item Item BlockMapping Pair Pair Key Pair Pair BlockLiteral BlockLiteralHeader Tagged Anchored Anchored Tagged Directive DirectiveName DirectiveContent Document",
  maxTerm: 74,
  context: indentation,
  nodeProps: [
    ["isolate", -3, 8, 9, 14, ""],
    ["openedBy", 18, "[", 32, "{"],
    ["closedBy", 19, "]", 33, "}"]
  ],
  propSources: [yamlHighlighting],
  skippedNodes: [0],
  repeatNodeCount: 6,
  tokenData: "-Y~RnOX#PXY$QYZ$]Z]#P]^$]^p#Ppq$Qqs#Pst$btu#Puv$yv|#P|}&e}![#P![!]'O!]!`#P!`!a'i!a!}#P!}#O*g#O#P#P#P#Q+Q#Q#o#P#o#p+k#p#q'i#q#r,U#r;'S#P;'S;=`#z<%l?HT#P?HT?HU,o?HUO#PQ#UU!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PQ#kTOY#PZs#Pt;'S#P;'S;=`#z<%lO#PQ#}P;=`<%l#P~$VQ!f~XY$Qpq$Q~$bO!g~~$gS^~OY$bZ;'S$b;'S;=`$s<%lO$b~$vP;=`<%l$bR%OX!WQOX%kXY#PZ]%k]^#P^p%kpq#hq;'S%k;'S;=`&_<%lO%kR%rX!WQ!VPOX%kXY#PZ]%k]^#P^p%kpq#hq;'S%k;'S;=`&_<%lO%kR&bP;=`<%l%kR&lUoP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR'VUmP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR'p[!PP!WQOY#PZp#Ppq#hq{#P{|(f|}#P}!O(f!O!R#P!R![)p![;'S#P;'S;=`#z<%lO#PR(mW!PP!WQOY#PZp#Ppq#hq!R#P!R![)V![;'S#P;'S;=`#z<%lO#PR)^U!PP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR)wY!PP!WQOY#PZp#Ppq#hq{#P{|)V|}#P}!O)V!O;'S#P;'S;=`#z<%lO#PR*nUcP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR+XUbP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR+rUqP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR,]UpP!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#PR,vU`P!WQOY#PZp#Ppq#hq;'S#P;'S;=`#z<%lO#P",
  tokenizers: [newlines, blockMark, literals, blockLiteral, 0, 1],
  topRules: { "Stream": [0, 15] },
  tokenPrec: 0
});

// node_modules/@codemirror/lang-yaml/dist/index.js
var parser2 = LRParser.deserialize({
  version: 14,
  states: "!vOQOPOOO]OPO'#C_OhOPO'#C^OOOO'#Cc'#CcOpOPO'#CaQOOOOOO{OPOOOOOO'#Cb'#CbO!WOPO'#C`O!`OPO,58xOOOO-E6a-E6aOOOO-E6`-E6`OOOO'#C_'#C_OOOO1G.d1G.d",
  stateData: "!h~OXPOYROWTP~OWVXXRXYRX~OYVOXSP~OXROYROWTX~OXROYROWTP~OYVOXSX~OX[O~OXY~",
  goto: "vWPPX[beioRUOQQOR]XRXQTTOUQWQRZWSSOURYS",
  nodeNames: "⚠ Document Frontmatter DashLine FrontmatterContent Body",
  maxTerm: 10,
  skippedNodes: [0],
  repeatNodeCount: 2,
  tokenData: "$z~RXOYnYZ!^Z]n]^!^^}n}!O!i!O;'Sn;'S;=`!c<%lOn~qXOYnYZ!^Z]n]^!^^;'Sn;'S;=`!c<%l~n~On~~!^~!cOY~~!fP;=`<%ln~!lZOYnYZ!^Z]n]^!^^}n}!O#_!O;'Sn;'S;=`!c<%l~n~On~~!^~#bZOYnYZ!^Z]n]^!^^}n}!O$T!O;'Sn;'S;=`!c<%l~n~On~~!^~$WXOYnYZ$sZ]n]^$s^;'Sn;'S;=`!c<%l~n~On~~$s~$zOX~Y~",
  tokenizers: [0],
  topRules: { "Document": [0, 1] },
  tokenPrec: 67
});
var yamlLanguage = LRLanguage.define({
  name: "yaml",
  parser: parser.configure({
    props: [
      indentNodeProp.add({
        Stream: (cx) => {
          for (let before = cx.node.resolve(cx.pos, -1); before && before.to >= cx.pos; before = before.parent) {
            if (before.name == "BlockLiteralContent" && before.from < before.to)
              return cx.baseIndentFor(before);
            if (before.name == "BlockLiteral")
              return cx.baseIndentFor(before) + cx.unit;
            if (before.name == "BlockSequence" || before.name == "BlockMapping")
              return cx.column(before.from, 1);
            if (before.name == "QuotedLiteral")
              return null;
            if (before.name == "Literal") {
              let col = cx.column(before.from, 1);
              if (col == cx.lineIndent(before.from, 1))
                return col;
              if (before.to > cx.pos)
                return null;
            }
          }
          return null;
        },
        FlowMapping: delimitedIndent({ closing: "}" }),
        FlowSequence: delimitedIndent({ closing: "]" })
      }),
      foldNodeProp.add({
        "FlowMapping FlowSequence": foldInside,
        "Item Pair BlockLiteral": (node, state) => ({ from: state.doc.lineAt(node.from).to, to: node.to })
      })
    ]
  }),
  languageData: {
    commentTokens: { line: "#" },
    indentOnInput: /^\s*[\]\}]$/
  }
});
function yaml() {
  return new LanguageSupport(yamlLanguage);
}
var frontmatterLanguage = LRLanguage.define({
  name: "yaml-frontmatter",
  parser: parser2.configure({
    props: [styleTags({ DashLine: tags.meta })]
  })
});
function yamlFrontmatter(config) {
  let { language, support } = config.content instanceof LanguageSupport ? config.content : { language: config.content, support: [] };
  return new LanguageSupport(frontmatterLanguage.configure({
    wrap: parseMixed((node) => {
      return node.name == "FrontmatterContent" ? { parser: yamlLanguage.parser } : node.name == "Body" ? { parser: language.parser } : null;
    })
  }), support);
}
export {
  yaml,
  yamlFrontmatter,
  yamlLanguage
};
//# sourceMappingURL=dist-5OSBD66N.js.map
