{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/dylan.js"], "sourcesContent": ["function forEach(arr, f) {\n  for (var i = 0; i < arr.length; i++) f(arr[i], i)\n}\nfunction some(arr, f) {\n  for (var i = 0; i < arr.length; i++) if (f(arr[i], i)) return true\n  return false\n}\n\n// Words\nvar words = {\n  // Words that introduce unnamed definitions like \"define interface\"\n  unnamedDefinition: [\"interface\"],\n\n  // Words that introduce simple named definitions like \"define library\"\n  namedDefinition: [\"module\", \"library\", \"macro\",\n                    \"C-struct\", \"C-union\",\n                    \"C-function\", \"C-callable-wrapper\"\n                   ],\n\n  // Words that introduce type definitions like \"define class\".\n  // These are also parameterized like \"define method\" and are\n  // appended to otherParameterizedDefinitionWords\n  typeParameterizedDefinition: [\"class\", \"C-subtype\", \"C-mapped-subtype\"],\n\n  // Words that introduce trickier definitions like \"define method\".\n  // These require special definitions to be added to startExpressions\n  otherParameterizedDefinition: [\"method\", \"function\",\n                                 \"C-variable\", \"C-address\"\n                                ],\n\n  // Words that introduce module constant definitions.\n  // These must also be simple definitions and are\n  // appended to otherSimpleDefinitionWords\n  constantSimpleDefinition: [\"constant\"],\n\n  // Words that introduce module variable definitions.\n  // These must also be simple definitions and are\n  // appended to otherSimpleDefinitionWords\n  variableSimpleDefinition: [\"variable\"],\n\n  // Other words that introduce simple definitions\n  // (without implicit bodies).\n  otherSimpleDefinition: [\"generic\", \"domain\",\n                          \"C-pointer-type\",\n                          \"table\"\n                         ],\n\n  // Words that begin statements with implicit bodies.\n  statement: [\"if\", \"block\", \"begin\", \"method\", \"case\",\n              \"for\", \"select\", \"when\", \"unless\", \"until\",\n              \"while\", \"iterate\", \"profiling\", \"dynamic-bind\"\n             ],\n\n  // Patterns that act as separators in compound statements.\n  // This may include any general pattern that must be indented\n  // specially.\n  separator: [\"finally\", \"exception\", \"cleanup\", \"else\",\n              \"elseif\", \"afterwards\"\n             ],\n\n  // Keywords that do not require special indentation handling,\n  // but which should be highlighted\n  other: [\"above\", \"below\", \"by\", \"from\", \"handler\", \"in\",\n          \"instance\", \"let\", \"local\", \"otherwise\", \"slot\",\n          \"subclass\", \"then\", \"to\", \"keyed-by\", \"virtual\"\n         ],\n\n  // Condition signaling function calls\n  signalingCalls: [\"signal\", \"error\", \"cerror\",\n                   \"break\", \"check-type\", \"abort\"\n                  ]\n};\n\nwords[\"otherDefinition\"] =\n  words[\"unnamedDefinition\"]\n  .concat(words[\"namedDefinition\"])\n  .concat(words[\"otherParameterizedDefinition\"]);\n\nwords[\"definition\"] =\n  words[\"typeParameterizedDefinition\"]\n  .concat(words[\"otherDefinition\"]);\n\nwords[\"parameterizedDefinition\"] =\n  words[\"typeParameterizedDefinition\"]\n  .concat(words[\"otherParameterizedDefinition\"]);\n\nwords[\"simpleDefinition\"] =\n  words[\"constantSimpleDefinition\"]\n  .concat(words[\"variableSimpleDefinition\"])\n  .concat(words[\"otherSimpleDefinition\"]);\n\nwords[\"keyword\"] =\n  words[\"statement\"]\n  .concat(words[\"separator\"])\n  .concat(words[\"other\"]);\n\n// Patterns\nvar symbolPattern = \"[-_a-zA-Z?!*@<>$%]+\";\nvar symbol = new RegExp(\"^\" + symbolPattern);\nvar patterns = {\n  // Symbols with special syntax\n  symbolKeyword: symbolPattern + \":\",\n  symbolClass: \"<\" + symbolPattern + \">\",\n  symbolGlobal: \"\\\\*\" + symbolPattern + \"\\\\*\",\n  symbolConstant: \"\\\\$\" + symbolPattern\n};\nvar patternStyles = {\n  symbolKeyword: \"atom\",\n  symbolClass: \"tag\",\n  symbolGlobal: \"variableName.standard\",\n  symbolConstant: \"variableName.constant\"\n};\n\n// Compile all patterns to regular expressions\nfor (var patternName in patterns)\n  if (patterns.hasOwnProperty(patternName))\n    patterns[patternName] = new RegExp(\"^\" + patterns[patternName]);\n\n// Names beginning \"with-\" and \"without-\" are commonly\n// used as statement macro\npatterns[\"keyword\"] = [/^with(?:out)?-[-_a-zA-Z?!*@<>$%]+/];\n\nvar styles = {};\nstyles[\"keyword\"] = \"keyword\";\nstyles[\"definition\"] = \"def\";\nstyles[\"simpleDefinition\"] = \"def\";\nstyles[\"signalingCalls\"] = \"builtin\";\n\n// protected words lookup table\nvar wordLookup = {};\nvar styleLookup = {};\n\nforEach([\n  \"keyword\",\n  \"definition\",\n  \"simpleDefinition\",\n  \"signalingCalls\"\n], function(type) {\n  forEach(words[type], function(word) {\n    wordLookup[word] = type;\n    styleLookup[word] = styles[type];\n  });\n});\n\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\nfunction tokenBase(stream, state) {\n  // String\n  var ch = stream.peek();\n  if (ch == \"'\" || ch == '\"') {\n    stream.next();\n    return chain(stream, state, tokenString(ch, \"string\"));\n  }\n  // Comment\n  else if (ch == \"/\") {\n    stream.next();\n    if (stream.eat(\"*\")) {\n      return chain(stream, state, tokenComment);\n    } else if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    stream.backUp(1);\n  }\n  // Decimal\n  else if (/[+\\-\\d\\.]/.test(ch)) {\n    if (stream.match(/^[+-]?[0-9]*\\.[0-9]*([esdx][+-]?[0-9]+)?/i) ||\n        stream.match(/^[+-]?[0-9]+([esdx][+-]?[0-9]+)/i) ||\n        stream.match(/^[+-]?\\d+/)) {\n      return \"number\";\n    }\n  }\n  // Hash\n  else if (ch == \"#\") {\n    stream.next();\n    // Symbol with string syntax\n    ch = stream.peek();\n    if (ch == '\"') {\n      stream.next();\n      return chain(stream, state, tokenString('\"', \"string\"));\n    }\n    // Binary number\n    else if (ch == \"b\") {\n      stream.next();\n      stream.eatWhile(/[01]/);\n      return \"number\";\n    }\n    // Hex number\n    else if (ch == \"x\") {\n      stream.next();\n      stream.eatWhile(/[\\da-f]/i);\n      return \"number\";\n    }\n    // Octal number\n    else if (ch == \"o\") {\n      stream.next();\n      stream.eatWhile(/[0-7]/);\n      return \"number\";\n    }\n    // Token concatenation in macros\n    else if (ch == '#') {\n      stream.next();\n      return \"punctuation\";\n    }\n    // Sequence literals\n    else if ((ch == '[') || (ch == '(')) {\n      stream.next();\n      return \"bracket\";\n      // Hash symbol\n    } else if (stream.match(/f|t|all-keys|include|key|next|rest/i)) {\n      return \"atom\";\n    } else {\n      stream.eatWhile(/[-a-zA-Z]/);\n      return \"error\";\n    }\n  } else if (ch == \"~\") {\n    stream.next();\n    ch = stream.peek();\n    if (ch == \"=\") {\n      stream.next();\n      ch = stream.peek();\n      if (ch == \"=\") {\n        stream.next();\n        return \"operator\";\n      }\n      return \"operator\";\n    }\n    return \"operator\";\n  } else if (ch == \":\") {\n    stream.next();\n    ch = stream.peek();\n    if (ch == \"=\") {\n      stream.next();\n      return \"operator\";\n    } else if (ch == \":\") {\n      stream.next();\n      return \"punctuation\";\n    }\n  } else if (\"[](){}\".indexOf(ch) != -1) {\n    stream.next();\n    return \"bracket\";\n  } else if (\".,\".indexOf(ch) != -1) {\n    stream.next();\n    return \"punctuation\";\n  } else if (stream.match(\"end\")) {\n    return \"keyword\";\n  }\n  for (var name in patterns) {\n    if (patterns.hasOwnProperty(name)) {\n      var pattern = patterns[name];\n      if ((pattern instanceof Array && some(pattern, function(p) {\n        return stream.match(p);\n      })) || stream.match(pattern))\n        return patternStyles[name];\n    }\n  }\n  if (/[+\\-*\\/^=<>&|]/.test(ch)) {\n    stream.next();\n    return \"operator\";\n  }\n  if (stream.match(\"define\")) {\n    return \"def\";\n  } else {\n    stream.eatWhile(/[\\w\\-]/);\n    // Keyword\n    if (wordLookup.hasOwnProperty(stream.current())) {\n      return styleLookup[stream.current()];\n    } else if (stream.current().match(symbol)) {\n      return \"variable\";\n    } else {\n      stream.next();\n      return \"variableName.standard\";\n    }\n  }\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, maybeNested = false, nestedCount = 0, ch;\n  while ((ch = stream.next())) {\n    if (ch == \"/\" && maybeEnd) {\n      if (nestedCount > 0) {\n        nestedCount--;\n      } else {\n        state.tokenize = tokenBase;\n        break;\n      }\n    } else if (ch == \"*\" && maybeNested) {\n      nestedCount++;\n    }\n    maybeEnd = (ch == \"*\");\n    maybeNested = (ch == \"/\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote, style) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped) {\n      state.tokenize = tokenBase;\n    }\n    return style;\n  };\n}\n\n// Interface\nexport const dylan = {\n  name: \"dylan\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      currentIndent: 0\n    };\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace())\n      return null;\n    var style = state.tokenize(stream, state);\n    return style;\n  },\n  languageData: {\n    commentTokens: {block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n\n"], "mappings": ";;;AAAA,SAAS,QAAQ,KAAK,GAAG;AACvB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,GAAE,IAAI,CAAC,GAAG,CAAC;AAClD;AACA,SAAS,KAAK,KAAK,GAAG;AACpB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAAG,QAAO;AAC9D,SAAO;AACT;AAGA,IAAI,QAAQ;AAAA;AAAA,EAEV,mBAAmB,CAAC,WAAW;AAAA;AAAA,EAG/B,iBAAiB;AAAA,IAAC;AAAA,IAAU;AAAA,IAAW;AAAA,IACrB;AAAA,IAAY;AAAA,IACZ;AAAA,IAAc;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKjB,6BAA6B,CAAC,SAAS,aAAa,kBAAkB;AAAA;AAAA;AAAA,EAItE,8BAA8B;AAAA,IAAC;AAAA,IAAU;AAAA,IACV;AAAA,IAAc;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAK9B,0BAA0B,CAAC,UAAU;AAAA;AAAA;AAAA;AAAA,EAKrC,0BAA0B,CAAC,UAAU;AAAA;AAAA;AAAA,EAIrC,uBAAuB;AAAA,IAAC;AAAA,IAAW;AAAA,IACX;AAAA,IACA;AAAA,EACD;AAAA;AAAA,EAGvB,WAAW;AAAA,IAAC;AAAA,IAAM;AAAA,IAAS;AAAA,IAAS;AAAA,IAAU;AAAA,IAClC;AAAA,IAAO;AAAA,IAAU;AAAA,IAAQ;AAAA,IAAU;AAAA,IACnC;AAAA,IAAS;AAAA,IAAW;AAAA,IAAa;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKX,WAAW;AAAA,IAAC;AAAA,IAAW;AAAA,IAAa;AAAA,IAAW;AAAA,IACnC;AAAA,IAAU;AAAA,EACX;AAAA;AAAA;AAAA,EAIX,OAAO;AAAA,IAAC;AAAA,IAAS;AAAA,IAAS;AAAA,IAAM;AAAA,IAAQ;AAAA,IAAW;AAAA,IAC3C;AAAA,IAAY;AAAA,IAAO;AAAA,IAAS;AAAA,IAAa;AAAA,IACzC;AAAA,IAAY;AAAA,IAAQ;AAAA,IAAM;AAAA,IAAY;AAAA,EACvC;AAAA;AAAA,EAGP,gBAAgB;AAAA,IAAC;AAAA,IAAU;AAAA,IAAS;AAAA,IACnB;AAAA,IAAS;AAAA,IAAc;AAAA,EACxB;AAClB;AAEA,MAAM,iBAAiB,IACrB,MAAM,mBAAmB,EACxB,OAAO,MAAM,iBAAiB,CAAC,EAC/B,OAAO,MAAM,8BAA8B,CAAC;AAE/C,MAAM,YAAY,IAChB,MAAM,6BAA6B,EAClC,OAAO,MAAM,iBAAiB,CAAC;AAElC,MAAM,yBAAyB,IAC7B,MAAM,6BAA6B,EAClC,OAAO,MAAM,8BAA8B,CAAC;AAE/C,MAAM,kBAAkB,IACtB,MAAM,0BAA0B,EAC/B,OAAO,MAAM,0BAA0B,CAAC,EACxC,OAAO,MAAM,uBAAuB,CAAC;AAExC,MAAM,SAAS,IACb,MAAM,WAAW,EAChB,OAAO,MAAM,WAAW,CAAC,EACzB,OAAO,MAAM,OAAO,CAAC;AAGxB,IAAI,gBAAgB;AACpB,IAAI,SAAS,IAAI,OAAO,MAAM,aAAa;AAC3C,IAAI,WAAW;AAAA;AAAA,EAEb,eAAe,gBAAgB;AAAA,EAC/B,aAAa,MAAM,gBAAgB;AAAA,EACnC,cAAc,QAAQ,gBAAgB;AAAA,EACtC,gBAAgB,QAAQ;AAC1B;AACA,IAAI,gBAAgB;AAAA,EAClB,eAAe;AAAA,EACf,aAAa;AAAA,EACb,cAAc;AAAA,EACd,gBAAgB;AAClB;AAGA,KAAS,eAAe;AACtB,MAAI,SAAS,eAAe,WAAW;AACrC,aAAS,WAAW,IAAI,IAAI,OAAO,MAAM,SAAS,WAAW,CAAC;AAFzD;AAMT,SAAS,SAAS,IAAI,CAAC,mCAAmC;AAE1D,IAAI,SAAS,CAAC;AACd,OAAO,SAAS,IAAI;AACpB,OAAO,YAAY,IAAI;AACvB,OAAO,kBAAkB,IAAI;AAC7B,OAAO,gBAAgB,IAAI;AAG3B,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC;AAEnB,QAAQ;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG,SAAS,MAAM;AAChB,UAAQ,MAAM,IAAI,GAAG,SAAS,MAAM;AAClC,eAAW,IAAI,IAAI;AACnB,gBAAY,IAAI,IAAI,OAAO,IAAI;AAAA,EACjC,CAAC;AACH,CAAC;AAGD,SAAS,MAAM,QAAQ,OAAO,GAAG;AAC/B,QAAM,WAAW;AACjB,SAAO,EAAE,QAAQ,KAAK;AACxB;AAEA,SAAS,UAAU,QAAQ,OAAO;AAEhC,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,WAAO,KAAK;AACZ,WAAO,MAAM,QAAQ,OAAO,YAAY,IAAI,QAAQ,CAAC;AAAA,EACvD,WAES,MAAM,KAAK;AAClB,WAAO,KAAK;AACZ,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,MAAM,QAAQ,OAAO,YAAY;AAAA,IAC1C,WAAW,OAAO,IAAI,GAAG,GAAG;AAC1B,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AACA,WAAO,OAAO,CAAC;AAAA,EACjB,WAES,YAAY,KAAK,EAAE,GAAG;AAC7B,QAAI,OAAO,MAAM,2CAA2C,KACxD,OAAO,MAAM,kCAAkC,KAC/C,OAAO,MAAM,WAAW,GAAG;AAC7B,aAAO;AAAA,IACT;AAAA,EACF,WAES,MAAM,KAAK;AAClB,WAAO,KAAK;AAEZ,SAAK,OAAO,KAAK;AACjB,QAAI,MAAM,KAAK;AACb,aAAO,KAAK;AACZ,aAAO,MAAM,QAAQ,OAAO,YAAY,KAAK,QAAQ,CAAC;AAAA,IACxD,WAES,MAAM,KAAK;AAClB,aAAO,KAAK;AACZ,aAAO,SAAS,MAAM;AACtB,aAAO;AAAA,IACT,WAES,MAAM,KAAK;AAClB,aAAO,KAAK;AACZ,aAAO,SAAS,UAAU;AAC1B,aAAO;AAAA,IACT,WAES,MAAM,KAAK;AAClB,aAAO,KAAK;AACZ,aAAO,SAAS,OAAO;AACvB,aAAO;AAAA,IACT,WAES,MAAM,KAAK;AAClB,aAAO,KAAK;AACZ,aAAO;AAAA,IACT,WAEU,MAAM,OAAS,MAAM,KAAM;AACnC,aAAO,KAAK;AACZ,aAAO;AAAA,IAET,WAAW,OAAO,MAAM,qCAAqC,GAAG;AAC9D,aAAO;AAAA,IACT,OAAO;AACL,aAAO,SAAS,WAAW;AAC3B,aAAO;AAAA,IACT;AAAA,EACF,WAAW,MAAM,KAAK;AACpB,WAAO,KAAK;AACZ,SAAK,OAAO,KAAK;AACjB,QAAI,MAAM,KAAK;AACb,aAAO,KAAK;AACZ,WAAK,OAAO,KAAK;AACjB,UAAI,MAAM,KAAK;AACb,eAAO,KAAK;AACZ,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,WAAW,MAAM,KAAK;AACpB,WAAO,KAAK;AACZ,SAAK,OAAO,KAAK;AACjB,QAAI,MAAM,KAAK;AACb,aAAO,KAAK;AACZ,aAAO;AAAA,IACT,WAAW,MAAM,KAAK;AACpB,aAAO,KAAK;AACZ,aAAO;AAAA,IACT;AAAA,EACF,WAAW,SAAS,QAAQ,EAAE,KAAK,IAAI;AACrC,WAAO,KAAK;AACZ,WAAO;AAAA,EACT,WAAW,KAAK,QAAQ,EAAE,KAAK,IAAI;AACjC,WAAO,KAAK;AACZ,WAAO;AAAA,EACT,WAAW,OAAO,MAAM,KAAK,GAAG;AAC9B,WAAO;AAAA,EACT;AACA,WAAS,QAAQ,UAAU;AACzB,QAAI,SAAS,eAAe,IAAI,GAAG;AACjC,UAAI,UAAU,SAAS,IAAI;AAC3B,UAAK,mBAAmB,SAAS,KAAK,SAAS,SAAS,GAAG;AACzD,eAAO,OAAO,MAAM,CAAC;AAAA,MACvB,CAAC,KAAM,OAAO,MAAM,OAAO;AACzB,eAAO,cAAc,IAAI;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,iBAAiB,KAAK,EAAE,GAAG;AAC7B,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,QAAQ,GAAG;AAC1B,WAAO;AAAA,EACT,OAAO;AACL,WAAO,SAAS,QAAQ;AAExB,QAAI,WAAW,eAAe,OAAO,QAAQ,CAAC,GAAG;AAC/C,aAAO,YAAY,OAAO,QAAQ,CAAC;AAAA,IACrC,WAAW,OAAO,QAAQ,EAAE,MAAM,MAAM,GAAG;AACzC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,KAAK;AACZ,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO,cAAc,OAAO,cAAc,GAAG;AAC5D,SAAQ,KAAK,OAAO,KAAK,GAAI;AAC3B,QAAI,MAAM,OAAO,UAAU;AACzB,UAAI,cAAc,GAAG;AACnB;AAAA,MACF,OAAO;AACL,cAAM,WAAW;AACjB;AAAA,MACF;AAAA,IACF,WAAW,MAAM,OAAO,aAAa;AACnC;AAAA,IACF;AACA,eAAY,MAAM;AAClB,kBAAe,MAAM;AAAA,EACvB;AACA,SAAO;AACT;AAEA,SAAS,YAAY,OAAO,OAAO;AACjC,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO,MAAM,MAAM;AACjC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,QAAQ,SAAS,CAAC,SAAS;AAC7B,cAAM;AACN;AAAA,MACF;AACA,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,QAAI,OAAO,CAAC,SAAS;AACnB,YAAM,WAAW;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AACF;AAGO,IAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS;AAClB,aAAO;AACT,QAAI,QAAQ,MAAM,SAAS,QAAQ,KAAK;AACxC,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,eAAe,EAAC,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAClD;AACF;", "names": []}