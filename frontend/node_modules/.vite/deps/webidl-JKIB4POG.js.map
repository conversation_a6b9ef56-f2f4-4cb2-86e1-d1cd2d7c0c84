{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/webidl.js"], "sourcesContent": ["function wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n};\n\nvar builtinArray = [\n  \"Clamp\",\n  \"Constructor\",\n  \"EnforceRange\",\n  \"Exposed\",\n  \"ImplicitThis\",\n  \"Global\", \"PrimaryGlobal\",\n  \"LegacyArrayClass\",\n  \"LegacyUnenumerableNamedProperties\",\n  \"LenientThis\",\n  \"NamedConstructor\",\n  \"NewObject\",\n  \"NoInterfaceObject\",\n  \"OverrideBuiltins\",\n  \"PutForwards\",\n  \"Replaceable\",\n  \"SameObject\",\n  \"TreatNonObjectAsNull\",\n  \"TreatNullAs\",\n    \"EmptyString\",\n  \"Unforgeable\",\n  \"Unscopeable\"\n];\nvar builtins = wordRegexp(builtinArray);\n\nvar typeArray = [\n  \"unsigned\", \"short\", \"long\",                  // UnsignedIntegerType\n  \"unrestricted\", \"float\", \"double\",            // UnrestrictedFloatType\n  \"boolean\", \"byte\", \"octet\",                   // Rest of PrimitiveType\n  \"Promise\",                                    // PromiseType\n  \"ArrayBuffer\", \"DataView\", \"Int8Array\", \"Int16Array\", \"Int32Array\",\n  \"Uint8Array\", \"Uint16Array\", \"Uint32Array\", \"Uint8ClampedArray\",\n  \"Float32Array\", \"Float64Array\",               // BufferRelatedType\n  \"ByteString\", \"DOMString\", \"USVString\", \"sequence\", \"object\", \"RegExp\",\n  \"Error\", \"DOMException\", \"FrozenArray\",       // Rest of NonAnyType\n  \"any\",                                        // Rest of SingleType\n  \"void\"                                        // Rest of ReturnType\n];\nvar types = wordRegexp(typeArray);\n\nvar keywordArray = [\n  \"attribute\", \"callback\", \"const\", \"deleter\", \"dictionary\", \"enum\", \"getter\",\n  \"implements\", \"inherit\", \"interface\", \"iterable\", \"legacycaller\", \"maplike\",\n  \"partial\", \"required\", \"serializer\", \"setlike\", \"setter\", \"static\",\n  \"stringifier\", \"typedef\",                     // ArgumentNameKeyword except\n                                                // \"unrestricted\"\n  \"optional\", \"readonly\", \"or\"\n];\nvar keywords = wordRegexp(keywordArray);\n\nvar atomArray = [\n  \"true\", \"false\",                              // BooleanLiteral\n  \"Infinity\", \"NaN\",                            // FloatLiteral\n  \"null\"                                        // Rest of ConstValue\n];\nvar atoms = wordRegexp(atomArray);\n\nvar startDefArray = [\"callback\", \"dictionary\", \"enum\", \"interface\"];\nvar startDefs = wordRegexp(startDefArray);\n\nvar endDefArray = [\"typedef\"];\nvar endDefs = wordRegexp(endDefArray);\n\nvar singleOperators = /^[:<=>?]/;\nvar integers = /^-?([1-9][0-9]*|0[Xx][0-9A-Fa-f]+|0[0-7]*)/;\nvar floats = /^-?(([0-9]+\\.[0-9]*|[0-9]*\\.[0-9]+)([Ee][+-]?[0-9]+)?|[0-9]+[Ee][+-]?[0-9]+)/;\nvar identifiers = /^_?[A-Za-z][0-9A-Z_a-z-]*/;\nvar identifiersEnd = /^_?[A-Za-z][0-9A-Z_a-z-]*(?=\\s*;)/;\nvar strings = /^\"[^\"]*\"/;\nvar multilineComments = /^\\/\\*.*?\\*\\//;\nvar multilineCommentsStart = /^\\/\\*.*/;\nvar multilineCommentsEnd = /^.*?\\*\\//;\n\nfunction readToken(stream, state) {\n  // whitespace\n  if (stream.eatSpace()) return null;\n\n  // comment\n  if (state.inComment) {\n    if (stream.match(multilineCommentsEnd)) {\n      state.inComment = false;\n      return \"comment\";\n    }\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (stream.match(\"//\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (stream.match(multilineComments)) return \"comment\";\n  if (stream.match(multilineCommentsStart)) {\n    state.inComment = true;\n    return \"comment\";\n  }\n\n  // integer and float\n  if (stream.match(/^-?[0-9\\.]/, false)) {\n    if (stream.match(integers) || stream.match(floats)) return \"number\";\n  }\n\n  // string\n  if (stream.match(strings)) return \"string\";\n\n  // identifier\n  if (state.startDef && stream.match(identifiers)) return \"def\";\n\n  if (state.endDef && stream.match(identifiersEnd)) {\n    state.endDef = false;\n    return \"def\";\n  }\n\n  if (stream.match(keywords)) return \"keyword\";\n\n  if (stream.match(types)) {\n    var lastToken = state.lastToken;\n    var nextToken = (stream.match(/^\\s*(.+?)\\b/, false) || [])[1];\n\n    if (lastToken === \":\" || lastToken === \"implements\" ||\n        nextToken === \"implements\" || nextToken === \"=\") {\n      // Used as identifier\n      return \"builtin\";\n    } else {\n      // Used as type\n      return \"type\";\n    }\n  }\n\n  if (stream.match(builtins)) return \"builtin\";\n  if (stream.match(atoms)) return \"atom\";\n  if (stream.match(identifiers)) return \"variable\";\n\n  // other\n  if (stream.match(singleOperators)) return \"operator\";\n\n  // unrecognized\n  stream.next();\n  return null;\n};\n\nexport const webIDL = {\n  name: \"webidl\",\n  startState: function() {\n    return {\n      // Is in multiline comment\n      inComment: false,\n      // Last non-whitespace, matched token\n      lastToken: \"\",\n      // Next token is a definition\n      startDef: false,\n      // Last token of the statement is a definition\n      endDef: false\n    };\n  },\n  token: function(stream, state) {\n    var style = readToken(stream, state);\n\n    if (style) {\n      var cur = stream.current();\n      state.lastToken = cur;\n      if (style === \"keyword\") {\n        state.startDef = startDefs.test(cur);\n        state.endDef = state.endDef || endDefs.test(cur);\n      } else {\n        state.startDef = false;\n      }\n    }\n\n    return style;\n  },\n\n  languageData: {\n    autocomplete: builtinArray.concat(typeArray).concat(keywordArray).concat(atomArray)\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,WAAW,OAAO;AACzB,SAAO,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,IAAI,OAAO;AACvD;AAEA,IAAI,eAAe;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACE;AAAA,EACF;AAAA,EACA;AACF;AACA,IAAI,WAAW,WAAW,YAAY;AAEtC,IAAI,YAAY;AAAA,EACd;AAAA,EAAY;AAAA,EAAS;AAAA;AAAA,EACrB;AAAA,EAAgB;AAAA,EAAS;AAAA;AAAA,EACzB;AAAA,EAAW;AAAA,EAAQ;AAAA;AAAA,EACnB;AAAA;AAAA,EACA;AAAA,EAAe;AAAA,EAAY;AAAA,EAAa;AAAA,EAAc;AAAA,EACtD;AAAA,EAAc;AAAA,EAAe;AAAA,EAAe;AAAA,EAC5C;AAAA,EAAgB;AAAA;AAAA,EAChB;AAAA,EAAc;AAAA,EAAa;AAAA,EAAa;AAAA,EAAY;AAAA,EAAU;AAAA,EAC9D;AAAA,EAAS;AAAA,EAAgB;AAAA;AAAA,EACzB;AAAA;AAAA,EACA;AAAA;AACF;AACA,IAAI,QAAQ,WAAW,SAAS;AAEhC,IAAI,eAAe;AAAA,EACjB;AAAA,EAAa;AAAA,EAAY;AAAA,EAAS;AAAA,EAAW;AAAA,EAAc;AAAA,EAAQ;AAAA,EACnE;AAAA,EAAc;AAAA,EAAW;AAAA,EAAa;AAAA,EAAY;AAAA,EAAgB;AAAA,EAClE;AAAA,EAAW;AAAA,EAAY;AAAA,EAAc;AAAA,EAAW;AAAA,EAAU;AAAA,EAC1D;AAAA,EAAe;AAAA;AAAA;AAAA,EAEf;AAAA,EAAY;AAAA,EAAY;AAC1B;AACA,IAAI,WAAW,WAAW,YAAY;AAEtC,IAAI,YAAY;AAAA,EACd;AAAA,EAAQ;AAAA;AAAA,EACR;AAAA,EAAY;AAAA;AAAA,EACZ;AAAA;AACF;AACA,IAAI,QAAQ,WAAW,SAAS;AAEhC,IAAI,gBAAgB,CAAC,YAAY,cAAc,QAAQ,WAAW;AAClE,IAAI,YAAY,WAAW,aAAa;AAExC,IAAI,cAAc,CAAC,SAAS;AAC5B,IAAI,UAAU,WAAW,WAAW;AAEpC,IAAI,kBAAkB;AACtB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,UAAU;AACd,IAAI,oBAAoB;AACxB,IAAI,yBAAyB;AAC7B,IAAI,uBAAuB;AAE3B,SAAS,UAAU,QAAQ,OAAO;AAEhC,MAAI,OAAO,SAAS,EAAG,QAAO;AAG9B,MAAI,MAAM,WAAW;AACnB,QAAI,OAAO,MAAM,oBAAoB,GAAG;AACtC,YAAM,YAAY;AAClB,aAAO;AAAA,IACT;AACA,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,iBAAiB,EAAG,QAAO;AAC5C,MAAI,OAAO,MAAM,sBAAsB,GAAG;AACxC,UAAM,YAAY;AAClB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,cAAc,KAAK,GAAG;AACrC,QAAI,OAAO,MAAM,QAAQ,KAAK,OAAO,MAAM,MAAM,EAAG,QAAO;AAAA,EAC7D;AAGA,MAAI,OAAO,MAAM,OAAO,EAAG,QAAO;AAGlC,MAAI,MAAM,YAAY,OAAO,MAAM,WAAW,EAAG,QAAO;AAExD,MAAI,MAAM,UAAU,OAAO,MAAM,cAAc,GAAG;AAChD,UAAM,SAAS;AACf,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,QAAQ,EAAG,QAAO;AAEnC,MAAI,OAAO,MAAM,KAAK,GAAG;AACvB,QAAI,YAAY,MAAM;AACtB,QAAI,aAAa,OAAO,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC;AAE5D,QAAI,cAAc,OAAO,cAAc,gBACnC,cAAc,gBAAgB,cAAc,KAAK;AAEnD,aAAO;AAAA,IACT,OAAO;AAEL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,OAAO,MAAM,QAAQ,EAAG,QAAO;AACnC,MAAI,OAAO,MAAM,KAAK,EAAG,QAAO;AAChC,MAAI,OAAO,MAAM,WAAW,EAAG,QAAO;AAGtC,MAAI,OAAO,MAAM,eAAe,EAAG,QAAO;AAG1C,SAAO,KAAK;AACZ,SAAO;AACT;AAEO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA;AAAA,MAEL,WAAW;AAAA;AAAA,MAEX,WAAW;AAAA;AAAA,MAEX,UAAU;AAAA;AAAA,MAEV,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,QAAQ,UAAU,QAAQ,KAAK;AAEnC,QAAI,OAAO;AACT,UAAI,MAAM,OAAO,QAAQ;AACzB,YAAM,YAAY;AAClB,UAAI,UAAU,WAAW;AACvB,cAAM,WAAW,UAAU,KAAK,GAAG;AACnC,cAAM,SAAS,MAAM,UAAU,QAAQ,KAAK,GAAG;AAAA,MACjD,OAAO;AACL,cAAM,WAAW;AAAA,MACnB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,cAAc;AAAA,IACZ,cAAc,aAAa,OAAO,SAAS,EAAE,OAAO,YAAY,EAAE,OAAO,SAAS;AAAA,EACpF;AACF;", "names": []}