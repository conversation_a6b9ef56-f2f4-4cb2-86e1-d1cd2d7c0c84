{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/pascal.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\nvar keywords = words(\n  \"absolute and array asm begin case const constructor destructor div do \" +\n    \"downto else end file for function goto if implementation in inherited \" +\n    \"inline interface label mod nil not object of operator or packed procedure \" +\n    \"program record reintroduce repeat self set shl shr string then to type \" +\n    \"unit until uses var while with xor as class dispinterface except exports \" +\n    \"finalization finally initialization inline is library on out packed \" +\n    \"property raise resourcestring threadvar try absolute abstract alias \" +\n    \"assembler bitpacked break cdecl continue cppdecl cvar default deprecated \" +\n    \"dynamic enumerator experimental export external far far16 forward generic \" +\n    \"helper implements index interrupt iocheck local message name near \" +\n    \"nodefault noreturn nostackframe oldfpccall otherwise overload override \" +\n    \"pascal platform private protected public published read register \" +\n    \"reintroduce result safecall saveregisters softfloat specialize static \" +\n    \"stdcall stored strict unaligned unimplemented varargs virtual write\");\nvar atoms = {\"null\": true};\n\nvar isOperatorChar = /[+\\-*&%=<>!?|\\/]/;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == \"#\" && state.startOfLine) {\n    stream.skipToEnd();\n    return \"meta\";\n  }\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (ch == \"(\" && stream.eat(\"*\")) {\n    state.tokenize = tokenComment;\n    return tokenComment(stream, state);\n  }\n  if (ch == \"{\") {\n    state.tokenize = tokenCommentBraces;\n    return tokenCommentBraces(stream, state);\n  }\n  if (/[\\[\\]\\(\\),;\\:\\.]/.test(ch)) {\n    return null;\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (ch == \"/\") {\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  stream.eatWhile(/[\\w\\$_]/);\n  var cur = stream.current().toLowerCase();\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  if (atoms.propertyIsEnumerable(cur)) return \"atom\";\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped) {end = true; break;}\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped) state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction tokenComment(stream, state) {\n  var maybeEnd = false, ch;\n  while (ch = stream.next()) {\n    if (ch == \")\" && maybeEnd) {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenCommentBraces(stream, state) {\n  var ch;\n  while (ch = stream.next()) {\n    if (ch == \"}\") {\n      state.tokenize = null;\n      break;\n    }\n  }\n  return \"comment\";\n}\n\n// Interface\n\nexport const pascal = {\n  name: \"pascal\",\n\n  startState: function() {\n    return {tokenize: null};\n  },\n\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\" || style == \"meta\") return style;\n    return style;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {block: {open: \"(*\", close: \"*)\"}}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,MAAM,KAAK;AAClB,MAAI,MAAM,CAAC,GAAGA,SAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE,EAAG,KAAIA,OAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AACA,IAAI,WAAW;AAAA,EACb;AAauE;AACzE,IAAI,QAAQ,EAAC,QAAQ,KAAI;AAEzB,IAAI,iBAAiB;AAErB,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI,MAAM,OAAO,MAAM,aAAa;AAClC,WAAO,UAAU;AACjB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,UAAM,WAAW,YAAY,EAAE;AAC/B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AACA,MAAI,MAAM,OAAO,OAAO,IAAI,GAAG,GAAG;AAChC,UAAM,WAAW;AACjB,WAAO,aAAa,QAAQ,KAAK;AAAA,EACnC;AACA,MAAI,MAAM,KAAK;AACb,UAAM,WAAW;AACjB,WAAO,mBAAmB,QAAQ,KAAK;AAAA,EACzC;AACA,MAAI,mBAAmB,KAAK,EAAE,GAAG;AAC/B,WAAO;AAAA,EACT;AACA,MAAI,KAAK,KAAK,EAAE,GAAG;AACjB,WAAO,SAAS,QAAQ;AACxB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,KAAK;AACb,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,eAAe,KAAK,EAAE,GAAG;AAC3B,WAAO,SAAS,cAAc;AAC9B,WAAO;AAAA,EACT;AACA,SAAO,SAAS,SAAS;AACzB,MAAI,MAAM,OAAO,QAAQ,EAAE,YAAY;AACvC,MAAI,SAAS,qBAAqB,GAAG,EAAG,QAAO;AAC/C,MAAI,MAAM,qBAAqB,GAAG,EAAG,QAAO;AAC5C,SAAO;AACT;AAEA,SAAS,YAAY,OAAO;AAC1B,SAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,UAAU,OAAO,MAAM,MAAM;AACjC,YAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,UAAI,QAAQ,SAAS,CAAC,SAAS;AAAC,cAAM;AAAM;AAAA,MAAM;AAClD,gBAAU,CAAC,WAAW,QAAQ;AAAA,IAChC;AACA,QAAI,OAAO,CAAC,QAAS,OAAM,WAAW;AACtC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,WAAW,OAAO;AACtB,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,OAAO,UAAU;AACzB,YAAM,WAAW;AACjB;AAAA,IACF;AACA,eAAY,MAAM;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,QAAQ,OAAO;AACzC,MAAI;AACJ,SAAO,KAAK,OAAO,KAAK,GAAG;AACzB,QAAI,MAAM,KAAK;AACb,YAAM,WAAW;AACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAIO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EAEN,YAAY,WAAW;AACrB,WAAO,EAAC,UAAU,KAAI;AAAA,EACxB;AAAA,EAEA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,QAAI,SAAS,MAAM,YAAY,WAAW,QAAQ,KAAK;AACvD,QAAI,SAAS,aAAa,SAAS,OAAQ,QAAO;AAClD,WAAO;AAAA,EACT;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAClD;AACF;", "names": ["words"]}