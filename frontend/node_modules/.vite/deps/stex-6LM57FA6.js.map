{"version": 3, "sources": ["../../@codemirror/legacy-modes/mode/stex.js"], "sourcesContent": ["function mkStex(mathMode) {\n  function pushCommand(state, command) {\n    state.cmdState.push(command);\n  }\n\n  function peekCommand(state) {\n    if (state.cmdState.length > 0) {\n      return state.cmdState[state.cmdState.length - 1];\n    } else {\n      return null;\n    }\n  }\n\n  function popCommand(state) {\n    var plug = state.cmdState.pop();\n    if (plug) {\n      plug.closeBracket();\n    }\n  }\n\n  // returns the non-default plugin closest to the end of the list\n  function getMostPowerful(state) {\n    var context = state.cmdState;\n    for (var i = context.length - 1; i >= 0; i--) {\n      var plug = context[i];\n      if (plug.name == \"DEFAULT\") {\n        continue;\n      }\n      return plug;\n    }\n    return { styleIdentifier: function() { return null; } };\n  }\n\n  function addPluginPattern(pluginName, cmdStyle, styles) {\n    return function () {\n      this.name = pluginName;\n      this.bracketNo = 0;\n      this.style = cmdStyle;\n      this.styles = styles;\n      this.argument = null;   // \\begin and \\end have arguments that follow. These are stored in the plugin\n\n      this.styleIdentifier = function() {\n        return this.styles[this.bracketNo - 1] || null;\n      };\n      this.openBracket = function() {\n        this.bracketNo++;\n        return \"bracket\";\n      };\n      this.closeBracket = function() {};\n    };\n  }\n\n  var plugins = {};\n\n  plugins[\"importmodule\"] = addPluginPattern(\"importmodule\", \"tag\", [\"string\", \"builtin\"]);\n  plugins[\"documentclass\"] = addPluginPattern(\"documentclass\", \"tag\", [\"\", \"atom\"]);\n  plugins[\"usepackage\"] = addPluginPattern(\"usepackage\", \"tag\", [\"atom\"]);\n  plugins[\"begin\"] = addPluginPattern(\"begin\", \"tag\", [\"atom\"]);\n  plugins[\"end\"] = addPluginPattern(\"end\", \"tag\", [\"atom\"]);\n\n  plugins[\"label\"    ] = addPluginPattern(\"label\"    , \"tag\", [\"atom\"]);\n  plugins[\"ref\"      ] = addPluginPattern(\"ref\"      , \"tag\", [\"atom\"]);\n  plugins[\"eqref\"    ] = addPluginPattern(\"eqref\"    , \"tag\", [\"atom\"]);\n  plugins[\"cite\"     ] = addPluginPattern(\"cite\"     , \"tag\", [\"atom\"]);\n  plugins[\"bibitem\"  ] = addPluginPattern(\"bibitem\"  , \"tag\", [\"atom\"]);\n  plugins[\"Bibitem\"  ] = addPluginPattern(\"Bibitem\"  , \"tag\", [\"atom\"]);\n  plugins[\"RBibitem\" ] = addPluginPattern(\"RBibitem\" , \"tag\", [\"atom\"]);\n\n  plugins[\"DEFAULT\"] = function () {\n    this.name = \"DEFAULT\";\n    this.style = \"tag\";\n\n    this.styleIdentifier = this.openBracket = this.closeBracket = function() {};\n  };\n\n  function setState(state, f) {\n    state.f = f;\n  }\n\n  // called when in a normal (no environment) context\n  function normal(source, state) {\n    var plug;\n    // Do we look like '\\command' ?  If so, attempt to apply the plugin 'command'\n    if (source.match(/^\\\\[a-zA-Z@\\xc0-\\u1fff\\u2060-\\uffff]+/)) {\n      var cmdName = source.current().slice(1);\n      plug = plugins.hasOwnProperty(cmdName) ? plugins[cmdName] : plugins[\"DEFAULT\"];\n      plug = new plug();\n      pushCommand(state, plug);\n      setState(state, beginParams);\n      return plug.style;\n    }\n\n    // escape characters\n    if (source.match(/^\\\\[$&%#{}_]/)) {\n      return \"tag\";\n    }\n\n    // white space control characters\n    if (source.match(/^\\\\[,;!\\/\\\\]/)) {\n      return \"tag\";\n    }\n\n    // find if we're starting various math modes\n    if (source.match(\"\\\\[\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"\\\\]\"); });\n      return \"keyword\";\n    }\n    if (source.match(\"\\\\(\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"\\\\)\"); });\n      return \"keyword\";\n    }\n    if (source.match(\"$$\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"$$\"); });\n      return \"keyword\";\n    }\n    if (source.match(\"$\")) {\n      setState(state, function(source, state){ return inMathMode(source, state, \"$\"); });\n      return \"keyword\";\n    }\n\n    var ch = source.next();\n    if (ch == \"%\") {\n      source.skipToEnd();\n      return \"comment\";\n    } else if (ch == '}' || ch == ']') {\n      plug = peekCommand(state);\n      if (plug) {\n        plug.closeBracket(ch);\n        setState(state, beginParams);\n      } else {\n        return \"error\";\n      }\n      return \"bracket\";\n    } else if (ch == '{' || ch == '[') {\n      plug = plugins[\"DEFAULT\"];\n      plug = new plug();\n      pushCommand(state, plug);\n      return \"bracket\";\n    } else if (/\\d/.test(ch)) {\n      source.eatWhile(/[\\w.%]/);\n      return \"atom\";\n    } else {\n      source.eatWhile(/[\\w\\-_]/);\n      plug = getMostPowerful(state);\n      if (plug.name == 'begin') {\n        plug.argument = source.current();\n      }\n      return plug.styleIdentifier();\n    }\n  }\n\n  function inMathMode(source, state, endModeSeq) {\n    if (source.eatSpace()) {\n      return null;\n    }\n    if (endModeSeq && source.match(endModeSeq)) {\n      setState(state, normal);\n      return \"keyword\";\n    }\n    if (source.match(/^\\\\[a-zA-Z@]+/)) {\n      return \"tag\";\n    }\n    if (source.match(/^[a-zA-Z]+/)) {\n      return \"variableName.special\";\n    }\n    // escape characters\n    if (source.match(/^\\\\[$&%#{}_]/)) {\n      return \"tag\";\n    }\n    // white space control characters\n    if (source.match(/^\\\\[,;!\\/]/)) {\n      return \"tag\";\n    }\n    // special math-mode characters\n    if (source.match(/^[\\^_&]/)) {\n      return \"tag\";\n    }\n    // non-special characters\n    if (source.match(/^[+\\-<>|=,\\/@!*:;'\"`~#?]/)) {\n      return null;\n    }\n    if (source.match(/^(\\d+\\.\\d*|\\d*\\.\\d+|\\d+)/)) {\n      return \"number\";\n    }\n    var ch = source.next();\n    if (ch == \"{\" || ch == \"}\" || ch == \"[\" || ch == \"]\" || ch == \"(\" || ch == \")\") {\n      return \"bracket\";\n    }\n\n    if (ch == \"%\") {\n      source.skipToEnd();\n      return \"comment\";\n    }\n    return \"error\";\n  }\n\n  function beginParams(source, state) {\n    var ch = source.peek(), lastPlug;\n    if (ch == '{' || ch == '[') {\n      lastPlug = peekCommand(state);\n      lastPlug.openBracket(ch);\n      source.eat(ch);\n      setState(state, normal);\n      return \"bracket\";\n    }\n    if (/[ \\t\\r]/.test(ch)) {\n      source.eat(ch);\n      return null;\n    }\n    setState(state, normal);\n    popCommand(state);\n\n    return normal(source, state);\n  }\n\n  return {\n    name: \"stex\",\n    startState: function() {\n      var f = mathMode ? function(source, state){ return inMathMode(source, state); } : normal;\n      return {\n        cmdState: [],\n        f: f\n      };\n    },\n    copyState: function(s) {\n      return {\n        cmdState: s.cmdState.slice(),\n        f: s.f\n      };\n    },\n    token: function(stream, state) {\n      return state.f(stream, state);\n    },\n    blankLine: function(state) {\n      state.f = normal;\n      state.cmdState.length = 0;\n    },\n    languageData: {\n      commentTokens: {line: \"%\"}\n    }\n  };\n};\n\nexport const stex = mkStex(false)\nexport const stexMath = mkStex(true)\n"], "mappings": ";;;AAAA,SAAS,OAAO,UAAU;AACxB,WAAS,YAAY,OAAO,SAAS;AACnC,UAAM,SAAS,KAAK,OAAO;AAAA,EAC7B;AAEA,WAAS,YAAY,OAAO;AAC1B,QAAI,MAAM,SAAS,SAAS,GAAG;AAC7B,aAAO,MAAM,SAAS,MAAM,SAAS,SAAS,CAAC;AAAA,IACjD,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,WAAS,WAAW,OAAO;AACzB,QAAI,OAAO,MAAM,SAAS,IAAI;AAC9B,QAAI,MAAM;AACR,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAGA,WAAS,gBAAgB,OAAO;AAC9B,QAAI,UAAU,MAAM;AACpB,aAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,UAAI,OAAO,QAAQ,CAAC;AACpB,UAAI,KAAK,QAAQ,WAAW;AAC1B;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO,EAAE,iBAAiB,WAAW;AAAE,aAAO;AAAA,IAAM,EAAE;AAAA,EACxD;AAEA,WAAS,iBAAiB,YAAY,UAAU,QAAQ;AACtD,WAAO,WAAY;AACjB,WAAK,OAAO;AACZ,WAAK,YAAY;AACjB,WAAK,QAAQ;AACb,WAAK,SAAS;AACd,WAAK,WAAW;AAEhB,WAAK,kBAAkB,WAAW;AAChC,eAAO,KAAK,OAAO,KAAK,YAAY,CAAC,KAAK;AAAA,MAC5C;AACA,WAAK,cAAc,WAAW;AAC5B,aAAK;AACL,eAAO;AAAA,MACT;AACA,WAAK,eAAe,WAAW;AAAA,MAAC;AAAA,IAClC;AAAA,EACF;AAEA,MAAI,UAAU,CAAC;AAEf,UAAQ,cAAc,IAAI,iBAAiB,gBAAgB,OAAO,CAAC,UAAU,SAAS,CAAC;AACvF,UAAQ,eAAe,IAAI,iBAAiB,iBAAiB,OAAO,CAAC,IAAI,MAAM,CAAC;AAChF,UAAQ,YAAY,IAAI,iBAAiB,cAAc,OAAO,CAAC,MAAM,CAAC;AACtE,UAAQ,OAAO,IAAI,iBAAiB,SAAS,OAAO,CAAC,MAAM,CAAC;AAC5D,UAAQ,KAAK,IAAI,iBAAiB,OAAO,OAAO,CAAC,MAAM,CAAC;AAExD,UAAQ,OAAW,IAAI,iBAAiB,SAAa,OAAO,CAAC,MAAM,CAAC;AACpE,UAAQ,KAAW,IAAI,iBAAiB,OAAa,OAAO,CAAC,MAAM,CAAC;AACpE,UAAQ,OAAW,IAAI,iBAAiB,SAAa,OAAO,CAAC,MAAM,CAAC;AACpE,UAAQ,MAAW,IAAI,iBAAiB,QAAa,OAAO,CAAC,MAAM,CAAC;AACpE,UAAQ,SAAW,IAAI,iBAAiB,WAAa,OAAO,CAAC,MAAM,CAAC;AACpE,UAAQ,SAAW,IAAI,iBAAiB,WAAa,OAAO,CAAC,MAAM,CAAC;AACpE,UAAQ,UAAW,IAAI,iBAAiB,YAAa,OAAO,CAAC,MAAM,CAAC;AAEpE,UAAQ,SAAS,IAAI,WAAY;AAC/B,SAAK,OAAO;AACZ,SAAK,QAAQ;AAEb,SAAK,kBAAkB,KAAK,cAAc,KAAK,eAAe,WAAW;AAAA,IAAC;AAAA,EAC5E;AAEA,WAAS,SAAS,OAAO,GAAG;AAC1B,UAAM,IAAI;AAAA,EACZ;AAGA,WAAS,OAAO,QAAQ,OAAO;AAC7B,QAAI;AAEJ,QAAI,OAAO,MAAM,uCAAuC,GAAG;AACzD,UAAI,UAAU,OAAO,QAAQ,EAAE,MAAM,CAAC;AACtC,aAAO,QAAQ,eAAe,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ,SAAS;AAC7E,aAAO,IAAI,KAAK;AAChB,kBAAY,OAAO,IAAI;AACvB,eAAS,OAAO,WAAW;AAC3B,aAAO,KAAK;AAAA,IACd;AAGA,QAAI,OAAO,MAAM,cAAc,GAAG;AAChC,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,MAAM,cAAc,GAAG;AAChC,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,MAAM,KAAK,GAAG;AACvB,eAAS,OAAO,SAASA,SAAQC,QAAM;AAAE,eAAO,WAAWD,SAAQC,QAAO,KAAK;AAAA,MAAG,CAAC;AACnF,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,KAAK,GAAG;AACvB,eAAS,OAAO,SAASD,SAAQC,QAAM;AAAE,eAAO,WAAWD,SAAQC,QAAO,KAAK;AAAA,MAAG,CAAC;AACnF,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,IAAI,GAAG;AACtB,eAAS,OAAO,SAASD,SAAQC,QAAM;AAAE,eAAO,WAAWD,SAAQC,QAAO,IAAI;AAAA,MAAG,CAAC;AAClF,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,GAAG,GAAG;AACrB,eAAS,OAAO,SAASD,SAAQC,QAAM;AAAE,eAAO,WAAWD,SAAQC,QAAO,GAAG;AAAA,MAAG,CAAC;AACjF,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,KAAK;AACb,aAAO,UAAU;AACjB,aAAO;AAAA,IACT,WAAW,MAAM,OAAO,MAAM,KAAK;AACjC,aAAO,YAAY,KAAK;AACxB,UAAI,MAAM;AACR,aAAK,aAAa,EAAE;AACpB,iBAAS,OAAO,WAAW;AAAA,MAC7B,OAAO;AACL,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,WAAW,MAAM,OAAO,MAAM,KAAK;AACjC,aAAO,QAAQ,SAAS;AACxB,aAAO,IAAI,KAAK;AAChB,kBAAY,OAAO,IAAI;AACvB,aAAO;AAAA,IACT,WAAW,KAAK,KAAK,EAAE,GAAG;AACxB,aAAO,SAAS,QAAQ;AACxB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,SAAS,SAAS;AACzB,aAAO,gBAAgB,KAAK;AAC5B,UAAI,KAAK,QAAQ,SAAS;AACxB,aAAK,WAAW,OAAO,QAAQ;AAAA,MACjC;AACA,aAAO,KAAK,gBAAgB;AAAA,IAC9B;AAAA,EACF;AAEA,WAAS,WAAW,QAAQ,OAAO,YAAY;AAC7C,QAAI,OAAO,SAAS,GAAG;AACrB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,OAAO,MAAM,UAAU,GAAG;AAC1C,eAAS,OAAO,MAAM;AACtB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,eAAe,GAAG;AACjC,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,YAAY,GAAG;AAC9B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,MAAM,cAAc,GAAG;AAChC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,MAAM,YAAY,GAAG;AAC9B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,MAAM,SAAS,GAAG;AAC3B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,MAAM,0BAA0B,GAAG;AAC5C,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,0BAA0B,GAAG;AAC5C,aAAO;AAAA,IACT;AACA,QAAI,KAAK,OAAO,KAAK;AACrB,QAAI,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AAC9E,aAAO;AAAA,IACT;AAEA,QAAI,MAAM,KAAK;AACb,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,WAAS,YAAY,QAAQ,OAAO;AAClC,QAAI,KAAK,OAAO,KAAK,GAAG;AACxB,QAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,iBAAW,YAAY,KAAK;AAC5B,eAAS,YAAY,EAAE;AACvB,aAAO,IAAI,EAAE;AACb,eAAS,OAAO,MAAM;AACtB,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK,EAAE,GAAG;AACtB,aAAO,IAAI,EAAE;AACb,aAAO;AAAA,IACT;AACA,aAAS,OAAO,MAAM;AACtB,eAAW,KAAK;AAEhB,WAAO,OAAO,QAAQ,KAAK;AAAA,EAC7B;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,YAAY,WAAW;AACrB,UAAI,IAAI,WAAW,SAAS,QAAQ,OAAM;AAAE,eAAO,WAAW,QAAQ,KAAK;AAAA,MAAG,IAAI;AAClF,aAAO;AAAA,QACL,UAAU,CAAC;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW,SAAS,GAAG;AACrB,aAAO;AAAA,QACL,UAAU,EAAE,SAAS,MAAM;AAAA,QAC3B,GAAG,EAAE;AAAA,MACP;AAAA,IACF;AAAA,IACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,aAAO,MAAM,EAAE,QAAQ,KAAK;AAAA,IAC9B;AAAA,IACA,WAAW,SAAS,OAAO;AACzB,YAAM,IAAI;AACV,YAAM,SAAS,SAAS;AAAA,IAC1B;AAAA,IACA,cAAc;AAAA,MACZ,eAAe,EAAC,MAAM,IAAG;AAAA,IAC3B;AAAA,EACF;AACF;AAEO,IAAM,OAAO,OAAO,KAAK;AACzB,IAAM,WAAW,OAAO,IAAI;", "names": ["source", "state"]}