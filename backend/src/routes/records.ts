import { Router } from 'express'
import validator from 'express-validator'
const { body } = validator
import { RecordController } from '@/controllers/RecordController'
import { authenticateToken } from '@/middlewares/auth'
import { validate, validateId, validateSort, validatePagination } from '@/middlewares/validation'

const router = Router()

// 所有路由都需要认证
router.use(authenticateToken)

// 获取所有分析记录列表（支持跨小说查询）
router.get('/',
  validatePagination,
  validateSort(['title', 'createdAt', 'updatedAt', 'importanceLevel', 'analysisType']),
  RecordController.getAllRecords
)

// 获取记录统计信息
router.get('/statistics',
  RecordController.getRecordStatistics
)

// 获取指定小说的分析记录列表
router.get('/novels/:novelId', 
  validateId('novelId'),
  validatePagination,
  validateSort(['title', 'createdAt', 'updatedAt', 'importanceLevel', 'analysisType']),
  RecordController.getRecords
)

// 获取分析记录详情
router.get('/:id', 
  validateId('id'),
  RecordController.getRecordById
)

// 为指定小说创建分析记录
router.post('/novels/:novelId', 
  validateId('novelId'),
  validate([
    body('title')
      .optional()
      .isLength({ min: 1, max: 200 })
      .withMessage('记录标题长度必须在1-200个字符之间')
      .trim(),
    body('chapterInfo')
      .optional()
      .isLength({ min: 1, max: 100 })
      .withMessage('章节信息长度必须在1-100个字符之间')
      .trim(),
    body('originalText')
      .optional()
      .isLength({ max: 50000 })
      .withMessage('原文内容长度不能超过50000个字符')
      .trim(),
    body('commentText')
      .optional()
      .isLength({ max: 50000 })
      .withMessage('评论内容长度不能超过50000个字符')
      .trim(),
    body('analysisType')
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage('分析类型长度必须在1-50个字符之间')
      .trim(),
    body('importanceLevel')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('重要程度必须是1-5之间的整数')
      .toInt(),
    body('tags')
      .optional()
      .isArray()
      .withMessage('标签必须是数组格式'),
    body('tags.*')
      .optional()
      .isString()
      .withMessage('标签必须是字符串')
      .isLength({ min: 1, max: 50 })
      .withMessage('标签长度必须在1-50个字符之间')
      .trim(),
    body('pageLocation')
      .optional()
      .isLength({ min: 1, max: 100 })
      .withMessage('页码位置长度必须在1-100个字符之间')
      .trim(),
    // 自定义验证：原文和评论至少有一个不为空
    body().custom((value) => {
      const { originalText, commentText } = value
      if (!originalText?.trim() && !commentText?.trim()) {
        throw new Error('原文内容和评论内容至少需要填写一个')
      }
      return true
    })
  ]),
  RecordController.createRecord
)

// 更新分析记录
router.put('/:id', 
  validateId('id'),
  validate([
    body('title')
      .optional()
      .isLength({ min: 1, max: 200 })
      .withMessage('记录标题长度必须在1-200个字符之间')
      .trim(),
    body('chapterInfo')
      .optional()
      .isLength({ min: 1, max: 100 })
      .withMessage('章节信息长度必须在1-100个字符之间')
      .trim(),
    body('originalText')
      .optional()
      .isLength({ max: 50000 })
      .withMessage('原文内容长度不能超过50000个字符')
      .trim(),
    body('commentText')
      .optional()
      .isLength({ max: 50000 })
      .withMessage('评论内容长度不能超过50000个字符')
      .trim(),
    body('analysisType')
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage('分析类型长度必须在1-50个字符之间')
      .trim(),
    body('importanceLevel')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('重要程度必须是1-5之间的整数')
      .toInt(),
    body('tags')
      .optional()
      .isArray()
      .withMessage('标签必须是数组格式'),
    body('tags.*')
      .optional()
      .isString()
      .withMessage('标签必须是字符串')
      .isLength({ min: 1, max: 50 })
      .withMessage('标签长度必须在1-50个字符之间')
      .trim(),
    body('pageLocation')
      .optional()
      .isLength({ min: 1, max: 100 })
      .withMessage('页码位置长度必须在1-100个字符之间')
      .trim()
  ]),
  RecordController.updateRecord
)

// 删除分析记录
router.delete('/:id', 
  validateId('id'),
  RecordController.deleteRecord
)

// 批量删除分析记录
router.delete('/batch', validate([
  body('recordIds')
    .isArray({ min: 1 })
    .withMessage('记录ID列表不能为空'),
  body('recordIds.*')
    .isInt({ min: 1 })
    .withMessage('记录ID必须是正整数')
    .toInt()
]), RecordController.batchDeleteRecords)

// 批量更新分析记录
router.put('/batch', validate([
  body('recordIds')
    .isArray({ min: 1 })
    .withMessage('记录ID列表不能为空'),
  body('recordIds.*')
    .isInt({ min: 1 })
    .withMessage('记录ID必须是正整数')
    .toInt(),
  body('updates')
    .isObject()
    .withMessage('更新数据必须是对象'),
  body('updates.analysisType')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('分析类型长度必须在1-50个字符之间'),
  body('updates.importanceLevel')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('重要程度必须是1-5之间的整数'),
  body('updates.tags')
    .optional()
    .isArray()
    .withMessage('标签必须是数组格式')
]), RecordController.batchUpdateRecords)

export default router
