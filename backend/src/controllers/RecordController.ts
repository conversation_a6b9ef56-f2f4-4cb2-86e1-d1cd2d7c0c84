import { Request, Response } from 'express'
import { AppDataSource } from '@/config/database'
import { AnalysisRecord } from '@/entities/AnalysisRecord'
import { Novel } from '@/entities/Novel'
import { successResponse, paginationResponse, ErrorResponses } from '@/utils/response'

export class RecordController {
  // 获取所有分析记录列表（支持跨小说查询）
  static async getAllRecords(req: Request, res: Response): Promise<void> {
    try {
      const {
        search,
        novelId,
        analysisType,
        importanceLevel,
        tags,
        startDate,
        endDate,
        sort = 'createdAt',
        order = 'desc'
      } = req.query

      const { page, limit, offset } = req.pagination!

      const recordRepository = AppDataSource.getRepository(AnalysisRecord)
      const queryBuilder = recordRepository
        .createQueryBuilder('record')
        .leftJoinAndSelect('record.novel', 'novel')
        .leftJoinAndSelect('novel.category', 'category')
        .where('record.isDeleted = 0')

      // 小说筛选
      if (novelId) {
        queryBuilder.andWhere('record.novelId = :novelId', { novelId })
      }

      // 关键词搜索
      if (search) {
        queryBuilder.andWhere(
          '(record.title LIKE :search OR record.originalText LIKE :search OR record.commentText LIKE :search OR novel.title LIKE :search)',
          { search: `%${search}%` }
        )
      }

      // 分析类型筛选
      if (analysisType) {
        queryBuilder.andWhere('record.analysisType = :analysisType', { analysisType })
      }

      // 重要程度筛选
      if (importanceLevel) {
        queryBuilder.andWhere('record.importanceLevel = :importanceLevel', { importanceLevel })
      }

      // 标签筛选
      if (tags) {
        const tagArray = Array.isArray(tags) ? tags : [tags]
        const tagConditions = tagArray.map((_, index) => `record.tags LIKE :tag${index}`).join(' OR ')
        const tagParams = tagArray.reduce((params, tag, index) => {
          params[`tag${index}`] = `%"${tag}"%`
          return params
        }, {} as any)

        queryBuilder.andWhere(`(${tagConditions})`, tagParams)
      }

      // 日期范围筛选
      if (startDate) {
        queryBuilder.andWhere('record.createdAt >= :startDate', { startDate })
      }
      if (endDate) {
        queryBuilder.andWhere('record.createdAt <= :endDate', { endDate })
      }

      // 排序
      const orderDirection = order === 'desc' ? 'DESC' : 'ASC'
      queryBuilder.orderBy(`record.${sort}`, orderDirection)

      // 分页
      queryBuilder.skip(offset).take(limit)

      const [records, total] = await queryBuilder.getManyAndCount()

      // 处理标签
      const recordsWithTags = records.map(record => ({
        ...record,
        tags: record.getTagsArray()
      }))

      paginationResponse(res, recordsWithTags, total, page, limit, '获取分析记录列表成功')
    } catch (error) {
      console.error('获取分析记录列表失败:', error)
      ErrorResponses.internalError(res, '获取分析记录列表失败')
    }
  }

  // 获取指定小说的分析记录列表
  static async getRecords(req: Request, res: Response): Promise<void> {
    try {
      const novelId = parseInt(req.params.novelId)
      const {
        search,
        analysisType,
        importanceLevel,
        sort = 'createdAt',
        order = 'desc'
      } = req.query
      
      const { page, limit, offset } = req.pagination!
      
      const recordRepository = AppDataSource.getRepository(AnalysisRecord)
      const queryBuilder = recordRepository
        .createQueryBuilder('record')
        .leftJoinAndSelect('record.novel', 'novel')
        .where('record.novelId = :novelId AND record.isDeleted = 0', { novelId })

      // 搜索
      if (search) {
        queryBuilder.andWhere(
          '(record.title LIKE :search OR record.originalText LIKE :search OR record.commentText LIKE :search)',
          { search: `%${search}%` }
        )
      }

      // 分析类型筛选
      if (analysisType) {
        queryBuilder.andWhere('record.analysisType = :analysisType', { analysisType })
      }

      // 重要程度筛选
      if (importanceLevel) {
        queryBuilder.andWhere('record.importanceLevel = :importanceLevel', { importanceLevel })
      }

      // 排序
      const orderDirection = order === 'desc' ? 'DESC' : 'ASC'
      queryBuilder.orderBy(`record.${sort}`, orderDirection)

      // 分页
      queryBuilder.skip(offset).take(limit)

      const [records, total] = await queryBuilder.getManyAndCount()

      // 处理标签
      const recordsWithTags = records.map(record => ({
        ...record,
        tags: record.getTagsArray()
      }))

      paginationResponse(res, recordsWithTags, total, page, limit, '获取分析记录列表成功')
    } catch (error) {
      console.error('获取分析记录列表失败:', error)
      ErrorResponses.internalError(res, '获取分析记录列表失败')
    }
  }

  // 获取分析记录详情
  static async getRecordById(req: Request, res: Response): Promise<void> {
    try {
      const recordId = parseInt(req.params.id)
      
      const recordRepository = AppDataSource.getRepository(AnalysisRecord)
      const record = await recordRepository
        .createQueryBuilder('record')
        .leftJoinAndSelect('record.novel', 'novel')
        .leftJoinAndSelect('novel.category', 'category')
        .where('record.id = :id AND record.isDeleted = 0', { id: recordId })
        .getOne()

      if (!record) {
        ErrorResponses.notFound(res, '分析记录不存在')
        return
      }

      // 增加查看次数
      await recordRepository.update(recordId, {
        viewCount: record.viewCount + 1
      })

      const recordWithTags = {
        ...record,
        tags: record.getTagsArray(),
        viewCount: record.viewCount + 1
      }

      successResponse(res, recordWithTags, '获取分析记录详情成功')
    } catch (error) {
      console.error('获取分析记录详情失败:', error)
      ErrorResponses.internalError(res, '获取分析记录详情失败')
    }
  }

  // 创建分析记录
  static async createRecord(req: Request, res: Response): Promise<void> {
    try {
      const novelId = parseInt(req.params.novelId)
      const {
        title,
        chapterInfo,
        originalText,
        commentText,
        analysisType,
        importanceLevel = 3,
        tags = [],
        pageLocation
      } = req.body
      
      const recordRepository = AppDataSource.getRepository(AnalysisRecord)
      const novelRepository = AppDataSource.getRepository(Novel)
      
      // 检查小说是否存在
      const novel = await novelRepository.findOne({
        where: { id: novelId, isDeleted: 0 }
      })

      if (!novel) {
        ErrorResponses.badRequest(res, '指定的小说不存在')
        return
      }

      // 验证内容不为空
      if (!originalText?.trim() && !commentText?.trim()) {
        ErrorResponses.badRequest(res, '原文内容和评论内容至少需要填写一个')
        return
      }

      // 创建分析记录
      const record = recordRepository.create({
        novelId,
        title,
        chapterInfo,
        originalText,
        commentText,
        analysisType,
        importanceLevel,
        pageLocation,
        isDeleted: 0
      })

      // 设置标签
      record.setTagsArray(tags)

      // 计算字数
      record.wordCount = record.calculateWordCount()

      const savedRecord = await recordRepository.save(record)

      // 获取完整信息返回
      const recordWithNovel = await recordRepository
        .createQueryBuilder('record')
        .leftJoinAndSelect('record.novel', 'novel')
        .leftJoinAndSelect('novel.category', 'category')
        .where('record.id = :id', { id: savedRecord.id })
        .getOne()

      successResponse(res, {
        ...recordWithNovel,
        tags: recordWithNovel!.getTagsArray()
      }, '创建分析记录成功', 201)
    } catch (error) {
      console.error('创建分析记录失败:', error)
      ErrorResponses.internalError(res, '创建分析记录失败')
    }
  }

  // 更新分析记录
  static async updateRecord(req: Request, res: Response): Promise<void> {
    try {
      const recordId = parseInt(req.params.id)
      const {
        title,
        chapterInfo,
        originalText,
        commentText,
        analysisType,
        importanceLevel,
        tags,
        pageLocation
      } = req.body
      
      const recordRepository = AppDataSource.getRepository(AnalysisRecord)
      
      // 检查记录是否存在
      const record = await recordRepository.findOne({
        where: { id: recordId, isDeleted: 0 }
      })

      if (!record) {
        ErrorResponses.notFound(res, '分析记录不存在')
        return
      }

      // 验证内容不为空
      const newOriginalText = originalText !== undefined ? originalText : record.originalText
      const newCommentText = commentText !== undefined ? commentText : record.commentText
      
      if (!newOriginalText?.trim() && !newCommentText?.trim()) {
        ErrorResponses.badRequest(res, '原文内容和评论内容至少需要填写一个')
        return
      }

      // 更新记录
      const updateData: Partial<AnalysisRecord> = {}
      if (title !== undefined) updateData.title = title
      if (chapterInfo !== undefined) updateData.chapterInfo = chapterInfo
      if (originalText !== undefined) updateData.originalText = originalText
      if (commentText !== undefined) updateData.commentText = commentText
      if (analysisType !== undefined) updateData.analysisType = analysisType
      if (importanceLevel !== undefined) updateData.importanceLevel = importanceLevel
      if (pageLocation !== undefined) updateData.pageLocation = pageLocation

      // 处理标签
      if (tags !== undefined) {
        updateData.tags = JSON.stringify(tags)
      }

      // 重新计算字数
      if (originalText !== undefined || commentText !== undefined) {
        const originalLength = newOriginalText?.length || 0
        const commentLength = newCommentText?.length || 0
        updateData.wordCount = originalLength + commentLength
      }

      await recordRepository.update(recordId, updateData)

      // 获取更新后的记录
      const updatedRecord = await recordRepository
        .createQueryBuilder('record')
        .leftJoinAndSelect('record.novel', 'novel')
        .leftJoinAndSelect('novel.category', 'category')
        .where('record.id = :id', { id: recordId })
        .getOne()

      successResponse(res, {
        ...updatedRecord,
        tags: updatedRecord!.getTagsArray()
      }, '更新分析记录成功')
    } catch (error) {
      console.error('更新分析记录失败:', error)
      ErrorResponses.internalError(res, '更新分析记录失败')
    }
  }

  // 删除分析记录
  static async deleteRecord(req: Request, res: Response): Promise<void> {
    try {
      const recordId = parseInt(req.params.id)
      
      const recordRepository = AppDataSource.getRepository(AnalysisRecord)
      
      // 检查记录是否存在
      const record = await recordRepository.findOne({
        where: { id: recordId, isDeleted: 0 }
      })

      if (!record) {
        ErrorResponses.notFound(res, '分析记录不存在')
        return
      }

      // 软删除记录
      await recordRepository.update(recordId, { isDeleted: 1 })

      successResponse(res, null, '删除分析记录成功')
    } catch (error) {
      console.error('删除分析记录失败:', error)
      ErrorResponses.internalError(res, '删除分析记录失败')
    }
  }

  // 批量删除分析记录
  static async batchDeleteRecords(req: Request, res: Response): Promise<void> {
    try {
      const { recordIds } = req.body

      if (!Array.isArray(recordIds) || recordIds.length === 0) {
        ErrorResponses.badRequest(res, '请提供要删除的记录ID列表')
        return
      }

      const recordRepository = AppDataSource.getRepository(AnalysisRecord)

      // 批量软删除
      await recordRepository.update(
        { id: recordIds as any, isDeleted: 0 },
        { isDeleted: 1 }
      )

      successResponse(res, null, `成功删除 ${recordIds.length} 条分析记录`)
    } catch (error) {
      console.error('批量删除分析记录失败:', error)
      ErrorResponses.internalError(res, '批量删除分析记录失败')
    }
  }

  // 批量更新分析记录
  static async batchUpdateRecords(req: Request, res: Response): Promise<void> {
    try {
      const { recordIds, updates } = req.body

      if (!Array.isArray(recordIds) || recordIds.length === 0) {
        ErrorResponses.badRequest(res, '请提供要更新的记录ID列表')
        return
      }

      if (!updates || typeof updates !== 'object') {
        ErrorResponses.badRequest(res, '请提供更新数据')
        return
      }

      const recordRepository = AppDataSource.getRepository(AnalysisRecord)

      // 验证更新字段
      const allowedFields = ['analysisType', 'importanceLevel', 'tags']
      const updateData: any = {}

      for (const field of allowedFields) {
        if (updates[field] !== undefined) {
          updateData[field] = updates[field]
        }
      }

      if (Object.keys(updateData).length === 0) {
        ErrorResponses.badRequest(res, '没有有效的更新字段')
        return
      }

      // 处理标签字段
      if (updateData.tags) {
        updateData.tags = JSON.stringify(updateData.tags)
      }

      // 批量更新
      await recordRepository.update(
        { id: recordIds as any, isDeleted: 0 },
        updateData
      )

      successResponse(res, null, `成功更新 ${recordIds.length} 条分析记录`)
    } catch (error) {
      console.error('批量更新分析记录失败:', error)
      ErrorResponses.internalError(res, '批量更新分析记录失败')
    }
  }

  // 获取记录统计信息
  static async getRecordStatistics(req: Request, res: Response): Promise<void> {
    try {
      const { novelId } = req.query

      const recordRepository = AppDataSource.getRepository(AnalysisRecord)
      const queryBuilder = recordRepository
        .createQueryBuilder('record')
        .where('record.isDeleted = 0')

      if (novelId) {
        queryBuilder.andWhere('record.novelId = :novelId', { novelId })
      }

      // 总记录数
      const totalRecords = await queryBuilder.getCount()

      // 按分析类型统计
      const typeStats = await recordRepository
        .createQueryBuilder('record')
        .select('record.analysisType', 'type')
        .addSelect('COUNT(*)', 'count')
        .where('record.isDeleted = 0')
        .andWhere(novelId ? 'record.novelId = :novelId' : '1=1', novelId ? { novelId } : {})
        .groupBy('record.analysisType')
        .getRawMany()

      // 按重要程度统计
      const importanceStats = await recordRepository
        .createQueryBuilder('record')
        .select('record.importanceLevel', 'level')
        .addSelect('COUNT(*)', 'count')
        .where('record.isDeleted = 0')
        .andWhere(novelId ? 'record.novelId = :novelId' : '1=1', novelId ? { novelId } : {})
        .groupBy('record.importanceLevel')
        .orderBy('record.importanceLevel', 'ASC')
        .getRawMany()

      // 最近7天的记录数量
      const recentStats = await recordRepository
        .createQueryBuilder('record')
        .select('DATE(record.createdAt)', 'date')
        .addSelect('COUNT(*)', 'count')
        .where('record.isDeleted = 0')
        .andWhere('record.createdAt >= DATE("now", "-7 days")')
        .andWhere(novelId ? 'record.novelId = :novelId' : '1=1', novelId ? { novelId } : {})
        .groupBy('DATE(record.createdAt)')
        .orderBy('DATE(record.createdAt)', 'ASC')
        .getRawMany()

      successResponse(res, {
        totalRecords,
        typeStats,
        importanceStats,
        recentStats
      }, '获取记录统计成功')
    } catch (error) {
      console.error('获取记录统计失败:', error)
      ErrorResponses.internalError(res, '获取记录统计失败')
    }
  }
}
