# HaoWriter 项目待开发功能文档

## 📋 目录

1. [功能清单](#功能清单)
2. [技术规格](#技术规格)
3. [开发指南](#开发指南)
4. [任务分解](#任务分解)
5. [预估工时](#预估工时)
6. [依赖关系](#依赖关系)

---

## 功能清单

### 🔥 高优先级功能

| 功能模块 | 状态 | 优先级 | 预估工时 | 描述 |
|---------|------|--------|----------|------|
| Markdown编辑器集成 | 未开始 | P0 | 16h | 分析记录编辑页面的核心功能 |
| 分析记录管理功能 | 未开始 | P0 | 20h | 记录的CRUD操作和列表展示 |
| 小说详情页面完善 | 未开始 | P1 | 12h | 小说详细信息和记录管理 |
| 高级搜索功能 | 部分完成 | P1 | 14h | 多条件搜索和全文检索 |

### 🚀 中优先级功能

| 功能模块 | 状态 | 优先级 | 预估工时 | 描述 |
|---------|------|--------|----------|------|
| 用户体验优化 | 部分完成 | P2 | 18h | 加载状态、错误处理、动画效果 |
| 数据导入导出 | 未开始 | P2 | 16h | 支持JSON/CSV格式的数据导入导出 |
| 移动端适配优化 | 部分完成 | P2 | 12h | 响应式设计和移动端交互优化 |
| 数据可视化 | 未开始 | P2 | 14h | 统计图表和数据分析展示 |

### 📈 低优先级功能

| 功能模块 | 状态 | 优先级 | 预估工时 | 描述 |
|---------|------|--------|----------|------|
| 系统设置页面 | 未开始 | P3 | 8h | 个性化配置和系统参数设置 |
| 用户管理功能 | 未开始 | P3 | 10h | 多用户支持和权限管理 |
| PWA支持 | 未开始 | P3 | 6h | 离线访问和应用安装 |
| 主题切换功能 | 未开始 | P3 | 4h | 深色模式和主题定制 |

---

## 技术规格

### 1. Markdown编辑器集成

#### 技术选型
- **推荐方案**: `@vueup/vue-quill` 或 `@toast-ui/editor`
- **备选方案**: `mavon-editor` 或 `v-md-editor`

#### 技术要求
```typescript
// 编辑器功能要求
interface MarkdownEditorProps {
  modelValue: string
  placeholder?: string
  height?: string | number
  toolbar?: ToolbarConfig
  preview?: boolean
  upload?: UploadConfig
}

// 工具栏配置
interface ToolbarConfig {
  bold: boolean
  italic: boolean
  heading: boolean
  code: boolean
  quote: boolean
  list: boolean
  link: boolean
  image: boolean
  table: boolean
}
```

#### 依赖包
```json
{
  "@toast-ui/editor": "^3.2.2",
  "@toast-ui/vue-editor": "^3.2.2",
  "highlight.js": "^11.9.0"
}
```

### 2. 分析记录管理功能

#### 数据模型扩展
```typescript
// 分析记录类型枚举
enum AnalysisType {
  CHARACTER = 'character',      // 人物分析
  PLOT = 'plot',               // 情节分析
  THEME = 'theme',             // 主题分析
  WRITING = 'writing',         // 写作技巧
  WORLDVIEW = 'worldview',     // 世界观设定
  OTHER = 'other'              // 其他
}

// 重要程度枚举
enum ImportanceLevel {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4
}
```

#### API接口设计
```typescript
// 分析记录API接口
interface RecordAPI {
  // 获取记录列表
  getRecords(params: RecordQueryParams): Promise<PaginatedResponse<AnalysisRecord>>
  
  // 获取记录详情
  getRecord(id: number): Promise<ApiResponse<AnalysisRecord>>
  
  // 创建记录
  createRecord(data: RecordForm): Promise<ApiResponse<AnalysisRecord>>
  
  // 更新记录
  updateRecord(id: number, data: RecordForm): Promise<ApiResponse<AnalysisRecord>>
  
  // 删除记录
  deleteRecord(id: number): Promise<ApiResponse<void>>
  
  // 批量操作
  batchDelete(ids: number[]): Promise<ApiResponse<void>>
  batchUpdate(updates: BatchUpdateItem[]): Promise<ApiResponse<void>>
}
```

### 3. 高级搜索功能

#### 搜索参数接口
```typescript
interface SearchParams {
  keyword?: string              // 关键词搜索
  type?: 'novels' | 'records'   // 搜索类型
  categoryId?: number           // 分类筛选
  author?: string               // 作者筛选
  readingStatus?: ReadingStatus // 阅读状态
  analysisType?: AnalysisType   // 分析类型
  importanceLevel?: ImportanceLevel // 重要程度
  dateRange?: [string, string]  // 日期范围
  tags?: string[]               // 标签筛选
  sortBy?: string               // 排序字段
  sortOrder?: 'asc' | 'desc'    // 排序方向
}
```

#### 全文搜索实现
```sql
-- FTS5全文搜索查询示例
SELECT * FROM novels_fts 
WHERE novels_fts MATCH '关键词' 
ORDER BY rank;

SELECT * FROM records_fts 
WHERE records_fts MATCH '分析内容' 
ORDER BY rank;
```

---

## 开发指南

### 1. Markdown编辑器集成

#### 文件结构
```
frontend/src/
├── components/
│   └── editor/
│       ├── MarkdownEditor.vue          # 编辑器组件
│       ├── EditorToolbar.vue           # 工具栏组件
│       └── EditorPreview.vue           # 预览组件
├── pages/
│   └── records/
│       ├── RecordEditPage.vue          # 记录编辑页面
│       └── RecordDetailPage.vue        # 记录详情页面
└── utils/
    └── markdown.ts                     # Markdown工具函数
```

#### 核心组件实现

**MarkdownEditor.vue**
```vue
<template>
  <div class="markdown-editor">
    <div class="editor-toolbar">
      <EditorToolbar 
        @command="handleToolbarCommand"
        :disabled="loading"
      />
    </div>
    
    <div class="editor-content">
      <div class="editor-input">
        <ToastUIEditor
          ref="editorRef"
          :initial-value="modelValue"
          :options="editorOptions"
          @change="handleChange"
        />
      </div>
      
      <div v-if="showPreview" class="editor-preview">
        <EditorPreview :content="modelValue" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ToastUIEditor } from '@toast-ui/vue-editor'
import EditorToolbar from './EditorToolbar.vue'
import EditorPreview from './EditorPreview.vue'

interface Props {
  modelValue: string
  height?: string | number
  showPreview?: boolean
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  showPreview: true,
  placeholder: '请输入分析内容...'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const editorRef = ref()
const loading = ref(false)

const editorOptions = computed(() => ({
  height: props.height,
  initialEditType: 'markdown',
  previewStyle: 'vertical',
  placeholder: props.placeholder,
  toolbarItems: [
    ['heading', 'bold', 'italic'],
    ['hr', 'quote'],
    ['ul', 'ol', 'task'],
    ['table', 'link', 'image'],
    ['code', 'codeblock']
  ]
}))

const handleChange = () => {
  const content = editorRef.value?.getMarkdown() || ''
  emit('update:modelValue', content)
}

const handleToolbarCommand = (command: string) => {
  // 处理自定义工具栏命令
  switch (command) {
    case 'save':
      handleSave()
      break
    case 'preview':
      togglePreview()
      break
  }
}
</script>
```

#### 安装和配置
```bash
# 安装依赖
cd frontend
npm install @toast-ui/editor @toast-ui/vue-editor highlight.js

# 在main.ts中引入样式
import '@toast-ui/editor/dist/toastui-editor.css'
import '@toast-ui/editor/dist/theme/toastui-editor-dark.css'
```

### 2. 分析记录管理功能

#### 后端API实现

**RecordController.ts 扩展**
```typescript
// backend/src/controllers/RecordController.ts
export class RecordController {
  // 获取记录列表（带筛选和分页）
  static async getRecords(req: Request, res: Response): Promise<void> {
    try {
      const {
        novelId,
        analysisType,
        importanceLevel,
        keyword,
        startDate,
        endDate
      } = req.query
      
      const page = parseInt(req.query.page as string) || 1
      const limit = parseInt(req.query.size as string) || 20
      const offset = (page - 1) * limit

      const recordRepository = AppDataSource.getRepository(AnalysisRecord)
      const queryBuilder = recordRepository
        .createQueryBuilder('record')
        .leftJoinAndSelect('record.novel', 'novel')
        .where('record.isDeleted = 0')

      // 添加筛选条件
      if (novelId) {
        queryBuilder.andWhere('record.novelId = :novelId', { novelId })
      }
      
      if (analysisType) {
        queryBuilder.andWhere('record.analysisType = :analysisType', { analysisType })
      }
      
      if (importanceLevel) {
        queryBuilder.andWhere('record.importanceLevel = :importanceLevel', { importanceLevel })
      }
      
      if (keyword) {
        queryBuilder.andWhere(
          '(record.title LIKE :keyword OR record.originalText LIKE :keyword OR record.commentText LIKE :keyword)',
          { keyword: `%${keyword}%` }
        )
      }
      
      if (startDate && endDate) {
        queryBuilder.andWhere('record.createdAt BETWEEN :startDate AND :endDate', {
          startDate,
          endDate
        })
      }

      // 分页和排序
      const [records, total] = await queryBuilder
        .orderBy('record.createdAt', 'DESC')
        .skip(offset)
        .take(limit)
        .getManyAndCount()

      paginationResponse(res, records, total, page, limit, '获取记录列表成功')
    } catch (error) {
      console.error('获取记录列表失败:', error)
      ErrorResponses.internalError(res, '获取记录列表失败')
    }
  }

  // 批量删除记录
  static async batchDeleteRecords(req: Request, res: Response): Promise<void> {
    try {
      const { ids } = req.body
      
      if (!Array.isArray(ids) || ids.length === 0) {
        return ErrorResponses.badRequest(res, '请提供要删除的记录ID列表')
      }

      const recordRepository = AppDataSource.getRepository(AnalysisRecord)
      
      await recordRepository
        .createQueryBuilder()
        .update(AnalysisRecord)
        .set({ isDeleted: 1, deletedAt: new Date() })
        .where('id IN (:...ids)', { ids })
        .execute()

      SuccessResponses.success(res, null, '批量删除成功')
    } catch (error) {
      console.error('批量删除记录失败:', error)
      ErrorResponses.internalError(res, '批量删除记录失败')
    }
  }
}
```

#### 前端页面实现

**RecordsPage.vue**
```vue
<template>
  <div class="records-page">
    <div class="page-header">
      <div class="header-content">
        <div>
          <h1 class="page-title">分析记录</h1>
          <p class="page-subtitle">管理和查看小说分析记录</p>
        </div>
        <el-button type="primary" :icon="Plus" @click="createRecord">
          新建记录
        </el-button>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <el-card class="filter-card">
      <div class="filter-content">
        <div class="filter-row">
          <el-select v-model="filters.novelId" placeholder="选择小说" clearable>
            <el-option
              v-for="novel in novelsOptions"
              :key="novel.id"
              :label="novel.title"
              :value="novel.id"
            />
          </el-select>

          <el-select v-model="filters.analysisType" placeholder="分析类型" clearable>
            <el-option label="人物分析" value="character" />
            <el-option label="情节分析" value="plot" />
            <el-option label="主题分析" value="theme" />
            <el-option label="写作技巧" value="writing" />
            <el-option label="世界观设定" value="worldview" />
            <el-option label="其他" value="other" />
          </el-select>

          <el-select v-model="filters.importanceLevel" placeholder="重要程度" clearable>
            <el-option label="低" :value="1" />
            <el-option label="中" :value="2" />
            <el-option label="高" :value="3" />
            <el-option label="关键" :value="4" />
          </el-select>

          <el-input
            v-model="filters.keyword"
            placeholder="搜索标题或内容"
            :prefix-icon="Search"
            clearable
            @keyup.enter="loadRecords"
          />

          <el-button :icon="Search" @click="loadRecords">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </div>
    </el-card>

    <!-- 记录列表 -->
    <el-card>
      <div class="list-toolbar">
        <div class="toolbar-left">
          <el-checkbox
            v-model="selectAll"
            :indeterminate="isIndeterminate"
            @change="handleSelectAll"
          >
            全选
          </el-checkbox>
          <el-button
            v-if="selectedRecords.length > 0"
            type="danger"
            :icon="Delete"
            @click="batchDelete"
          >
            批量删除 ({{ selectedRecords.length }})
          </el-button>
        </div>
        
        <div class="toolbar-right">
          <el-button :icon="Download" @click="exportRecords">导出</el-button>
        </div>
      </div>

      <div v-loading="loading" class="records-list">
        <div v-if="records.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无分析记录">
            <el-button type="primary" :icon="Plus" @click="createRecord">
              创建第一条记录
            </el-button>
          </el-empty>
        </div>

        <div v-else class="records-grid">
          <div
            v-for="record in records"
            :key="record.id"
            class="record-card"
            :class="{ selected: selectedRecords.includes(record.id) }"
          >
            <div class="record-header">
              <el-checkbox
                :model-value="selectedRecords.includes(record.id)"
                @change="toggleSelect(record.id)"
              />
              
              <div class="record-meta">
                <el-tag :type="getAnalysisTypeColor(record.analysisType)" size="small">
                  {{ getAnalysisTypeLabel(record.analysisType) }}
                </el-tag>
                <el-rate
                  :model-value="record.importanceLevel"
                  :max="4"
                  disabled
                  size="small"
                />
              </div>
            </div>

            <div class="record-content" @click="viewRecord(record)">
              <h3 class="record-title">{{ record.title }}</h3>
              <p class="record-novel">{{ record.novel?.title }}</p>
              <p class="record-excerpt">{{ getExcerpt(record.originalText) }}</p>
              <div class="record-footer">
                <span class="record-date">{{ formatDate(record.createdAt) }}</span>
                <span class="record-words">{{ getWordCount(record) }} 字</span>
              </div>
            </div>

            <div class="record-actions">
              <el-button size="small" :icon="View" @click="viewRecord(record)">
                查看
              </el-button>
              <el-button size="small" :icon="Edit" @click="editRecord(record)">
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                :icon="Delete"
                @click="deleteRecord(record)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="records.length > 0" class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadRecords"
            @current-change="loadRecords"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Delete, Download, View, Edit } from '@element-plus/icons-vue'
import { useRecordsStore } from '@/stores/records'
import { useNovelsStore } from '@/stores/novels'
import type { AnalysisRecord, AnalysisType } from '@/types/models'

const router = useRouter()
const recordsStore = useRecordsStore()
const novelsStore = useNovelsStore()

// 响应式数据
const loading = ref(false)
const selectedRecords = ref<number[]>([])

// 筛选条件
const filters = reactive({
  novelId: undefined as number | undefined,
  analysisType: '',
  importanceLevel: undefined as number | undefined,
  keyword: ''
})

// 计算属性
const records = computed(() => recordsStore.records)
const pagination = computed(() => recordsStore.pagination)
const novelsOptions = computed(() => novelsStore.novelsOptions)

const selectAll = computed({
  get: () => selectedRecords.value.length === records.value.length && records.value.length > 0,
  set: (value: boolean) => {
    selectedRecords.value = value ? records.value.map(r => r.id) : []
  }
})

const isIndeterminate = computed(() => 
  selectedRecords.value.length > 0 && selectedRecords.value.length < records.value.length
)

// 方法实现
const loadRecords = async () => {
  loading.value = true
  try {
    await recordsStore.fetchRecords({
      page: pagination.value.current,
      size: pagination.value.size,
      ...filters
    })
  } catch (error) {
    ElMessage.error('加载记录列表失败')
  } finally {
    loading.value = false
  }
}

const createRecord = () => {
  router.push('/records/new')
}

const viewRecord = (record: AnalysisRecord) => {
  router.push(`/records/${record.id}`)
}

const editRecord = (record: AnalysisRecord) => {
  router.push(`/records/${record.id}/edit`)
}

// 其他方法...
</script>
```

### 3. 高级搜索功能

#### 搜索页面实现

**SearchPage.vue**
```vue
<template>
  <div class="search-page">
    <div class="search-header">
      <h1 class="page-title">高级搜索</h1>
      <p class="page-subtitle">搜索小说和分析记录</p>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-form-card">
      <el-form :model="searchForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关键词">
              <el-input
                v-model="searchForm.keyword"
                placeholder="输入搜索关键词"
                :prefix-icon="Search"
                @keyup.enter="performSearch"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="搜索类型">
              <el-radio-group v-model="searchForm.type">
                <el-radio label="all">全部</el-radio>
                <el-radio label="novels">小说</el-radio>
                <el-radio label="records">记录</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分类">
              <el-select v-model="searchForm.categoryId" placeholder="选择分类" clearable>
                <el-option
                  v-for="category in categoriesOptions"
                  :key="category.value"
                  :label="category.label"
                  :value="category.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="作者">
              <el-input v-model="searchForm.author" placeholder="作者姓名" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="标签">
              <el-select
                v-model="searchForm.tags"
                multiple
                placeholder="选择标签"
                clearable
              >
                <el-option
                  v-for="tag in tagsOptions"
                  :key="tag"
                  :label="tag"
                  :value="tag"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="排序方式">
              <el-select v-model="searchForm.sortBy">
                <el-option label="相关度" value="relevance" />
                <el-option label="创建时间" value="createdAt" />
                <el-option label="更新时间" value="updatedAt" />
                <el-option label="标题" value="title" />
              </el-select>
              <el-radio-group v-model="searchForm.sortOrder" style="margin-left: 10px;">
                <el-radio label="desc">降序</el-radio>
                <el-radio label="asc">升序</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button type="primary" :icon="Search" @click="performSearch" :loading="searching">
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="saveSearchTemplate">保存搜索模板</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 搜索结果 -->
    <el-card v-if="hasSearched" class="search-results-card">
      <template #header>
        <div class="results-header">
          <span>搜索结果 ({{ searchResults.total }} 条)</span>
          <div class="results-actions">
            <el-button :icon="Download" @click="exportResults">导出结果</el-button>
          </div>
        </div>
      </template>

      <div v-loading="searching" class="search-results">
        <div v-if="searchResults.total === 0" class="no-results">
          <el-empty description="未找到相关内容">
            <el-button type="primary" @click="resetSearch">重新搜索</el-button>
          </el-empty>
        </div>

        <div v-else class="results-list">
          <!-- 小说结果 -->
          <div v-if="searchResults.novels.length > 0" class="result-section">
            <h3 class="section-title">小说 ({{ searchResults.novels.length }})</h3>
            <div class="novels-results">
              <div
                v-for="novel in searchResults.novels"
                :key="`novel-${novel.id}`"
                class="result-item novel-item"
                @click="viewNovel(novel)"
              >
                <div class="item-header">
                  <h4 class="item-title" v-html="highlightKeyword(novel.title)"></h4>
                  <el-tag type="info" size="small">小说</el-tag>
                </div>
                <p class="item-author">作者：{{ novel.author }}</p>
                <p class="item-description" v-html="highlightKeyword(novel.description)"></p>
                <div class="item-meta">
                  <span class="meta-item">分类：{{ novel.category?.name }}</span>
                  <span class="meta-item">状态：{{ getReadingStatusLabel(novel.readingStatus) }}</span>
                  <span class="meta-item">{{ formatDate(novel.createdAt) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 记录结果 -->
          <div v-if="searchResults.records.length > 0" class="result-section">
            <h3 class="section-title">分析记录 ({{ searchResults.records.length }})</h3>
            <div class="records-results">
              <div
                v-for="record in searchResults.records"
                :key="`record-${record.id}`"
                class="result-item record-item"
                @click="viewRecord(record)"
              >
                <div class="item-header">
                  <h4 class="item-title" v-html="highlightKeyword(record.title)"></h4>
                  <div class="item-tags">
                    <el-tag type="success" size="small">记录</el-tag>
                    <el-tag :type="getAnalysisTypeColor(record.analysisType)" size="small">
                      {{ getAnalysisTypeLabel(record.analysisType) }}
                    </el-tag>
                  </div>
                </div>
                <p class="item-novel">小说：{{ record.novel?.title }}</p>
                <p class="item-content" v-html="highlightKeyword(getExcerpt(record.originalText))"></p>
                <div class="item-meta">
                  <span class="meta-item">重要度：{{ getImportanceLevelLabel(record.importanceLevel) }}</span>
                  <span class="meta-item">{{ formatDate(record.createdAt) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="searchResults.total > 0" class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="searchResults.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="performSearch"
            @current-change="performSearch"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, Download } from '@element-plus/icons-vue'
import { useSearchStore } from '@/stores/search'
import { useCategoriesStore } from '@/stores/categories'
import type { SearchParams, SearchResults } from '@/types/models'

const router = useRouter()
const searchStore = useSearchStore()
const categoriesStore = useCategoriesStore()

// 响应式数据
const searching = ref(false)
const hasSearched = ref(false)

// 搜索表单
const searchForm = reactive<SearchParams>({
  keyword: '',
  type: 'all',
  categoryId: undefined,
  author: '',
  tags: [],
  dateRange: undefined,
  sortBy: 'relevance',
  sortOrder: 'desc'
})

// 计算属性
const searchResults = computed(() => searchStore.searchResults)
const pagination = computed(() => searchStore.pagination)
const categoriesOptions = computed(() => categoriesStore.categoriesOptions)
const tagsOptions = computed(() => searchStore.availableTags)

// 方法实现
const performSearch = async () => {
  if (!searchForm.keyword.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  searching.value = true
  hasSearched.value = true
  
  try {
    await searchStore.search({
      ...searchForm,
      page: pagination.value.current,
      size: pagination.value.size
    })
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    searching.value = false
  }
}

const highlightKeyword = (text: string): string => {
  if (!searchForm.keyword || !text) return text
  
  const keyword = searchForm.keyword.trim()
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 其他方法...
</script>
```

---

## 任务分解

### 1. Markdown编辑器集成 (16h)

#### 任务1.1: 编辑器组件开发 (8h)
- [ ] 安装和配置Toast UI Editor
- [ ] 创建MarkdownEditor.vue组件
- [ ] 实现工具栏自定义功能
- [ ] 添加图片上传支持
- [ ] 实现实时预览功能

#### 任务1.2: 编辑页面集成 (6h)
- [ ] 更新RecordEditPage.vue页面
- [ ] 集成编辑器组件
- [ ] 实现自动保存功能
- [ ] 添加快捷键支持

#### 任务1.3: 样式和优化 (2h)
- [ ] 自定义编辑器主题
- [ ] 响应式布局适配
- [ ] 性能优化

### 2. 分析记录管理功能 (20h)

#### 任务2.1: 后端API开发 (8h)
- [ ] 扩展RecordController功能
- [ ] 实现高级筛选查询
- [ ] 添加批量操作接口
- [ ] 实现全文搜索API

#### 任务2.2: 前端页面开发 (10h)
- [ ] 创建RecordsPage.vue页面
- [ ] 实现记录列表展示
- [ ] 添加筛选和搜索功能
- [ ] 实现批量操作功能

#### 任务2.3: 详情页面开发 (2h)
- [ ] 创建RecordDetailPage.vue
- [ ] 实现记录详情展示
- [ ] 添加相关记录推荐

### 3. 小说详情页面完善 (12h)

#### 任务3.1: 页面结构设计 (4h)
- [ ] 设计页面布局
- [ ] 创建NovelDetailPage.vue
- [ ] 实现基础信息展示

#### 任务3.2: 记录管理集成 (6h)
- [ ] 添加记录列表组件
- [ ] 实现记录快速创建
- [ ] 添加记录统计图表

#### 任务3.3: 交互优化 (2h)
- [ ] 添加编辑模式切换
- [ ] 实现快捷操作
- [ ] 优化加载性能

### 4. 高级搜索功能 (14h)

#### 任务4.1: 后端搜索引擎 (6h)
- [ ] 优化FTS5全文搜索
- [ ] 实现多条件组合查询
- [ ] 添加搜索结果排序
- [ ] 实现搜索历史记录

#### 任务4.2: 前端搜索页面 (6h)
- [ ] 创建SearchPage.vue
- [ ] 实现高级搜索表单
- [ ] 添加搜索结果展示
- [ ] 实现搜索模板保存

#### 任务4.3: 搜索优化 (2h)
- [ ] 添加搜索建议功能
- [ ] 实现关键词高亮
- [ ] 优化搜索性能

---

## 预估工时

### 详细工时分解

| 功能模块 | 子任务 | 预估工时 | 难度 | 优先级 |
|---------|--------|----------|------|--------|
| **Markdown编辑器** | | **16h** | 中 | P0 |
| | 编辑器组件开发 | 8h | 中 | P0 |
| | 页面集成 | 6h | 低 | P0 |
| | 样式优化 | 2h | 低 | P0 |
| **分析记录管理** | | **20h** | 高 | P0 |
| | 后端API开发 | 8h | 高 | P0 |
| | 前端页面开发 | 10h | 中 | P0 |
| | 详情页面 | 2h | 低 | P0 |
| **小说详情页面** | | **12h** | 中 | P1 |
| | 页面结构设计 | 4h | 中 | P1 |
| | 记录管理集成 | 6h | 中 | P1 |
| | 交互优化 | 2h | 低 | P1 |
| **高级搜索功能** | | **14h** | 高 | P1 |
| | 后端搜索引擎 | 6h | 高 | P1 |
| | 前端搜索页面 | 6h | 中 | P1 |
| | 搜索优化 | 2h | 中 | P1 |
| **用户体验优化** | | **18h** | 中 | P2 |
| | 加载状态优化 | 6h | 中 | P2 |
| | 错误处理完善 | 4h | 中 | P2 |
| | 动画效果添加 | 4h | 低 | P2 |
| | 响应式优化 | 4h | 中 | P2 |
| **数据导入导出** | | **16h** | 中 | P2 |
| | 导出功能开发 | 8h | 中 | P2 |
| | 导入功能开发 | 8h | 高 | P2 |
| **移动端适配** | | **12h** | 中 | P2 |
| | 响应式布局 | 6h | 中 | P2 |
| | 移动端交互 | 4h | 中 | P2 |
| | 性能优化 | 2h | 中 | P2 |
| **数据可视化** | | **14h** | 中 | P2 |
| | 图表组件集成 | 6h | 中 | P2 |
| | 统计页面开发 | 6h | 中 | P2 |
| | 交互优化 | 2h | 低 | P2 |

### 开发周期规划

**第一阶段 (1-2周)**: 核心功能开发
- Markdown编辑器集成
- 分析记录管理功能

**第二阶段 (3-4周)**: 功能完善
- 小说详情页面完善
- 高级搜索功能实现

**第三阶段 (5-6周)**: 体验优化
- 用户体验优化
- 移动端适配优化

**第四阶段 (7-8周)**: 扩展功能
- 数据导入导出功能
- 数据可视化功能

---

## 依赖关系

### 功能依赖图

```mermaid
graph TD
    A[Markdown编辑器] --> B[分析记录管理]
    B --> C[小说详情页面]
    B --> D[高级搜索功能]
    C --> E[用户体验优化]
    D --> E
    E --> F[数据导入导出]
    E --> G[移动端适配]
    F --> H[数据可视化]
    G --> H
```

### 开发顺序建议

#### 阶段1: 基础功能 (必须按顺序)
1. **Markdown编辑器集成** - 其他功能的基础
2. **分析记录管理功能** - 核心业务功能

#### 阶段2: 核心功能 (可并行开发)
3. **小说详情页面完善** - 依赖记录管理
4. **高级搜索功能** - 依赖记录管理

#### 阶段3: 优化功能 (可并行开发)
5. **用户体验优化** - 可与其他功能并行
6. **移动端适配优化** - 可与其他功能并行

#### 阶段4: 扩展功能 (可并行开发)
7. **数据导入导出功能** - 独立功能
8. **数据可视化功能** - 依赖完整数据

### 技术依赖

#### 前端依赖
```json
{
  "新增依赖": {
    "@toast-ui/editor": "^3.2.2",
    "@toast-ui/vue-editor": "^3.2.2",
    "echarts": "^5.4.3",
    "vue-echarts": "^6.6.1",
    "file-saver": "^2.0.5",
    "xlsx": "^0.18.5"
  }
}
```

#### 后端依赖
```json
{
  "新增依赖": {
    "multer": "^1.4.5-lts.1",
    "csv-parser": "^3.0.0",
    "json2csv": "^6.1.0"
  }
}
```

### 数据库依赖

#### 新增表结构
```sql
-- 搜索历史表
CREATE TABLE search_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  keyword TEXT NOT NULL,
  search_params TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 搜索模板表
CREATE TABLE search_templates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  search_params TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

---

## 开发注意事项

### 1. 代码规范
- 遵循现有的TypeScript和Vue 3代码规范
- 使用Composition API编写组件
- 保持组件的单一职责原则
- 添加适当的类型定义和注释

### 2. 性能考虑
- 大列表使用虚拟滚动
- 图片懒加载
- 组件懒加载
- 合理使用缓存

### 3. 用户体验
- 添加加载状态指示
- 提供友好的错误提示
- 实现操作确认机制
- 保持界面一致性

### 4. 测试要求
- 为新功能编写单元测试
- 进行跨浏览器兼容性测试
- 移动端适配测试
- 性能测试

### 5. 文档维护
- 更新API文档
- 添加组件使用说明
- 维护开发文档
- 更新部署指南

---

这份文档提供了HaoWriter项目后续开发的详细指南，开发者可以根据优先级和依赖关系有序地完成各项功能的开发。建议在开发过程中及时更新文档，确保项目的可维护性。
